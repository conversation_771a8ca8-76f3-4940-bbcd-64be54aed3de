package models

import (
	"fmt"
	"log"
	"utility/logger"

	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var db *gorm.DB

// Model 数据模型
// type Model struct {
// 	ID         int `gorm:"primary_key" json:"id"`
// 	CreatedOn  int `json:"created_on"`
// 	ModifiedOn int `json:"modified_on"`
// 	DeletedOn  int `json:"deleted_on"`
// }

// Setup initializes the database instance
func Setup(username, password, dbserver, dbname string, port int64) {
	// log.Printf("[info] start to setup database connection...")
	var err error
	db, err = gorm.Open(mysql.Open(fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username,
		password,
		dbserver,
		port,
		dbname)), &gorm.Config{})

	if err != nil {
		logger.Log.Errorf("models.Setup err: %v", err)
	}
	// db.Logger.LogMode(true)
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("database set up error:%+v", err)
	}
	// sqlDB.SingularTable(true)
	// db.Callback().Create().Replace("gorm:update_time_stamp", updateTimeStampForCreateCallback)
	// db.Callback().Update().Replace("gorm:update_time_stamp", updateTimeStampForUpdateCallback)
	// db.Callback().Delete().Replace("gorm:delete", deleteCallback)
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Duration(8*3600) * time.Second) //设置了连接可复用的最大时间。
}

// CloseDB closes database connection (unnecessary)
func CloseDB() {
	sqlDB, _ := db.DB()
	defer sqlDB.Close()
}

// // updateTimeStampForCreateCallback will set `CreatedOn`, `ModifiedOn` when creating
// func updateTimeStampForCreateCallback(scope *gorm.Scope) {
// 	if !scope.HasError() {
// 		nowTime := time.Now().Unix()
// 		if createTimeField, ok := scope.FieldByName("CreatedOn"); ok {
// 			if createTimeField.IsBlank {
// 				createTimeField.Set(nowTime)
// 			}
// 		}

// 		if modifyTimeField, ok := scope.FieldByName("ModifiedOn"); ok {
// 			if modifyTimeField.IsBlank {
// 				modifyTimeField.Set(nowTime)
// 			}
// 		}
// 	}
// }

// // updateTimeStampForUpdateCallback will set `ModifiedOn` when updating
// func updateTimeStampForUpdateCallback(scope *gorm.Scope) {
// 	if _, ok := scope.Get("gorm:update_column"); !ok {
// 		scope.SetColumn("ModifiedOn", time.Now().Unix())
// 	}
// }

// // deleteCallback will set `DeletedOn` where deleting
// func deleteCallback(scope *gorm.Scope) {
// 	if !scope.HasError() {
// 		var extraOption string
// 		if str, ok := scope.Get("gorm:delete_option"); ok {
// 			extraOption = fmt.Sprint(str)
// 		}

// 		deletedOnField, hasDeletedOnField := scope.FieldByName("DeletedOn")

// 		if !scope.Search.Unscoped && hasDeletedOnField {
// 			scope.Raw(fmt.Sprintf(
// 				"UPDATE %v SET %v=%v%v%v",
// 				scope.QuotedTableName(),
// 				scope.Quote(deletedOnField.DBName),
// 				scope.AddToVars(time.Now().Unix()),
// 				addExtraSpaceIfExist(scope.CombinedConditionSql()),
// 				addExtraSpaceIfExist(extraOption),
// 			)).Exec()
// 		} else {
// 			scope.Raw(fmt.Sprintf(
// 				"DELETE FROM %v%v%v",
// 				scope.QuotedTableName(),
// 				addExtraSpaceIfExist(scope.CombinedConditionSql()),
// 				addExtraSpaceIfExist(extraOption),
// 			)).Exec()
// 		}
// 	}
// }

// // addExtraSpaceIfExist adds a separator
// func addExtraSpaceIfExist(str string) string {
// 	if str != "" {
// 		return " " + str
// 	}
// 	return ""
// }
