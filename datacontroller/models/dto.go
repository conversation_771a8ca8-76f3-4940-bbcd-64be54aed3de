package models

type TestIdDTO struct {
	Testid string `json:"testid"`
}

type TestIdPidDTO struct {
	Testid string `json:"testid"`
	Pid    string `json:"pid"`
}

type CorpReportDTO struct {
	Rptid     int    `json:"rptid"`
	Pagestyle int    `json:"pagestyle"`
	Outdir    string `json:"outdir"`
}

// MedQueryDTO ... MedQueryDTO
type MedQueryDTO struct {
	Dtstart    string   `json:"dtstart"`
	Dtend      string   `json:"dtend"`
	Testid     []string `json:"testid"`
	Cpme       int      `json:"cpme"`
	Corpid     int      `json:"corpid"`
	Testtype   int      `json:"testtype"`
	Statuslow  int      `json:"statuslow"`
	Statushigh int      `json:"statushigh"`
	Pid        []string `json:"pid"`
	Pname      string   `json:"pname"`
	Isrecheck  int      `json:"isrecheck"`
	Rptnum     string   `json:"rptnum"`
}

// MedinfoPoisionDTO ... MedinfoPoisionDTO
type MedinfoPoisionDTO struct {
	// Utype       string         `json:"type"`    //更新类型
	Medinfo    TjMedexaminfo  `json:"medinfo"` //体检编号
	Hazardinfo []TjHazardinfo `json:"hdinfos"` // 毒害因素列表
	// PoisionTime string         `json:"ptime"`   // 接害时间
}

type KeyDTO struct {
	KeyVal string `json:"key`
}

// KeysDTO ...
type KeysDTO struct {
	KeyIDs []string `json:"keys"`
}

// KeysIntDTO ...
type KeysIntDTO struct {
	KeyIDs []int `json:"keys"`
}

type ExtCodeDTO struct {
	InternalCode string `json:"intcode"` // 本系统编码
	ExternalCode string `json:"extcode"` //外部对接系统编码，CDC
}

// MedinfoCheckitemsDTO ... MedinfoCheckitemsDTO
type MedinfoCheckitemsDTO struct {
	// Utype      string            `json:"type"`    //更新呢类型
	Medinfo    []TjMedexaminfo   `json:"medinfos"` //体检编号
	Hazardinfo []TjCheckiteminfo `json:"ciinfos"`  // 体检项目列表
}

// MedReportDTO ...
type MedReportDTO struct {
	Testids []string `json:"testids"` //体检编号列表
	Status  int      `json:"status"`  //状态
	Rptnum  string   `json:"rptnum"`  //报告编号
}

// PtQueryDTO ... PtQueryDTO
type PtQueryDTO struct {
	Pname   string   `json:"pname"`   //根据名字模糊查找
	Pids    []string `json:"pids"`    //pid列表，支持多个pid查找
	Testids []string `json:"testids"` //体检号
	Idcard  string   `json:"idcard"`  //身份证号
}

// PatientDiseaseDTO ... 体检者疾病信息DTO，用于查找和删除
type PatientDiseaseDTO struct {
	Testid string `json:"testid"`
	Deptid string `json:"deptid"`
	Disid  int    `json:"disid"` //疾病编号
	ID     int    `json:"id"`
}

// CiQueryDTO ... 体检项目查找DTO
type CiQueryDTO struct {
	Testid   []string `json:"testid"`
	Deptid   []string `json:"deptid"`
	Barcode  string   `json:"barcode"`  //条码
	Flag     int      `json:"flag"`     //flag = 是否异常标志 - 1:全部 0：正常 1：异常 2：未检
	Combined int      `json:"combined"` //combined = combined -1:全部 0：非组合项目 1：组合项目
}

// CiDeleteDTO ... 体检项目删除DTO
type CiDeleteDTO struct {
	Testid string `json:"testid"`
	Synid  string `json:"synid"`
}

// TsQueryDTO ... test summary query condition
type TsQueryDTO struct {
	Stdate     string   `json:"stdate"`  //开始时间
	Enddate    string   `json:"enddate"` //结束时间
	Testid     []string `json:"testid"`  // 体检号
	Deptid     []string `json:"deptid"`  //科室
	IsFinished int      `json:"isfinished"`
	IsForceend int      `json:"isforceend"`
	StaffNo    string   `json:"staffid"`
}

// TsDeleteDTO ...
type TsDeleteDTO struct {
	Testid string `json:"testid"`
	Deptid string `json:"deptid"`
}

// CaQueryDTO ...
type CaQueryDTO struct {
	Testids  []string `json:"testids"`
	Stdate   string   `json:"stdate"`
	Enddate  string   `json:"enddate"`
	CaStatus int      `json:"status"`  //状态
	RetType  int      `json:"rettype"` //结果类型
}

// CorpQueryDTO ...
type CorpQueryDTO struct {
	Ids      []int  `json:"keys"`
	Type     int    `json:"type"`
	CorpID   string `json:"corpid"`
	CorpName string `json:"name"`
}

// CorpMedQuery ...
type CorpMedQuery struct {
	Status       int `json:"status"`
	Corpnum      int `json:"corpnum"`
	Stdate       int `json:"stdate"`
	Enddate      int `json:"enddate"`
	HidePersonal int `json:"hidepersonal"` //是否显示个人
}

// AudiogramDTO ...
type AudiogramDTO struct {
	Testid string `json:"testid"` //
	Ear    int    `json:"ear"`    //耳朵 0：右耳 1：左耳
	Type   int    `json:"type"`   //类型 0：气导 1：骨导
	Freq   int    `json:"freq"`   //频率
}

// ItemdelDTO ...
type ItemdelDTO struct {
	Combid string `json:"combid"`
	Itemid string `json:"itemid"`
}

// ItemrefDTO ...
type ItemrefDTO struct {
	Struid   string `json:"struid"`
	Strucode string `json:"strucode"`
	Itemid   string `json:"itemid"`
}

// ItemrefDelDTO ... itemref delete DTO
type ItemrefDelDTO struct {
	ID     int    `json:"id"`
	Struid string `json:"struid"`
}

// PackagedelDTO ...
type PackagedelDTO struct {
	ID   int `json:"id"`
	Pnum int `json:"pnum"`
}

// BardetailDTO ...
type BardetailDTO struct {
	Binum  []string `json:"binum"`
	Itemid []string `json:"itemid"`
}

// DeptItemDTO ...
type DeptItemDTO struct {
	Deptid string `json:"deptid"`
	Itemid string `json:"itemid"`
}

// LabresultDTO ...
type LabresultDTO struct {
	Testid  string `json:"testid"`
	DtStart string `json:"dtstart"`
	DtEnd   string `json:"dtend"`
}

// HazardDTO ...
type HazardDTO struct {
	Hids   []int    `json:"ids"`    //毒害因素ID
	Hnames []string `json:"hnames"` //毒害因素名称列表
	Htype  int      `json:"type"`   //毒害因素类别
}

// HazardDIDTO ...
type HazardDIDTO struct {
	Hids   []int  `json:"ids"`    //id列表
	Type   int    `json:"type"`   //体检类型
	Itemid string `json:"itemid"` //项目编号
}

// DiseaseDTO ...
type DiseaseDTO struct {
	Ids     []int `json:"id"`      //疾病ID列表
	Deptid  []int `json:"deptid"`  //科室编号
	Typeids []int `json:"typeids"` //类型
}

// OccuconditionDTO ...
type OccuconditionDTO struct {
	Testtype  int    `json:"testtype"` //体检类型
	Poisionid int    `json:"poision"`  //毒害因素编号
	Itemid    string `json:"itemid"`   //项目编号
}

// AutodiagDTO ...
type AutodiagDTO struct {
	Disids  []int    `json:"disids"`  //疾病列表
	Itemids []string `json:"itemids"` //项目列表
}

// GuideitemDTO ...
type GuideitemDTO struct {
	Guideid int    `json:"guideid"` //指引单编号
	Itemid  string `json:"itemid"`  //项目编号
}

// StaffDeptDTO ...
type StaffDeptDTO struct {
	StaffID int    `json:"staffid"`
	DeptID  string `json:"deptid"`
}

// ReportDTO ...
type ReportDTO struct {
	Reportids   []int  `json:"reportids"`
	Dtstart     string `json:"dtstart"`
	Dtend       string `json:"dtend"`
	Corp        string `json:"Corp"`       //单位编号
	Type        string `json:"type"`       //类型
	Patientname string `json:"pname"`      //姓名
	Testid      string `json:"testid"`     //体检号
	Reportnum   string `json:"reportnum"`  // 报告编号
	Orptid      int    `json:"orptid"`     //原报告ID
	Reporttype  int    `json:"reporttype"` //报告类型
}

// ReportinfoDTO ...
type ReportinfoDTO struct {
	Testids   []string `json:"testids"`
	Reportids []string `json:"reportids"`
	Rptids    []int32  `json:"rptids"`
}

// scdchazardfactordto
type SCDCHazardFactorDTO struct {
	CdcCode  string `json:"cdc_code"`
	CdcName  string `json:"cdc_name"`
	HazardID int    `json:"hazard_id"`
}

type ExtDTO struct {
	Testid string `json:"testid"`
	Extkey string `json:"extkey"`
}

type ExtTypeDTO struct {
	Testid  string `json:"testid"`
	Exttype int    `json:"exttype"`
	// Additional string `json:"additional"`
}

type LisDTO struct {
	Testid        string `json:"testid"`
	Itemcode      string `json:"itemcode"`
	Itemname      string `json:"itemname"`
	Result        string `json:"result"`
	Range_l       string `json:"range l"`
	Range_h       string `json:"range h"`
	Refrange      string `json:"refrange"`
	Abnormal      int    `json:"abnormal"` //是否异常 0：正常 1：异常
	Displayflag   string `json:"displayflag"`
	Checkdoctor   string `json:"checkdoctor"`
	Checkdate     string `json:"checkdate"`
	Recheckdoctor string `json:"recheckdoctor"`
	Recheckdate   string `json:"recheckdate"`
}

type PacsDTO struct {
	Testid         string `json:"testid"`
	Jclx           string `json:"jclx"`
	Jxmc           string `json:"jxmc"`
	Jcxm           string `json:"jcxm"`
	Jcys           string `json:"jcys"`
	Bgys           string `json:"bgys"`
	Imagesight     string `json:"imagesight"`
	Imagediagnosis string `json:"imagediagnosis"`
	Bgrq           string `json:"bgrq"`
}

type MedexaminfoView struct {
	ID int
	// bool selected
	Testid        string //体检编号
	Age           int
	Testcatid     int
	Testcatname   string //体检类别：个人，单位
	Testtypeid    int
	Testtypename  string //体检类型
	Worktypeid    string
	Worktypename  string //工种
	Worktypecode  string //工种代码
	Workage       string //工龄
	Poisionfactor string //毒害因素
	Poisionage    string //接害工龄
	Recorddate    int64  //登记日期
	Printflagid   int
	Printflagname string //打印标志，0未打印1已打印
	Printtimes    int    //打印次数
	Testdate      int64  //体检日期
	Reportnum     string //报告编号
	Preportnum    string //个人报告编号
	Statusid      int    //
	Statusname    string //体检状态
	Peid          int    //所属单位体检编号
	Empid         string //工号

	//个人信息
	Pname         string //名字
	Pid           string //档案编号
	Sexid         int
	Sexname       string //性别
	Pidcard       string //身份证号
	Pmarriageid   int
	Nation        string //民族
	Pmarriagename string //婚姻状况
	Pcareer       string //职业
	Paddress      string //地址
	Pmobile       string //号码
	Pbirthday     string //出生日期
	Photo         string //照片

	//企业信息
	Corpid   int    //公司ID
	Corpnum  string //公司编码
	Corpname string //所在公司

	//总检信息
	Checkresultesid   int    //总检结果id
	Checkresultsstr   string //总检结果对应的名字， 正常......
	Checkresultesname string //总检结果
	Checkstatusid     int    //总检状态 1:已经总检
	Checkstatusname   string // 总检状态中文显示
	Checkalldate      int64  //总检时间
	//科室信息
	//int Departid //科室ID
	//string Departcode //科室代码
	//string departname //科室名字

	////职业禁忌与职业病
	Forbidden string
	Targetdis string
}
