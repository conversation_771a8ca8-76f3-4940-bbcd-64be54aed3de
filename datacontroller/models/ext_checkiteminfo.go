package models

import (
	"errors"
	"utility/logger"
)

type ExtCheckiteminfoModel struct{}

// VexternalLis ... VexternalLis
type ExtCheckiteminfo struct {
	ID            int64  `gorm:"primary_key;auto_increment;column:id;type:bigint(10);not null" json:"id"`
	Testid        string `gorm:"column:testid" json:"testid"`
	Tid           string `gorm:"column:tid" json:"tid"`
	Testername    string `gorm:"column:testername" json:"testername"`
	Idcard        string `gorm:"column:idcard" json:"idcard"`
	Psex          int    `gorm:"column:psex" json:"psex"`
	Csex          string `gorm:"column:csex" json:"csex"`
	Birthdate     string `gorm:"column:birthdate" json:"birthdate"`
	Phone         string `gorm:"column:phone" json:"phone"`
	Age           int    `gorm:"column:age" json:"age"`
	Itemname      string `gorm:"column:itemname" json:"itemname"`
	Itemid        string `gorm:"column:itemid" json:"itemid"`
	Itemid2       string `gorm:"column:itemid2" json:"itemid2"`
	Deptid        string `gorm:"column:deptid" json:"deptid"`
	Deptname      string `gorm:"column:deptname" json:"deptname"`
	Depttype      int    `gorm:"column:depttype" json:"depttype"`
	Requesterid   string `gorm:"column:requesterid" json:"requesterid"`
	Requestername string `gorm:"column:requestername" json:"requestername"`
	Requestdate   uint64 `gorm:"column:requestdate" json:"requestdate"`
	Requestdate2  string `gorm:"column:requestdate2" json:"requestdate2"`
	Paytype       int    `gorm:"column:paytype" json:"paytype"`
	Packagename   string `gorm:"column:packagename" json:"packagename"`
	Zdym          string `gorm:"column:zdym" json:"zdym"`
	Sampletype    string `gorm:"column:sampletype" json:"sampletype"`
	Corpnum       int    `gorm:"column:corpnum" json:"corpnum"`
	Syncstatus    int    `gorm:"column:syncstatus" json:"syncstatus"`
}

// TableName 会将 struct 的表名重写为 `string`
func (ExtCheckiteminfo) TableName() string {
	return "ext_checkiteminfo"
}

/// insert_many 批量插入数据
func (ExtCheckiteminfoModel) InsertMany(info *[]ExtCheckiteminfo) error {
	if info == nil || len(*info) <= 0 {
		logger.Log.Errorf("请求数据错误，体检号为空")
		return errors.New("插入数据不能为空")
	}

	tx := db.CreateInBatches(*info, 100)

	if tx.Error != nil {
		logger.Log.Errorf("Insert TjCheckiteminfo err: %+v", tx.Error)
		return tx.Error
	}

	return nil
}

func (ExtCheckiteminfoModel) DeleteMany(testid string) error {
	tx := db.Exec("delete from ext_checkiteminfo where testid = ?", testid).Debug()
	return tx.Error
}

func (ExtCheckiteminfoModel) QueryMany(testid string, exttype int) ([]ExtCheckiteminfo, error) {
	var err error

	var infos []ExtCheckiteminfo

	ret := db.Table("ext_checkiteminfo").Where("testid = ?", testid)
	if exttype != 0 {
		ret = ret.Where("depttype = ?", exttype)
	}

	ret = ret.Find(&infos)

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}

	return infos, nil
}

// func GenerateExtcheckitem(ciinfo *[]TjCheckiteminfo, )
