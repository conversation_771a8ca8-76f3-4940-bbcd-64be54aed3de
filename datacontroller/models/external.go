package models

import "utility/logger"

// ExternalView ...
type ExternalView struct{}

// VexternalLis ... VexternalLis
type VexternalChecks struct {
	ID            int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	Testid        string `gorm:"column:testid" json:"testid"`
	Tid           string `gorm:"column:tid" json:"tid"`
	Testername    string `gorm:"column:testername" json:"testername"`
	Idcard        string `gorm:"column:idcard" json:"idcard"`
	Psex          string `gorm:"column:psex" json:"psex"`
	Sex           string `gorm:"column:sex" json:"sex"`
	Birthdate     string `gorm:"column:birthdate" json:"birthdate"`
	Phone         string `gorm:"column:phone" json:"phone"`
	Age           string `gorm:"column:age" json:"age"`
	Itemname      string `gorm:"column:itemname" json:"itemname"`
	Sampletype    string `gorm:"column:sampletype" json:"sampletype"` //
	Depttype      int    `gorm:"column:depttype" json:"depttype"`     //depttype
	Corpnum       int    `gorm:"column:corpnum" json:"corpnum"`       //depttype
	Itemid        string `gorm:"column:itemid" json:"itemid"`
	Deptid        string `gorm:"column:deptid" json:"deptid"`
	Deptname      string `gorm:"column:deptname" json:"deptname"`
	Requesterid   string `gorm:"column:requesterid" json:"requesterid"`
	Requestername string `gorm:"column:requestername" json:"requestername"`
	Requestdate   uint64 `gorm:"column:requestdate" json:"requestdate"` //
	Zdym          string `gorm:"column:zdym" json:"zdym"`               //zdym
	Paytype       int    `gorm:"column:paytype" json:"paytype"`
}

// TableName 会将 User 的表名重写为 `profiles`
func (VexternalChecks) TableName() string {
	return "v_external_checks"
}

// VexternalLis ... VexternalLis
type VexternalLis struct {
	ID            int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	Testid        string `gorm:"column:testid" json:"testid"`
	Tid           string `gorm:"column:tid" json:"tid"`
	Testername    string `gorm:"column:testername" json:"testername"`
	Idcard        string `gorm:"column:idcard" json:"idcard"`
	Psex          string `gorm:"column:psex" json:"psex"`
	Sex           string `gorm:"column:sex" json:"sex"`
	Birthdate     string `gorm:"column:birthdate" json:"birthdate"`
	Phone         string `gorm:"column:phone" json:"phone"`
	Age           string `gorm:"column:age" json:"age"`
	Itemname      string `gorm:"column:itemname" json:"itemname"`
	Sampletype    string `gorm:"column:sampletype" json:"sampletype"`
	Itemid        string `gorm:"column:itemid" json:"itemid"`
	Deptid        string `gorm:"column:deptid" json:"deptid"`
	Deptname      string `gorm:"column:deptname" json:"deptname"`
	Requesterid   string `gorm:"column:requesterid" json:"requesterid"`
	Requestername string `gorm:"column:requestername" json:"requestername"`
	Requestdate   uint64 `gorm:"column:requestdate" json:"requestdate"`
	Requestdate2  string `gorm:"column:requestdate2" json:"requestdate2"`
	Paytype       int    `gorm:"column:paytype" json:"paytype"`
}

// TableName 会将 User 的表名重写为 `profiles`
func (VexternalLis) TableName() string {
	return "v_external_lis"
}

// VexternalPacs ... VexternalPacs
type VexternalPacs struct {
	ID            int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	Testid        string `gorm:"column:testid" json:"testid"`
	Testername    string `gorm:"column:testername" json:"testername"`
	Psex          string `gorm:"column:psex" json:"psex"`
	Sex           string `gorm:"column:sex" json:"sex"`
	Idcard        string `gorm:"column:idcard" json:"idcard"`
	Birthdate     string `gorm:"column:birthdate" json:"birthdate"`
	Age           string `gorm:"column:age" json:"age"`
	Zdym          string `gorm:"column:zdym" json:"zdym"`
	Itemid        string `gorm:"column:itemid" json:"itemid"`
	Itemname      string `gorm:"column:itemname" json:"itemname"`
	Deptid        string `gorm:"column:deptid" json:"deptid"`
	Deptname      string `gorm:"column:deptname" json:"deptname"`
	Requesterid   string `gorm:"column:requesterid" json:"requesterid"`
	Requestername string `gorm:"column:requestername" json:"requestername"`
	Requestdate   uint64 `gorm:"column:requestdate" json:"requestdate"`
	Requestdate2  string `gorm:"column:requestdate2" json:"requestdate2"`
}

// TableName 会将 User 的表名重写为 `profiles`
func (VexternalPacs) TableName() string {
	return "v_external_pacs"
}

// GetLisItems ... GetLisItems by code
func (ExternalView) GetCheckItemsbyTestid(testid string) ([]VexternalChecks, error) {
	var err error

	var infos []VexternalChecks

	ret := db.Table("v_external_checks").Where("testid = ?", testid).Find(&infos)

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}

	return infos, nil
}

// GetLisItems ... GetLisItems by code
func (ExternalView) GetLisCheckItems(barcode string) (*[]VexternalLis, error) {
	var err error

	var infos []VexternalLis

	ret := db.Table("v_external_lis").Where("tid = ?", barcode).Find(&infos)

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}

	return &infos, nil
}

// GetLisItemsByTestid ... GetLisItems by testid
func (ExternalView) GetLisCheckItemsbyTestid(testid string) (*[]VexternalLis, error) {
	var err error

	var infos []VexternalLis

	ret := db.Table("v_external_lis").Where("testid = ?", testid).Find(&infos).Debug()

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}

	logger.Log.Infof("lis结果信息:%+v\n", infos)

	return &infos, nil
}

// GetLisItems ... GetLisItems by code
func (ExternalView) GetPacsCheckItems(barcode string) (*[]VexternalPacs, error) {
	var err error

	var infos []VexternalPacs

	ret := db.Table("v_external_pacs").Where("testid = ?", barcode).Find(&infos)

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}

	return &infos, nil
}
