package models

type GjCdcCheckitem struct {
	ID           int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	CdcItemcode  string `gorm:"column:cdc_itemcode" json:"cdc_itemcode"`
	CdcItemName  string `gorm:"column:cdc_item_name" json:"cdc_item_name"`
	CdcItemLevel int    `gorm:"column:cdc_item_level" json:"cdc_type"`
	// CdcItemPcode string `gorm:"column:cdc_item_pcode" json:"cdc_item_pcode"`
	CdcDeptid int    `gorm:"column:cdc_is_final" json:"cdc_deptid"`
	TjItemid  string `gorm:"column:tj_itemid" json:"tj_itemid"`
}

func (*GjCdcCheckitem) TableName() string {
	return "gj_cdc_checkitem"
}
func (*GjCdcCheckitem) GetPrimaryKey() string {
	return "id"
}

func (GjCdcCheckitem) NewSlice() interface{} {
	return &[]GjCdcCheckitem{}
}

func (GjCdcCheckitem) NewStruct() interface{} {
	return &GjCdcCheckitem{}
}

// // UpdateGcCDCCheckItem ...
// func UpdateGjcdcCheckitem(info *ExtCodeDTO) error {

// 	log.Infof("更新上报平台的项目信息:%+v", info)
// 	var infos []GjCdcCheckitem
// 	result := db.Table("gj_cdc_checkitem").Where("id >= 1 and cdc_itemcode = ?", info.CdcItemcode).Debug().Find(&infos)

// 	if result.Error != nil {

// 		log.Errorf("获取项目信息错误:%+v", result.Error)
// 		return result.Error
// 		// return nil, err
// 	}

// 	if len(infos) <= 0 {
// 		log.Errorf("找不到代码为%s的项目信息", info.CdcItemcode)
// 		return fmt.Errorf("找不到代码为%s的项目信息", info.CdcItemcode)
// 	}

// 	//clear
// 	ret := db.Exec("update gj_cdc_checkitem set tj_itemid = 0 where tj_itemid = ?", info.TjItemid).Debug()
// 	if ret.Error != nil {
// 		return ret.Error
// 	}
// 	// areaprovince := app.Application.Areaprovince

// 	if len(infos) >= 1 {
// 		info.ID = infos[0].ID
// 		info.CdcDeptid = infos[0].CdcDeptid
// 		info.CdcItemcode = infos[0].CdcItemcode
// 		info.CdcItemName = infos[0].CdcItemName
// 		info.CdcItemLevel = infos[0].CdcItemLevel
// 		// info.CdcItemPcode = infos[0].CdcItemPcode
// 		ret := db.Table("gj_cdc_checkitem").Debug().Save(info)
// 		if ret.Error != nil {
// 			log.Errorf("更新匹配信息错误，错误：%+v", ret.Error)
// 			return ret.Error
// 		}
// 		// log.Debugf("开始删除编号为:%d的匹配的毒害因素", hdid)
// 		// ret := db.Exec("delete from s_cdc_hazardfactor where tj_hazardid = ?", hdid).Debug()
// 		// ret := db.Exec("update s_cdc_hazardfactor set tj_hazardid = 0 where tj_hazardid = ?", hdid).Debug()
// 	}
// 	return nil
// }
