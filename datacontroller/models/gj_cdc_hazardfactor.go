package models

type GjCdcHazardfactor struct {
	Id          int32  `gorm:"column:id;auto_increment;primary_key" json:"id"`
	CdcCode     string `gorm:"column:cdc_code" json:"cdc_code"`
	HazardType  int32  `gorm:"column:hazard_type" json:"hazard_type"`
	TjHazardid  int32  `gorm:"column:tj_hazardid" json:"hazard_id"`
	Description string `gorm:"column:description" json:"cdc_name"`
}

func (*GjCdcHazardfactor) TableName() string {
	return "gj_cdc_hazardfactor"
}
func (*GjCdcHazardfactor) GetPrimaryKey() string {
	return "id"
}

func (GjCdcHazardfactor) NewSlice() interface{} {
	return &[]GjCdcHazardfactor{}
}

func (GjCdcHazardfactor) NewStruct() interface{} {
	return &GjCdcHazardfactor{}
}

// // UpdateGjCdcHazardfactor ...
// func UpdateGjCdcHazardfactor(info *ExtCodeDTO) error {

// 	log.Infof("更新上报平台的毒害因素信息: %+v", info)
// 	var infos []GjCdcHazardfactor
// 	//clear
// 	ret := db.Exec("update tj_hazard set tj_hazardid = 0 where tj_hazardid = ?", info.TjHazardid).Debug()
// 	if ret.Error != nil {
// 		return ret.Error
// 	}
// 	// areaprovince := app.Application.Areaprovince
// 	result := db.Table("gj_cdc_hazardfactor").Where("id >= 1 and cdc_code = ?", info.ExternalCode).Debug().Find(&infos)

// 	if result.Error != nil {
// 		log.Errorf("获取项目信息错误:%+v", result.Error)
// 		info.Id = 0
// 		ret := db.Table("gj_cdc_hazardfactor").Create(info)
// 		if ret.Error != nil {
// 			log.Errorf("插入匹配信息错误，错误：%+v", ret.Error)
// 			return ret.Error
// 		}
// 		// return nil, err
// 	}

// 	if len(infos) <= 0 {
// 		log.Errorf("找不到代码为%s的项目信息，开始插入新数据", info.ExternalCode)
// 		info.Id = 0
// 		ret := db.Table("gj_cdc_hazardfactor").Debug().Create(info)
// 		if ret.Error != nil {
// 			log.Errorf("插入匹配信息错误，错误：%+v", ret.Error)
// 			return ret.Error
// 		}
// 	}

// 	if len(infos) >= 1 {
// 		info.Id = infos[0].Id
// 		info.HazardType = infos[0].HazardType
// 		info.Description = infos[0].Description

// 		// info.CdcType = infos[0].CdcType
// 		ret := db.Table("gj_cdc_hazardfactor").Debug().Save(info)
// 		if ret.Error != nil {
// 			log.Errorf("更新匹配信息错误，错误：%+v", ret.Error)
// 			return ret.Error
// 		}
// 		// log.Debugf("开始删除编号为:%d的匹配的毒害因素", hdid)
// 		// ret := db.Exec("delete from s_cdc_hazardfactor where tj_hazardid = ?", hdid).Debug()
// 		// ret := db.Exec("update s_cdc_hazardfactor set tj_hazardid = 0 where tj_hazardid = ?", hdid).Debug()
// 	}
// 	return nil
// }
