package models

type ScdcCheckitem struct {
	ID          int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	CdcItemcode string `gorm:"column:cdc_itemcode;type:varchar(20);not null" json:"cdc_itemcode"`    // 项目代码
	CdcItemName string `gorm:"column:cdc_item_name;type:varchar(100);not null" json:"cdc_item_name"` // cdc_item_name
	CdcDeptid   int    `gorm:"column:cdc_deptid;type:int(4);not null" json:"cdc_deptid"`             //
	CdcType     int    `gorm:"column:cdc_type;type:int(4);not null" json:"cdc_type"`                 /*结果类型 1：定性 2：定量 3：描述*/
	TjItemid    string `gorm:"column:tj_itemid;type:varchar(20);not null" json:"tj_itemid"`          //
}

// TableName get sql table name.获取数据库表名
func (ScdcCheckitem) TableName() string {
	return "s_cdc_checkitem"
}

// UpdateScdcCheckitem ...
func UpdateScdcCheckitem(info *ExtCodeDTO) error {

	// logger.Log.Infof("更新上报平台的项目信息:%+v", info)
	// var infos []ScdcCheckitem
	//clear
	ret := db.Exec("update tj_iteminfo set tj_extcode = '' where tj_extcode = ?", info.ExternalCode)
	if ret.Error != nil {
		return ret.Error
	}
	ret = db.Exec("update tj_iteminfo set tj_extcode = ? where tj_itemid = ?", info.ExternalCode, info.InternalCode).Debug()
	if ret.Error != nil {
		return ret.Error
	}

	// // areaprovince := app.Application.Areaprovince
	// result := db.Table("s_cdc_checkitem").Where("id >= 1 and cdc_itemcode = ?", info.CdcItemcode).Debug().Find(&infos)

	// if result.Error != nil {
	// 	if info.CdcType == 0 {
	// 		info.CdcType = 3
	// 	}
	// 	log.Errorf("获取项目信息错误:%+v", result.Error)
	// 	ret := db.Table("s_cdc_checkitem").Create(info)
	// 	if ret.Error != nil {
	// 		log.Errorf("插入匹配信息错误，错误：%+v", ret.Error)
	// 		return ret.Error
	// 	}
	// 	// return nil, err
	// }

	// if len(infos) <= 0 {
	// 	log.Errorf("找不到代码为%s的项目信息，开始插入新数据", info.CdcItemcode)
	// 	info.ID = 0
	// 	if info.CdcType == 0 {
	// 		info.CdcType = 3
	// 	}
	// 	ret := db.Table("s_cdc_checkitem").Debug().Create(info)
	// 	if ret.Error != nil {
	// 		log.Errorf("插入匹配信息错误，错误：%+v", ret.Error)
	// 		return ret.Error
	// 	}
	// }

	// if len(infos) >= 1 {
	// 	info.ID = infos[0].ID
	// 	info.CdcDeptid = infos[0].CdcDeptid
	// 	if info.CdcType == 0 {
	// 		info.CdcType = infos[0].CdcType
	// 	}
	// 	// info.CdcType = infos[0].CdcType
	// 	ret := db.Table("s_cdc_checkitem").Debug().Save(info)
	// 	if ret.Error != nil {
	// 		log.Errorf("更新匹配信息错误，错误：%+v", ret.Error)
	// 		return ret.Error
	// 	}
	// 	// log.Debugf("开始删除编号为:%d的匹配的毒害因素", hdid)
	// 	// ret := db.Exec("delete from s_cdc_hazardfactor where tj_hazardid = ?", hdid).Debug()
	// 	// ret := db.Exec("update s_cdc_hazardfactor set tj_hazardid = 0 where tj_hazardid = ?", hdid).Debug()
	// }
	return nil
}
