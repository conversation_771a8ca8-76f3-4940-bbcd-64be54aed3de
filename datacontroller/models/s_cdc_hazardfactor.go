package models

import (
	"errors"
	"utility/logger"

	"gorm.io/gorm"
)

// ScdcHazardfactor
type ScdcHazardfactor struct {
	ID            int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	CdcCode       string `gorm:"column:cdc_code;type:varchar(20);not null" json:"cdc_code"`            // 毒害因素的编号
	CdcHazardname string `gorm:"column:cdc_hazardname;type:varchar(100);not null" json:"cdc_name"`     // cdc_hazardname
	HazardType    int    `gorm:"column:hazard_type;type:int(4);not null" json:"hazard_type"`           //
	TjHazardid    int    `gorm:"column:tj_hazardid;type:int(4);not null" json:"hazard_id"`             //
}

// TableName get sql table name.获取数据库表名
func (ScdcHazardfactor) TableName() string {
	return "s_cdc_hazardfactor"
}

// ******************** 危害因素对应的代码 ********************************* BEGIN

// UpdateScdcCheckitem ...
func UpdateScdcHazardfactor(info *ExtCodeDTO) error {

	logger.Log.Infof("更新上报平台的毒害因素信息:%+v", info)
	// var infos []ScdcHazardfactor
	//clear
	ret := db.Exec("update tj_hazardinfo set tj_extcode = ? where id = ?", info.ExternalCode, info.InternalCode).Debug()
	if ret.Error != nil {
		return ret.Error
	}
	// areaprovince := app.Application.Areaprovince
	// result := db.Table("s_cdc_hazardfactor").Where("id >= 1 and cdc_code = ?", info.ExternalCode).Debug().Find(&infos)

	// if result.Error != nil {
	// 	logger.Log.Errorf("获取项目信息错误:%+v", result.Error)
	// 	ret := db.Table("s_cdc_hazardfactor").Create(info)
	// 	if ret.Error != nil {
	// 		logger.Log.Errorf("插入匹配信息错误，错误：%+v", ret.Error)
	// 		return ret.Error
	// 	}
	// 	// return nil, err
	// }

	// if len(infos) <= 0 {
	// 	log.Errorf("找不到代码为%s的项目信息，开始插入新数据", info.ExternalCode)
	// 	info.ID = 0
	// 	ret := db.Table("s_cdc_hazardfactor").Debug().Create(info)
	// 	if ret.Error != nil {
	// 		log.Errorf("插入匹配信息错误，错误：%+v", ret.Error)
	// 		return ret.Error
	// 	}
	// }

	// if len(infos) >= 1 {
	// 	info.ID = infos[0].ID
	// 	info.ExternalCode = infos[0].CdcCode
	// 	info.CdcHazardname = infos[0].CdcHazardname
	// 	// info.CdcType = infos[0].CdcType
	// 	ret := db.Table("s_cdc_hazardfactor").Debug().Save(info)
	// 	if ret.Error != nil {
	// 		log.Errorf("更新匹配信息错误，错误：%+v", ret.Error)
	// 		return ret.Error
	// 	}
	// 	// log.Debugf("开始删除编号为:%d的匹配的毒害因素", hdid)
	// 	// ret := db.Exec("delete from s_cdc_hazardfactor where tj_hazardid = ?", hdid).Debug()
	// 	// ret := db.Exec("update s_cdc_hazardfactor set tj_hazardid = 0 where tj_hazardid = ?", hdid).Debug()
	// }
	return nil
}

// GetScdcHazardfactors ...
func (HazardModel) GetScdcHazardfactors(cdc_code string, tjhazardid int) (*[]ScdcHazardfactor, error) {
	var infos []ScdcHazardfactor
	var ret *gorm.DB

	ret = db.Table("s_cdc_hazardfactor").Where("1 = 1")
	if cdc_code != "" {
		ret = ret.Where("cdc_code = ?", cdc_code)
	}

	if tjhazardid > 0 {
		ret = ret.Where("tj_hazardid = ?", tjhazardid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// InsertHazarddisease ...
func (HazardModel) InsertScdcHazardfactor(info *ScdcHazardfactor) (*ScdcHazardfactor, error) {
	logger.Log.Infof("Save s_cdc_hazardfactor:%+v", info)
	result := db.Table("s_cdc_hazardfactor").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateScdcHazardfactor ...
func (HazardModel) UpdateScdcHazardfactorInfo(info *ScdcHazardfactor) (*ScdcHazardfactor, error) {

	logger.Log.Infof("update s_cdc_hazardfactor:%+v", info)
	result := db.Table("s_cdc_hazardfactor").Debug().Save(info)
	if result.Error != nil {
		return nil, result.Error
	}
	return info, nil
	// if hdid <= 0 {
	// 	return errors.New("hdid 不能为空")
	// }
	// logger.Log.Debugf("开始更新编号为:%d的匹配的毒害因素", hdid)
	// // ret := db.Exec("delete from s_cdc_hazardfactor where tj_hazardid = ?", hdid).Debug()
	// ret := db.Exec("update s_cdc_hazardfactor set tj_hazardid = 0 where tj_hazardid = ?", hdid).Debug()

	// return ret.Error
}

// UpdateScdcHazardfactor ...
func (HazardModel) UpdateScdcHazardfactor(hdid int) error {

	// logger.Log.Infof("update s_cdc_hazardfactor:%+v", info)
	// result := db.Table("tj_hazarddisease").Debug().Save(info)
	// if result.Error != nil {
	// 	return result.Error
	// }
	if hdid <= 0 {
		return errors.New("hdid 不能为空")
	}
	logger.Log.Debugf("开始更新编号为:%d的匹配的毒害因素", hdid)
	// ret := db.Exec("delete from s_cdc_hazardfactor where tj_hazardid = ?", hdid).Debug()
	ret := db.Exec("update s_cdc_hazardfactor set tj_hazardid = 0 where tj_hazardid = ?", hdid).Debug()

	return ret.Error
}

// ******************** 危害因素对应的代码 ********************************* END
