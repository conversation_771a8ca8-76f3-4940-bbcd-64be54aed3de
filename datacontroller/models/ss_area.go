package models

import (
	"strings"
	"utility/logger"
)

// SsArea ... SsArea
type SsArea struct {
	ID           int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	AreaCode     string `gorm:"column:area_code" json:"area_code"`
	AreaName     string `gorm:"column:area_name" json:"area_name"`
	AreaFullname string `gorm:"column:area_fullname" json:"area_fullname"`
	AreaLevel    int    `gorm:"column:area_level" json:"area_level"`
	AreaPcode    string `gorm:"column:area_pcode" json:"area_pcode"`
}

// TableName 会将 User 的表名重写为 `profiles`
func (SsArea) TableName() string {
	return "ss_area"
}

// GetAreaInfo ... GetAreaInfo by code
func (InfoModel) GetAreaInfo(code string, areacode string) (*[]SsArea, error) {
	var err error

	var infos []SsArea
	// areaprovince := app.Application.Areaprovince
	ret := db.Table("ss_area").Where("id >= 1")
	if areacode != "" {
		ret = ret.Where("SUBSTRING(area_code,1,2) in (?)", strings.Split(areacode, ","))
	}
	if code != "0" {
		ret = ret.Where("area_code = ?", code)
	}

	result := ret.Debug().Find(&infos)
	// ret := db.Table("ss_area").Where("area_code = ?", code).Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, err
	}

	return &infos, nil
}

// GetAreaInfosbyParentcode ... GetAreaInfos
func (InfoModel) GetAreaInfosbyParentcode(pcode string) (*[]SsArea, error) {
	var err error
	// var u = SsInfoconfig{}
	var infos []SsArea
	// var ret *gorm.DB
	// if uno == 0 {
	// ret := db.Find(&infos)
	// } else {
	// err = db.Debug().Model(SsInfoconfig{}).Where("ss_type = ?", uno).Take(&u).Error
	ret := db.Table("ss_area").Where("area_pcode = ?", pcode).Find(&infos)
	// }

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}

	return &infos, nil
}
