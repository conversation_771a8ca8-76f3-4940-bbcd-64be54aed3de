package models

import (
	"errors"
	"time"
	"utility/logger"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// SsDictionary 系统数据字典表
type SsDictionary struct {
	ID          int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	SsTypeid    int    `gorm:"column:ss_typeid;type:int(11);not null" json:"ss_typeid"`
	SsPid       int    `gorm:"column:ss_pid;type:int(10);not null" json:"ss_pid"`
	SsShort     string `gorm:"column:ss_short;type:varchar(100);not null" json:"ss_short"`
	SsName      string `gorm:"column:ss_name;type:varchar(200);not null" json:"ss_name"`
	SsShoworder int    `gorm:"column:ss_showorder;type:int(11);not null" json:"ss_showorder"`
	SsMemo      string `gorm:"column:ss_memo;type:varchar(200);not null" json:"ss_memo"`
}

// TableName get sql table name.获取数据库表名
func (SsDictionary) TableName() string {
	return "ss_dictionary"
}

// SsIDentity 系统值表
type SsIDentity struct {
	ID         int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	IdName     string `gorm:"column:id_name;type:varchar(30);not null" json:"id_name"`              //  名称
	IdCvalue   int    `gorm:"column:id_cvalue;type:int(11);not null" json:"id_cvalue"`              //  当前值
	IdOvalue   int    `gorm:"column:id_ovalue;type:int(11);not null" json:"id_ovalue"`              //  起始值
	IdIncvalue int    `gorm:"column:id_incvalue;type:int(11);not null" json:"id_incvalue"`          //  自动增量值
}

// TableName get sql table name.获取数据库表名
func (SsIDentity) TableName() string {
	return "ss_identity"
}

// TjSampletype [...]
type TjSampletype struct {
	ID         int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"`
	SampleCode string `gorm:"column:sample_code;type:varchar(5);not null" json:"sample_code"`  // 样本类型编码
	SampleName string `gorm:"column:sample_name;type:varchar(20);not null" json:"sample_name"` // 样本类型名称
	SamplePy   string `gorm:"column:sample_py;type:varchar(20);not null" json:"sample_py"`     // 样本拼音简写
	SampleMemo string `gorm:"column:sample_memo;type:varchar(50);not null" json:"sample_memo"` // 样本备注说明
}

// TableName get sql table name.获取数据库表名
func (TjSampletype) TableName() string {
	return "tj_sampletype"
}

// FindDictionary ... FindDictionary
func FindDictionary(typeid int, pid int) (*[]SsDictionary, error) {
	// var err error
	// // var u = SsInfoconfig{}
	var infos []SsDictionary
	// var ret *gorm.DB
	// if typeid == 0 {
	// 	ret = db.Table("ss_identity").Find(&infos)
	// } else {
	// 	ret = db.Table("ss_identity").Where("ss_typeid = ?", typeid).Find(&infos)
	// }
	ret := db.Table("ss_dictionary").Where("ss_typeid = ? and ss_pid = ?", typeid, pid).Find(&infos)
	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, ret.Error
	}

	return &infos, nil
}

// FindDictionaries ...
func FindDictionaries(typeid int) ([]SsDictionary, error) {
	var err error
	// var u = SsInfoconfig{}
	var infos []SsDictionary
	var ret *gorm.DB
	if typeid == 0 {
		ret = db.Table("ss_dictionary").Find(&infos)
	} else {
		ret = db.Table("ss_dictionary").Where("ss_typeid = ?", typeid).Find(&infos)
	}

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}
	// log.Printf("所有的字典信息：%+v\n", &infos)
	return infos, nil
}

// SaveDictionary ...
func SaveDictionary(info *SsDictionary) error {
	if info == nil {
		return errors.New("字典信息不能为空")
	}
	logger.Log.Infof("Update ss_dictionary: %+v", info)
	return db.Table("ss_dictionary").Save(info).Debug().Error
}

// FindIdentity ... tjstaffadmin
func FindIdentity(idname string) (*SsIDentity, error) {
	var err error
	// var u = SsInfoconfig{}
	var new_info SsIDentity
	var ret *gorm.DB
	if idname == "" {
		return nil, errors.New("idname is empty")
	}
	ret = db.Table("ss_identity").Clauses(clause.Locking{Strength: "UPDATE"}).Where("id_name = ?", idname).First(&new_info)

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}

	// new_info := infos
	new_info.IdCvalue = new_info.IdCvalue + new_info.IdIncvalue
	if idname == "reportnum" || idname == "nreportnum" || idname == "olreport" || idname == "olnotice" || idname == "ofreport" {
		year, _, _ := time.Now().Date()
		if year != new_info.IdOvalue {
			new_info.IdCvalue = 1
			new_info.IdOvalue = year
		}
	}

	// ret = db.Table("ss_identity").Save()
	// ret = db.Table(("ss_identity")).Where("ID_NAME = ?", idname).UpdateColumn("ID_CVALUE", gorm.Expr("ID_CVALUE + ID_INCVALUE"))
	ret = db.Table("ss_identity").Save(new_info).Debug()
	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}
	// infos.IDCVALUE = infos.IDCVALUE + infos.IDINCVALUE

	return &new_info, nil
}

// SaveIdentity ... tjstaffadmin
func SaveIdentity(newval *SsIDentity) error {
	// var err error
	if newval == nil {
		return errors.New("不能为空")
	}
	ret := db.Table("ss_identity").Save(newval).Debug()
	// ret := db.Exec("update ss_identity set id_cvalue = ? where id_name = ?", newval, idname).Debug()
	logger.Log.Infof("Update ss_identity: %+v", newval)
	return ret.Error
}

// FindSampleTypes ...
func FindSampleTypes(code string) ([]TjSampletype, error) {
	var err error
	// var u = SsInfoconfig{}
	var infos []TjSampletype
	var ret *gorm.DB
	if code == "" || code == "0" {
		ret = db.Table("tj_sampletype").Find(&infos)
	} else {
		ret = db.Table("tj_sampletype").Where("sample_code = ?", code).Find(&infos)
	}

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, err
	}

	return infos, nil
}
