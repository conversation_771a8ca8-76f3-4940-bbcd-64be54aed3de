package models

import (
	"errors"
	"utility/logger"

	"gorm.io/gorm"
)

// InfoModel ...
type InfoModel struct{}

// SsInfoconfig 常用信息配置表
type SsInfoconfig struct {
	ID       int    `gorm:"primary_key;auto_increment;column:id;type:int(11);not null" json:"id"`
	SsCode   string `gorm:"column:ss_code;type:varchar(45);not null" json:"ss_code"`
	SsName   string `gorm:"column:ss_name;type:varchar(65);not null" json:"ss_name"` // 配置信息名称
	SsPyjm   string `gorm:"column:ss_pyjm;type:varchar(45);not null" json:"ss_pyjm"`
	SsType   int    `gorm:"column:ss_type;type:int(4);not null" json:"ss_type"` // 级别
	SsParent string `gorm:"column:ss_parent;type:varchar(45);not null" json:"ss_parent"`
}

// TableName get sql table name.获取数据库表名
func (m *SsInfoconfig) TableName() string {
	return "ss_infoconfig"
}

// GetInfoConfigs ... tjstaffadmin
func (InfoModel) GetInfoConfigs(keyids []int) (*[]SsInfoconfig, error) {
	var err error
	// var u = SsInfoconfig{}
	var infos []SsInfoconfig
	var ret *gorm.DB
	if len(keyids) == 0 {
		ret = db.Find(&infos)
	} else {
		// err = db.Debug().Model(SsInfoconfig{}).Where("ss_type = ?", uno).Take(&u).Error
		ret = db.Table("ss_infoconfig").Where("ss_type in (?)", keyids).Find(&infos).Debug()
	}

	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}

	return &infos, nil
}

// GetInfoConfig ... GetInfoConfig
func (InfoModel) GetInfoConfig(scode string) (*SsInfoconfig, error) {
	var err error
	// var u = SsInfoconfig{}
	var info = SsInfoconfig{}
	// var ret *gorm.DB
	if scode == "" {
		return nil, errors.New("代码不能为空")
	}
	// err = db.Debug().Model(SsInfoconfig{}).Where("ss_type = ?", uno).Take(&u).Error
	// ret = db.Where("ss_code = ?", scode).Find(&infos)
	err = db.Table("ss_infoconfig").Debug().Model(SsInfoconfig{}).Where("ss_code = ?", scode).Take(&info).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}
	// if gorm.IsRecordNotFoundError(err) {
	// 	return &SsInfoconfig{}, errors.New("info Not Found by: " + scode)
	// }

	return &info, nil
}

// GetInfoConfigByName ... GetInfoConfigByName
func (InfoModel) GetInfoConfigByName(sname string) (*SsInfoconfig, error) {
	var err error
	// var u = SsInfoconfig{}
	var info = SsInfoconfig{}
	// var ret *gorm.DB
	if sname == "" {
		return nil, errors.New("名称不能为空")
	}
	// err = db.Debug().Model(SsInfoconfig{}).Where("ss_type = ?", uno).Take(&u).Error
	// ret = db.Where("ss_code = ?", sname).Find(&infos)
	err = db.Table("ss_infoconfig").Debug().Model(SsInfoconfig{}).Where("ss_name = ?", sname).Take(&info).Error
	// if gorm.IsRecordNotFoundError(err) {
	// 	return &SsInfoconfig{}, errors.New("info Not Found by: " + sname)
	// }
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}
	return &info, nil
}
