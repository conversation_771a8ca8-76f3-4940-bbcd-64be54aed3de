package models

import (
	"errors"
	"utility/logger"
)

// AudiogramModel ...
type AudiogramModel struct{}

// TjAudiogramdetail 电测听结果（频率， 耳朵、传到类型以及修正值等）
type TjAudiogramdetail struct {
	ID       int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjTestid string `gorm:"column:tj_testid;type:varchar(20);not null" json:"tj_testid"`          // 体检编号
	TjItemid string `gorm:"column:tj_itemid;type:varchar(20);not null" json:"tj_itemid"`          // 项目编号
	TjFreq   int    `gorm:"column:tj_freq;type:int(11);not null" json:"tj_freq"`                  // 频率
	TjAdtype int    `gorm:"column:tj_adtype;type:int(11);not null" json:"tj_adtype"`              // 传导类型 0：气导, 1:骨导
	TjEar    int    `gorm:"column:tj_ear;type:int(11);not null" json:"tj_ear"`                    // 耳朵 0:右耳 1:左耳
	TjResult int    `gorm:"column:tj_result;type:int(11);not null" json:"tj_result"`              // 结果值
	TjRevise int    `gorm:"column:tj_revise;type:int(11);not null" json:"tj_revise"`              // 校正后的结果
	TjCover  int    `gorm:"column:tj_cover;type:int(11);not null" json:"tj_cover"`                // 是否遮蔽
}

// TableName get sql table name.获取数据库表名
func (TjAudiogramdetail) TableName() string {
	return "tj_audiogramdetail"
}

// TjAudiogramresult 电测听结果（曲线和听阈）
type TjAudiogramresult struct {
	ID       int     `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjTestid string  `gorm:"column:tj_testid;type:varchar(20);not null" json:"tj_testid"`          // 体检编号
	TjAvgsgp float32 `gorm:"column:tj_avgsyp;type:float;not null" json:"tj_avgsyp"`                // 双高频平均听阈
	TjAvgyyp float32 `gorm:"column:tj_avgyyp;type:float;not null" json:"tj_avgyyp"`                // 右耳语频平均听阈
	TjAvgzyp float32 `gorm:"column:tj_avgzyp;type:float;not null" json:"tj_avgzyp"`                // 左耳语频平均听阈
	TjAvgsyp float32 `gorm:"column:tj_avgsgp;type:float;not null" json:"tj_avgsgp"`                // 双语频平均听阈
	TjAvgygp float32 `gorm:"column:tj_avgygp;type:float;not null" json:"tj_avgygp"`                // 右耳高频平均听阈
	TjAvgzgp float32 `gorm:"column:tj_avgzgp;type:float;not null" json:"tj_avgzgp"`                // 左耳高频平均听阈

	TjAvgsty float32 `gorm:"column:tj_avgsty;type:float;not null" json:"tj_avgsty"` // 双语加权
	TjAvgyty float32 `gorm:"column:tj_avgyty;type:float;not null" json:"tj_avgyty"` // 右耳加权
	TjAvgzty float32 `gorm:"column:tj_avgzty;type:float;not null" json:"tj_avgzty"` // 左耳加权
}

// TableName get sql table name.获取数据库表名
func (TjAudiogramresult) TableName() string {
	return "tj_audiogramresult"
}

// TjAudiogramrevise 电测听高频结果修正依据表
type TjAudiogramrevise struct {
	ID         int `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjFreq     int `gorm:"column:tj_freq;type:int(11);not null" json:"tj_freq"`                  // 频率
	TjSex      int `gorm:"column:tj_sex;type:int(11);not null" json:"tj_sex"`                    // 性别
	TjStartage int `gorm:"column:tj_startage;type:int(11);not null" json:"tj_startage"`          // 起始年龄
	TjEndage   int `gorm:"column:tj_endage;type:int(11);not null" json:"tj_endage"`              // 结束年龄
	TjRevise   int `gorm:"column:tj_revise;type:int(11);not null" json:"tj_revise"`              // 修正值
}

// TableName get sql table name.获取数据库表名
func (TjAudiogramrevise) TableName() string {
	return "tj_audiogramrevise"
}

// TjAudiogramsummary 电测听的小结内容
type TjAudiogramsummary struct {
	ID             int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjAdsummary    string `gorm:"column:tj_adsummary;type:varchar(200)" json:"tj_adsummary"`            // 电测听小结
	TjAdsuggestion string `gorm:"column:tj_adsuggestion;type:varchar(200)" json:"tj_adsuggestion"`      // 电测听建议
	TjIsabnormal   int    `gorm:"column:tj_isabnormal;type:int(4)" json:"tj_isabnormal"`                // 是否异常 0:异常 1：正常
}

// TableName get sql table name.获取数据库表名
func (TjAudiogramsummary) TableName() string {
	return "tj_audiogramsummary"
}

// QueryAudiogramDetails ...
func (AudiogramModel) QueryAudiogramDetails(dto *AudiogramDTO) (*[]TjAudiogramdetail, error) {
	var infos []TjAudiogramdetail
	ret := db.Table("tj_audiogramdetail").Where("1=1")
	if dto.Testid != "" {
		ret = ret.Where("tj_testid = ?", dto.Testid)
	}
	if dto.Freq > 0 {
		ret = ret.Where("tj_freq = ?", dto.Freq)
	}
	if dto.Ear >= 0 {
		ret = ret.Where("tj_ear = ?", dto.Ear)
	}
	if dto.Type >= 0 {
		ret = ret.Where("tj_adtype = ?", dto.Type)
	}

	err := ret.Debug().Order("tj_freq, tj_ear,tj_adtype").Find(&infos).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}
	return &infos, nil
}

// CreateAudiogramDetail ...
func (AudiogramModel) CreateAudiogramDetail(infos *[]TjAudiogramdetail) (*[]TjAudiogramdetail, error) {

	logger.Log.Infof("Save audiogram detail:%+v", infos)

	err := db.Table("tj_audiogramdetail").Create(infos).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}

	return infos, nil
}

// UpdateAudiogramDetail ...
func (AudiogramModel) UpdateAudiogramDetail(infos *[]TjAudiogramdetail) (*[]TjAudiogramdetail, error) {
	logger.Log.Infof("更新 audiogram detail:%+v", infos)
	err := db.Table("tj_audiogramdetail").Save(infos).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}

	return infos, nil
}

// DeleteAudiogramDetail ...
func (AudiogramModel) DeleteAudiogramDetail(testid string, ear int, adtype int) error {
	if testid == "" {
		return errors.New("体检号为空，不能删除")
	}
	ret := db.Table("tj_audiogramdetail").Where("tj_testid = ?", testid)
	if ear >= 0 {
		ret = ret.Where("tj_ear = ?", ear)
	}
	if adtype >= 0 {
		ret = ret.Where("tj_adtype = ?", adtype)
	}

	return ret.Debug().Delete(TjAudiogramdetail{}).Error

}

// audiogram result

// QueryAudiogramResult ...
func (AudiogramModel) QueryAudiogramResult(testid string) (*[]TjAudiogramresult, error) {
	var infos []TjAudiogramresult
	ret := db.Table("tj_audiogramresult").Where("1=1")
	if testid != "" {
		ret = ret.Where("tj_testid = ?", testid)
	}

	err := ret.Debug().Find(&infos).Error

	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}
	return &infos, nil
}

// CreateAudiogramResult ...
func (AudiogramModel) CreateAudiogramResult(infos *TjAudiogramresult) (*TjAudiogramresult, error) {

	logger.Log.Infof("Save audiogram Result:%+v", infos)
	err := db.Table("tj_audiogramresult").Create(infos).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}

	return infos, nil
}

// UpdateAudiogramResult ...
func (AudiogramModel) UpdateAudiogramResult(infos *TjAudiogramresult) (*TjAudiogramresult, error) {
	logger.Log.Infof("更新 audiogram Result:%+v", infos)
	err := db.Table("tj_audiogramresult").Save(infos).Error
	if err != nil {
		return nil, err
	}

	return infos, nil
}

// DeleteAudiogramResult ...
func (AudiogramModel) DeleteAudiogramResult(testid string) error {
	if testid == "" {
		return errors.New("体检号为空，不能删除")
	}
	ret := db.Table("tj_audiogramresult").Where("tj_testid = ?", testid)

	return ret.Debug().Delete(TjAudiogramdetail{}).Error

}

// ***************************** audiogram summary BEGIN *****************

// QueryAudiogramSummary ...
func (AudiogramModel) QueryAudiogramSummary() (*[]TjAudiogramsummary, error) {
	var infos []TjAudiogramsummary
	// ret := db.Table("tj_audiogramsummary").Where("1=1")

	err := db.Table("tj_audiogramsummary").Debug().Find(&infos).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}
	return &infos, nil
}

// CreateAudiogramSummary ...
func (AudiogramModel) CreateAudiogramSummary(infos *TjAudiogramsummary) (*TjAudiogramsummary, error) {

	logger.Log.Infof("Save tj_audiogramsummary:%+v", infos)
	err := db.Table("tj_audiogramsummary").Create(infos).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}

	return infos, nil
}

// UpdateAudiogramSummary ...
func (AudiogramModel) UpdateAudiogramSummary(infos *TjAudiogramsummary) (*TjAudiogramsummary, error) {
	logger.Log.Infof("更新 tj_audiogramsummary:%+v", infos)
	err := db.Table("tj_audiogramsummary").Save(infos).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}

	return infos, nil
}

// DeleteAudiogramSummary ...
func (AudiogramModel) DeleteAudiogramSummary(id int) error {
	if id <= 0 {
		return errors.New("ID为空，不能删除")
	}
	ret := db.Table("tj_audiogramsummary").Where("id = ?", id)

	return ret.Debug().Delete(TjAudiogramsummary{}).Error

}

// *********************** END ********************

// audiogram revise

// QueryAudiogramRevise ...
func (AudiogramModel) QueryAudiogramRevise() (*[]TjAudiogramrevise, error) {
	var infos []TjAudiogramrevise
	// ret := db.Table("tj_audiogramrevise").Where("1=1")

	err := db.Table("tj_audiogramrevise").Debug().Find(&infos).Error
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return nil, err
	}
	return &infos, nil
}
