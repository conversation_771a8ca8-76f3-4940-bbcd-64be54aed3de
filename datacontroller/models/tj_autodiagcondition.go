package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// TjAutodiagcondition 自动诊断条件(诊断该疾病的条件信息)
type TjAutodiagcondition struct {
	ID                int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"`          // primary key
	TjDisid           int    `gorm:"column:tj_disid;type:int(10);not null" json:"tj_disid"`                         // 所有疾病情况信息表
	TjHazardfactor    int    `gorm:"column:tj_hazardfactor;type:int(11);not null" json:"tj_hazardfactor"`           // 毒害因素
	TjMetype          int    `gorm:"column:tj_metype;type:int(11);not null" json:"tj_metype"`                       // 体检类别(上岗，离岗，在岗等)
	TjItemid          string `gorm:"column:tj_itemid;type:varchar(15);not null" json:"tj_itemid"`                   // 表达式编号
	TjOperator        string `gorm:"column:tj_operator;type:varchar(10);not null" json:"tj_operator"`               // 运算符
	TjRefvalue        string `gorm:"column:tj_refvalue;type:varchar(255);not null" json:"tj_refvalue"`              // 参考值
	TjConditionsymbol string `gorm:"column:tj_conditionsymbol;type:varchar(20);not null" json:"tj_conditionsymbol"` // 条件连接字符
	TjOrder           int    `gorm:"column:tj_order;type:int(11);not null" json:"tj_order"`                         // ccxh顺序
}

// TableName get sql table name.获取数据库表名
func (TjAutodiagcondition) TableName() string {
	return "tj_autodiagcondition"
}

// ********************  TjAutodiagcondition ********************************* BEGIN

// GetAutodiagcondition ...
func (DiseaseModel) GetAutodiagcondition(dto *AutodiagDTO) (*[]TjAutodiagcondition, error) {
	var infos []TjAutodiagcondition
	var ret *gorm.DB

	ret = db.Table("tj_autodiagcondition").Where("1 = 1")

	if len(dto.Disids) > 0 {
		ret = ret.Where("tj_disid in (?)", dto.Disids)
	}
	if len(dto.Itemids) > 0 {
		ret = ret.Where("tj_itemid in (?)", dto.Itemids)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// InsertAutodiagcondition ...
func (DiseaseModel) InsertAutodiagcondition(info *TjAutodiagcondition) (*TjAutodiagcondition, error) {
	logger.Log.Infof("Save tj_autodiagcondition:%+v", info)
	result := db.Table("tj_autodiagcondition").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateAutodiagcondition ...
func (DiseaseModel) UpdateAutodiagcondition(info *TjAutodiagcondition) error {

	logger.Log.Infof("update tj_autodiagcondition:%+v", info)
	result := db.Table("tj_autodiagcondition").Debug().Save(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return result.Error
	}
	return nil
}

// DeleteAutodiagcondition ...
func (DiseaseModel) DeleteAutodiagcondition(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_autodiagcondition where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjAutodiagcondition ********************************* END
