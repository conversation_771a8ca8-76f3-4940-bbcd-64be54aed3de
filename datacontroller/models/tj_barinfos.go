package models

import (
	"fmt"
	"utility/logger"

	"gorm.io/gorm"
)

// BarModel ...
type BarModel struct{}

// TjBarnameinfo 条码类别名称表
type TjBarnameinfo struct {
	ID         int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjBarnum   string `gorm:"column:tj_barnum;type:varchar(20);not null" json:"tj_barnum"`          // 编号
	TjBarname  string `gorm:"column:tj_barname;type:varchar(40);not null" json:"tj_barname"`        // 名称
	TjBardesc  string `gorm:"column:tj_bardesc;type:varchar(200);not null" json:"tj_bardesc"`       // 描述
	TjBarorder int    `gorm:"column:tj_barorder;type:int(11);not null" json:"tj_barorder"`          // 顺序
}

// TableName get sql table name.获取数据库表名
func (TjBarnameinfo) TableName() string {
	return "tj_barnameinfo"
}

// TjBardetail 条码内容详细信息表
type TjBardetail struct {
	ID         int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjBinum    string `gorm:"column:tj_binum;type:varchar(16);not null" json:"tj_binum"`            // 条码内容编号
	TjItemid   string `gorm:"column:tj_itemid;type:varchar(20);not null" json:"tj_itemid"`          // 项目编号
	TjBarorder int    `gorm:"column:tj_barorder;type:int(11);not null" json:"tj_barorder"`          // 顺序
}

// TableName get sql table name.获取数据库表名
func (TjBardetail) TableName() string {
	return "tj_bardetail"
}

// TjBaritems 条码内容表
type TjBaritems struct {
	ID         int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjBinum    string `gorm:"column:tj_binum;type:varchar(16);not null" json:"tj_binum"`            // 内容编号
	TjBiname   string `gorm:"column:tj_biname;type:varchar(200);not null" json:"tj_biname"`         // 内容名称
	TjBarnum   string `gorm:"column:tj_barnum;type:varchar(20);not null" json:"tj_barnum"`          // 条码类别编号
	TjBarflag  int    `gorm:"column:tj_barflag;type:int(11);not null" json:"tj_barflag"`            // 是否打印条码
	TjBarorder int    `gorm:"column:tj_barorder;type:int(11);not null" json:"tj_barorder"`
}

// TableName get sql table name.获取数据库表名
func (TjBaritems) TableName() string {
	return "tj_baritems"
}

// ********************  TjBarnameinfo ********************************* BEGIN

// GetBarnameinfo ...
func (BarModel) GetBarnameinfo(pnum string) (*[]TjBarnameinfo, error) {
	var infos []TjBarnameinfo
	var ret *gorm.DB

	ret = db.Table("tj_barnameinfo").Where("1 = 1")

	if pnum != "" && pnum != "0" {
		ret = ret.Where("tj_barnum = ?", pnum)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// InsertBarnameinfo ...
func (BarModel) InsertBarnameinfo(info *TjBarnameinfo) (*TjBarnameinfo, error) {
	logger.Log.Infof("Save tj_barnameinfo:%+v", info)
	result := db.Table("tj_barnameinfo").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateBarnameinfo ...
func (BarModel) UpdateBarnameinfo(info *TjBarnameinfo) error {

	logger.Log.Infof("update tj_barnameinfo:%+v", info)
	result := db.Table("tj_barnameinfo").Debug().Save(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return result.Error
	}
	return nil
}

// DeleteBarnameinfo ...
func (BarModel) DeleteBarnameinfo(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_barnameinfo where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjBarnameinfo ********************************* END

// ********************  TjBardetail ********************************* BEGIN

// GetBardetail ...
func (BarModel) GetBardetail(dto *BardetailDTO) (*[]TjBardetail, error) {
	var infos []TjBardetail
	var ret *gorm.DB

	ret = db.Table("tj_bardetail").Where("1 = 1")

	if len(dto.Binum) > 0 {
		ret = ret.Where("tj_binum in ?", dto.Binum)
	}

	if len(dto.Itemid) > 0 {
		ret = ret.Where("tj_itemid in ?", dto.Itemid)
	}
	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// InsertBardetail ...
func (BarModel) InsertBardetail(info *TjBardetail) (*TjBardetail, error) {
	logger.Log.Infof("Save tj_bardetail:%+v", info)
	result := db.Table("tj_bardetail").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateBardetail ...
func (BarModel) UpdateBardetail(info *TjBardetail) error {

	logger.Log.Infof("update tj_bardetail:%+v", info)
	result := db.Table("tj_bardetail").Debug().Save(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return result.Error
	}
	return nil
}

// DeleteBardetail ...
func (BarModel) DeleteBardetail(dto *BardetailDTO) error {
	logger.Log.Infof("删除ID:%+v", dto)
	// .Infof("删除ID:%+v", dto)
	fmt.Printf("删除ID:%+v", dto)
	var ret *gorm.DB
	ret = db.Table("tj_bardetail").Where("1 = 1")
	if len(dto.Binum) > 0 {
		ret = ret.Where("tj_binum in (?)", dto.Binum)
	}
	if len(dto.Itemid) > 0 {
		ret = ret.Where("tj_itemid in (?)", dto.Itemid)
	}

	result := ret.Delete(&TjBardetail{}).Debug()
	// ret := db.Exec("delete from tj_bardetail where id = ?", id).Debug()

	return result.Error

}

// ********************  TjBardetail ********************************* END

// ********************  TjBaritems ********************************* BEGIN

// GetBaritems ...
func (BarModel) GetBaritems(barnum string) (*[]TjBaritems, error) {
	var infos []TjBaritems
	var ret *gorm.DB

	ret = db.Table("tj_baritems").Where("1 = 1")

	if barnum != "" && barnum != "0" {
		ret = ret.Where("tj_barnum = ?", barnum)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// InsertBaritems ...
func (BarModel) InsertBaritems(info *TjBaritems) (*TjBaritems, error) {
	logger.Log.Infof("Save tj_baritems:%+v", info)
	result := db.Table("tj_baritems").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateBaritems ...
func (BarModel) UpdateBaritems(info *TjBaritems) error {

	logger.Log.Infof("update tj_baritems:%+v", info)
	result := db.Table("tj_baritems").Debug().Save(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return result.Error
	}
	return nil
}

// DeleteBaritems ...
func (BarModel) DeleteBaritems(binum string) error {
	logger.Log.Infof("删除tj_baritemsID:%d", binum)
	ret := db.Exec("delete from tj_baritems where tj_binum = ?", binum).Debug()

	return ret.Error

}

// ********************  Baritems ********************************* END
