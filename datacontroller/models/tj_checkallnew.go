package models

import (
	"errors"
	"utility/logger"
)

// TjCheckallnew 个人体检总检表（新）
type TjCheckallnew struct {
	ID              int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjTestid        string `gorm:"column:tj_testid;type:varchar(20);not null" json:"tj_testid"`          // 检查编号
	TjTypeid        int    `gorm:"column:tj_typeid;type:int(11);not null" json:"tj_typeid"`              // 结果类型编号 0：正常，1：需复查，2禁忌， 3：疑似
	TjDiscode       string `gorm:"column:tj_discode;type:varchar(50);not null" json:"tj_discode"`
	TjItemcode      string `gorm:"column:tj_itemcode;type:varchar(50);not null" json:"tj_itemcode"`
	TjHazardcode    string `gorm:"column:tj_hazardcode;type:varchar(50);not null" json:"tj_hazardcode"`    //毒害因素代码
	TjDiseasename   string `gorm:"column:tj_diseasename;type:varchar(200);not null" json:"tj_diseasename"` // 疾病名称(针对禁忌等)
	TjOcuabnormal   string `gorm:"column:tj_ocuabnormal;type:longtext;not null" json:"tj_ocuabnormal"`     // 职业异常指标
	TjOthabnormal   string `gorm:"column:tj_othabnormal;type:longtext;not null" json:"tj_othabnormal"`     // 其他异常指标
	TjOcuconclusion string `gorm:"column:tj_ocuconclusion;type:longtext;not null" json:"tj_ocuconclusion"` // 职业相关结论
	TjOthconclusion string `gorm:"column:tj_othconclusion;type:longtext;not null" json:"tj_othconclusion"` // 其他结论
	TjOcusuggestion string `gorm:"column:tj_ocusuggestion;type:longtext;not null" json:"tj_ocusuggestion"` // 职业处理意见
	TjOthsuggestion string `gorm:"column:tj_othsuggestion;type:longtext;not null" json:"tj_othsuggestion"` // 其他处理意见
	TjOcuopinion    string `gorm:"column:tj_ocuopinion;type:longtext;not null" json:"tj_ocuopinion"`       // 职业相关医学建议
	TjOthopinion    string `gorm:"column:tj_othopinion;type:longtext;not null" json:"tj_othopinion"`       // 其他医学建议
	TjStaffid       int    `gorm:"column:tj_staffid;type:int(10);not null" json:"tj_staffid"`              // 总检医生编码
	TjCheckdate     int64  `gorm:"column:tj_checkdate;type:bigint(20);not null" json:"tj_checkdate"`
	TjCastatus      int    `gorm:"column:tj_castatus;type:int(10);not null" json:"tj_castatus"` // 总检状态，0：未总检 1：已总检
}

// TableName get sql table name.获取数据库表名
func (TjCheckallnew) TableName() string {
	return "tj_checkallnew"
}

// CreateCheckAll ...
func CreateCheckAll(info *TjCheckallnew) (*TjCheckallnew, error) {
	if info == nil {
		return nil, errors.New("tj_checkallnew model is empty")
	}

	result := db.Table("tj_checkallnew").Debug().Create(info)

	logger.Log.Infof("Insert tj_checkallnew: %+v", info)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateCheckAll ...
func UpdateCheckAll(info *TjCheckallnew) (*TjCheckallnew, error) {
	if info == nil {
		return nil, errors.New("tj_checkallnew model is empty")
	}

	result := db.Table("tj_checkallnew").Debug().Save(info)

	logger.Log.Infof("Update tj_checkallnew: %+v", info)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// GetCheckall ...
func GetCheckall(dto *CaQueryDTO) ([]TjCheckallnew, error) {
	if dto == nil {
		return nil, errors.New("查找条件为空")
	}

	var infos []TjCheckallnew
	ret := db.Table("tj_checkallnew").Where("1 = 1")
	if len(dto.Testids) > 0 {
		ret = ret.Where("tj_testid in (?)", dto.Testids)
	}
	if dto.Stdate != "" && dto.Stdate != "0" {
		ret = ret.Where("tj_checkdate > ?", dto.Stdate)
	}
	if dto.Enddate != "" && dto.Enddate != "0" {
		ret = ret.Where("tj_checkdate < ?", dto.Enddate)
	}
	if dto.CaStatus != -1 {
		ret = ret.Where("tj_castatus = ?", dto.CaStatus)
	}
	if dto.RetType != -1 {
		ret = ret.Where("tj_typeid = ?", dto.RetType)
	}

	result := ret.Find(&infos)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}
	return infos, nil
}

// DeleteCheckAll ...
func DeleteCheckAll(info *TjCheckallnew) (*TjCheckallnew, error) {
	if info == nil {
		return nil, errors.New("tj_checkallnew model is empty")
	}

	result := db.Table("tj_checkallnew").Debug().Save(info)

	logger.Log.Infof("Update tj_checkallnew: %+v", info)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}
