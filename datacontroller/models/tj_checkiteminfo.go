package models

import (
	"errors"
	"utility/logger"

	"gorm.io/gorm"
)

// TjCheckiteminfo 患者体检项目明细与以及体检结果表
type TjCheckiteminfo struct {
	ID              int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"`     // primary key
	TjTestid        string `gorm:"column:tj_testid;type:varchar(20);not null" json:"tj_testid"`              // 检查编号
	TjItemid        string `gorm:"column:tj_itemid;type:varchar(20);not null" json:"tj_itemid"`              // 体检项目编号
	TjSynid         string `gorm:"column:tj_synid;type:varchar(12);not null" json:"tj_synid"`                // 项目组合编号
	TjDeptid        string `gorm:"column:tj_deptid;type:varchar(10);not null" json:"tj_deptid"`              // 科室编码
	TjItemname      string `gorm:"column:tj_itemname;type:varchar(60);not null" json:"tj_itemname"`          // 体检项目名称
	TjResult        string `gorm:"column:tj_result;type:varchar(500);not null" json:"tj_result"`             // 体检结果
	TjItemrange     string `gorm:"column:tj_itemrange;type:varchar(20);not null" json:"tj_itemrange"`        // 查看范围
	TjItemunit      string `gorm:"column:tj_itemunit;type:varchar(30);not null" json:"tj_itemunit"`          // 项目单位
	TjAbnormalflag  int    `gorm:"column:tj_abnormalflag;type:int(11);not null" json:"tj_abnormalflag"`      // 是否异常的标志
	TjBarflag       int    `gorm:"column:tj_barflag;type:int(11);not null" json:"tj_barflag"`                // 打印条码标志(检验科项目)
	TjAbnormalshow  string `gorm:"column:tj_abnormalshow;type:varchar(100);not null" json:"tj_abnormalshow"` // 异常提示
	TjCombineflag   int    `gorm:"column:tj_combineflag;type:int(11);not null" json:"tj_combineflag"`        // 组合标志
	TjDeptorder     int    `gorm:"column:tj_deptorder;type:int(10);not null" json:"tj_deptorder"`
	TjShoworder     int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`       // 显示顺序
	TjCombineorder  int    `gorm:"column:tj_combineorder;type:int(11);not null" json:"tj_combineorder"` // 组合顺序
	TjBarnum        string `gorm:"column:tj_barnum;type:varchar(10);not null" json:"tj_barnum"`
	TjBarcode       string `gorm:"column:tj_barcode;type:varchar(20);not null" json:"tj_barcode"`
	TjCheckdate     int64  `gorm:"column:tj_checkdate;type:bigint(20);not null" json:"tj_checkdate"`           // 检查日期
	TjCheckdoctor   string `gorm:"column:tj_checkdoctor;type:varchar(100);not null" json:"tj_checkdoctor"`     // 检查医生(直接用名字显示)
	TjRecheckdoctor string `gorm:"column:tj_recheckdoctor;type:varchar(100);not null" json:"tj_recheckdoctor"` // 复查医生(直接用名字显示)
	TjRecheckdate   int64  `gorm:"column:tj_recheckdate;type:bigint(20);not null" json:"tj_recheckdate"`       // 复查日期
}

// TableName 会将 struct 的表名重写为 `string`
func (TjCheckiteminfo) TableName() string {
	return "tj_checkiteminfo"
}

// CreateCheckitemInfo ... insert new TjCheckiteminfo
func CreateCheckitemInfo(info *TjCheckiteminfo) (*TjCheckiteminfo, error) {
	if info == nil {
		return nil, errors.New("TjCheckiteminfo model is empty")
	}
	// logger.Log.Println("开始gorm create......")
	result := db.Table("tj_checkiteminfo").Debug().Create(info)
	// logger.Log.Println("完成gorm create......")

	// logger.Log.Printf("insert medexaminfo ID: [%d]", info.ID)
	// logger.Log.Printf("insert medexaminfo: [%+v]", info)

	logger.Log.Infof("Insert TjCheckiteminfo: %+v", info)

	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// CreateCheckitemInfos ... insert new TjCheckiteminfo
func CreateCheckitemInfos(info *[]TjCheckiteminfo) (*[]TjCheckiteminfo, error) {
	if info == nil {
		return nil, errors.New("TjCheckiteminfo model is empty")
	}

	if len(*info) <= 0 {
		return nil, errors.New("empty data")
	}
	// logger.Log.Infof("Insert TjCheckiteminfo: %+v", info)
	// var err error
	// var retinfo []TjCheckiteminfo

	// err = db.Table("tj_checkiteminfo").CreateInBatches(*info, len(*info)).Error
	// db.Table("tj_checkiteminfo").CreateInBatches()

	tx := db.CreateInBatches(*info, 100)
	if tx.Error != nil {
		logger.Log.Errorf("Insert TjCheckiteminfo err: %+v", tx.Error)
		return nil, tx.Error
	}

	// for _, v := range *info {
	// 	err = db.Table("tj_checkiteminfo").Create(&v).Error
	// 	if err != nil {
	// 		break
	// 	}
	// 	retinfo = append(retinfo, v)
	// }

	// if err != nil {
	// 	logger.Log.Errorf("Insert TjCheckiteminfo err: %+v", err)
	// 	return nil, err
	// }

	return info, nil
}

// SaveCheckitemInfo ... update TjCheckiteminfo
func SaveCheckitemInfo(info *[]TjCheckiteminfo) (*[]TjCheckiteminfo, error) {
	if info == nil {
		return nil, errors.New("TjCheckiteminfo model is empty")
	}

	if len(*info) <= 0 {
		return nil, errors.New("empty data")
	}

	logger.Log.Infof("Update TjCheckiteminfo: %+v", info)
	result := db.Table("tj_checkiteminfo").Save(*info).Debug()

	// logger.Log.Printf("insert medexaminfo ID: [%d]", info.ID)
	// logger.Log.Printf("insert medexaminfo: [%+v]", info)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// FindCheckiteminfos ... FindByTestID
func FindCheckiteminfos(dto *CiQueryDTO) ([]TjCheckiteminfo, error) {
	// func FindMedinfo(query *service.MedinfoDTO) (*[]TjMedexaminfo, error)

	if dto == nil {
		return nil, errors.New("nil query condition")
	}
	var infos []TjCheckiteminfo
	var ret *gorm.DB

	ret = db.Table("tj_checkiteminfo").Where("1=1")

	if len(dto.Testid) > 0 {
		ret = ret.Where("tj_testid in (?)", dto.Testid)
	}

	if len(dto.Deptid) > 0 {
		ret = ret.Where("tj_deptid in (?)", dto.Deptid)
	}

	if dto.Barcode != "" {
		ret = ret.Where("tj_barcode = ?", dto.Barcode)
	}

	if dto.Combined != -1 {
		ret = ret.Where("tj_combineflag = ?", dto.Combined)
	}

	if dto.Flag != -1 {
		ret = ret.Where("tj_abnormalflag = ?", dto.Flag)
	}

	ret = ret.Order("tj_synid, tj_showorder")
	// ret = ret.Where("tj_checkstatus <> -1")

	// logger.Log.Debugf("%+v", )
	ret.Debug().Find(&infos)
	return infos, ret.Error
}

// FindCheckiteminfos ... FindByTestID
func FindCheckiteminfosByTestid(testid string) ([]TjCheckiteminfo, error) {

	if testid == "" {
		return nil, errors.New("nil query condition")
	}
	var infos []TjCheckiteminfo
	var ret *gorm.DB

	ret = db.Table("tj_checkiteminfo").Where("tj_testid = ?", testid)
	ret = ret.Order("tj_synid, tj_showorder")
	ret.Debug().Find(&infos)
	return infos, ret.Error
}

// DeleteCheckiteminfos ... FindByTestID
func DeleteCheckiteminfos(dto *CiDeleteDTO) error {
	if dto == nil {
		return errors.New("nil query condition")
	}
	var ret *gorm.DB

	ret = db.Table("tj_checkiteminfo").Where("tj_testid = ?", dto.Testid)
	if dto.Synid != "" {
		ret = ret.Where("tj_synid = ?", dto.Synid)
	}

	result := ret.Delete(TjCheckiteminfo{}).Debug()
	return result.Error
}
