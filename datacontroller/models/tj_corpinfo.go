package models

import (
	"log"
	"utility/logger"
)

// CorpinfoModel ...
type CorpinfoModel struct{}

// TjCorpinfo 体检单位信息表
type TjCorpinfo struct {
	ID             int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjCorpid       string `gorm:"column:tj_corpid;type:varchar(30);not null" json:"tj_corpid"`          // 公司编号
	TjCorpname     string `gorm:"column:tj_corpname;type:varchar(100);not null" json:"tj_corpname"`     // 公司名字
	TjContactor    string `gorm:"column:tj_contactor;type:varchar(40);not null" json:"tj_contactor"`    // 公司联系人
	TjPrinciple    string `gorm:"column:tj_principle;type:varchar(80);not null" json:"tj_principle"`    // 负责人
	TjPhone        string `gorm:"column:tj_phone;type:varchar(32);not null" json:"tj_phone"`            // 联系电话
	TjFax          string `gorm:"column:tj_fax;type:varchar(32);not null" json:"tj_fax"`                // 传真
	TjAreacode     string `gorm:"column:tj_areacode;type:varchar(45);not null" json:"tj_areacode"`
	TjAddress      string `gorm:"column:tj_address;type:varchar(256);not null" json:"tj_address"`    // 地址
	TjPostcode     string `gorm:"column:tj_postcode;type:varchar(20);not null" json:"tj_postcode"`   // 邮编
	TjIndustry2    string `gorm:"column:tj_industry2;type:varchar(10);not null" json:"tj_industry2"` // 所属行业，先手工录入
	TjEconomic2    string `gorm:"column:tj_economic2;type:varchar(10);not null" json:"tj_economic2"`
	TjPyjm         string `gorm:"column:tj_pyjm;type:varchar(60);not null" json:"tj_pyjm"`
	TjZdym         string `gorm:"column:tj_zdym;type:varchar(60);not null" json:"tj_zdym"`
	TjOperator     int    `gorm:"column:tj_operator;type:int(10);not null" json:"tj_operator"` // 操作员
	TjAdddate      int64  `gorm:"column:tj_adddate;type:bigint(20);not null" json:"tj_adddate"`
	TjTesttype     int    `gorm:"column:tj_testtype;type:int(11);not null" json:"tj_testtype"`     // 体检类型
	TjPassword     string `gorm:"column:tj_password;type:varchar(50);not null" json:"tj_password"` // 查询密码
	TjMail         string `gorm:"column:tj_mail;type:varchar(30);not null" json:"tj_mail"`         // 邮箱
	TjMemo         string `gorm:"column:tj_memo;type:varchar(256);not null" json:"tj_memo"`
	TjOrgcode      string `gorm:"column:tj_orgcode;type:varchar(45);not null" json:"tj_orgcode"` // 组织机构代码
	TjGbcode       string `gorm:"column:tj_gbcode;type:varchar(45);not null" json:"tj_gbcode"`
	TjEcotypeH     int    `gorm:"column:tj_ecotypeh;type:int(4);not null" json:"tj_ecotypeh"`
	TjEcotype      int    `gorm:"column:tj_ecotype;type:int(4);not null" json:"tj_ecotype"`
	TjIndustryH    int    `gorm:"column:tj_industryh;type:int(4);not null" json:"tj_industryh"`
	TjIndustry     int    `gorm:"column:tj_industry;type:int(4);not null" json:"tj_industry"`
	TjSecondcode   string `gorm:"column:tj_secondcode;type:varchar(45);not null" json:"tj_secondcode"`
	TjSecondname   string `gorm:"column:tj_secondname;type:varchar(100);not null" json:"tj_secondname"`
	TjCorpscale    int    `gorm:"column:tj_corpscale;type:int(4);not null" json:"tj_corpscale"`
	TjTotal        int    `gorm:"column:tj_total;type:int(4);not null" json:"tj_total"`
	TjTotalF       int    `gorm:"column:tj_totalf;type:int(4);not null" json:"tj_totalf"`
	TjOperationer  int    `gorm:"column:tj_operationer;type:int(4);not null" json:"tj_operationer"`
	TjOperationerF int    `gorm:"column:tj_operationerf;type:int(4);not null" json:"tj_operationerf"`
	TjHazarders    int    `gorm:"column:tj_hazarders;type:int(4);not null" json:"tj_hazarders"`
	TjHazardersF   int    `gorm:"column:tj_hazardersf;type:int(4);not null" json:"tj_hazardersf"`
	TjSyncflag     int    `gorm:"column:tj_syncflag;type:tinyint(1);not null" json:"tj_syncflag"`       // 数据提交状态 0：未同步，1：已同步
	TjStatus       int    `gorm:"column:tj_status;type:tinyint(1);not null" json:"tj_status"`           // 企业状态 0：删除 1：正常
	TjMonitortype  string `gorm:"column:tj_monitortype;type:varchar(8);not null" json:"tj_monitortype"` // 监测类型
}

// TableName get sql table name.获取数据库表名
func (TjCorpinfo) TableName() string {
	return "tj_corpinfo"
}

// TjCorpmedexaminfo 单位体检信息表
type TjCorpmedexaminfo struct {
	ID            int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjCorpnum     int    `gorm:"column:tj_corpnum;type:int(10);not null" json:"tj_corpnum"`
	TjCorpname    string `gorm:"column:tj_corpname;type:varchar(50);not null" json:"tj_corpname"`      // 单位编码
	TjPyjm        string `gorm:"column:tj_pyjm;type:varchar(50);not null" json:"tj_pyjm"`              // 拼音简码
	TjTesttype    int    `gorm:"column:tj_testtype;type:int(11);not null" json:"tj_testtype"`          // 体检类型
	TjStatus      int    `gorm:"column:tj_status;type:int(11);not null" json:"tj_status"`              // 体检状态 体检状态 1：预约 2：已登记 3：体检中 4：结束 5:已打印报告，0:平台推送默认状态，-1：预约失败
	TjTesters     int    `gorm:"column:tj_testers;type:int(11);not null" json:"tj_testers"`            // 体检人数
	TjChecked     int    `gorm:"column:tj_checked;type:int(11);not null" json:"tj_checked"`            // 已检查人数 (暂时不用)
	TjReportnum   string `gorm:"column:tj_reportnum;type:varchar(20);not null" json:"tj_reportnum"`    // 报告编号
	TjStaffid     int    `gorm:"column:tj_staffid;type:int(10);not null" json:"tj_staffid"`            // 登记人员ID
	TjAptnum      int    `gorm:"column:tj_aptnum;type:bigint(20);not null" json:"tj_aptnum"`           // 平台推送过来的预约编号
	TjNotifystate int    `gorm:"column:tj_notifystate;type:tinyint(1);not null" json:"tj_notifystate"` // 已通知平台状态，对应上面tj_status 状态，默认预约状态已通知  1
	TjTestdate    int    `gorm:"column:tj_testdate;type:bigint(20);not null" json:"tj_testdate"`       // 体检日期
	TjRecdate     int    `gorm:"column:tj_recdate;type:bigint(20);not null" json:"tj_recdate"`         // 预约日期
}

// TableName get sql table name.获取数据库表名
func (TjCorpmedexaminfo) TableName() string {
	return "tj_corpmedexaminfo"
}

// CreateCorpinfo ... CreateCorpinfo
func (CorpinfoModel) CreateCorpinfo(info *TjCorpinfo) (*TjCorpinfo, error) {

	logger.Log.Infof("添加企业信息: %+v", info)
	ret := db.Table("tj_corpinfo").Debug().Create(info)
	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, ret.Error
	}

	return info, nil
}

// UpdateCorpinfo ... UpdateCorpinfo
func (CorpinfoModel) UpdateCorpinfo(info *TjCorpinfo) error {

	logger.Log.Infof("更新企业信息: %+v", info)
	log.Printf("更新企业信息:%+v\n", info)
	ret := db.Table("tj_corpinfo").Debug().Save(info)

	return ret.Error
}

// QueryCorpinfo ...
func (CorpinfoModel) QueryCorpinfobyID(corpid string, corpnum string) (*TjCorpinfo, error) {
	// var ret gorm.DB
	var infos TjCorpinfo

	ret := db.Table("tj_corpinfo").Where("tj_status > 0")
	if corpid != "" {
		ret = ret.Where("ID = ?", corpid)
	}

	if corpnum != "" {
		ret = ret.Where("tj_corpid = ?", corpnum)
	}

	rt := ret.Find(&infos)
	if rt.Error != nil {
		logger.Log.Errorf("错误:%+v", rt.Error)
		return nil, rt.Error
	}
	return &infos, nil
}

// QueryCorpinfo ...
func (CorpinfoModel) QueryCorpinfo(dto *CorpQueryDTO) (*[]TjCorpinfo, error) {
	// var ret gorm.DB
	var infos []TjCorpinfo

	ret := db.Table("tj_corpinfo").Where("tj_status > 0")
	if len(dto.Ids) > 0 {
		ret = ret.Where("ID in (?)", dto.Ids)
	}

	if dto.Type == 0 {
		ret = ret.Where("tj_corpid != '09999'")
	}

	if dto.CorpID != "" {
		ret = ret.Where("tj_corpid = ?", dto.CorpID)
	}

	if dto.CorpName != "" {
		ret = ret.Where("tj_corpname = ?", dto.CorpName)
	}

	rt := ret.Debug().Find(&infos)
	if rt.Error != nil {
		logger.Log.Errorf("错误:%+v", rt.Error)
		return nil, rt.Error
	}
	return &infos, nil
}

// DeleteCorpinfo ... DeleteCorpinfo
func (CorpinfoModel) DeleteCorpinfo(id int) error {

	logger.Log.Infof("删除企业信息, id%d", id)
	ret := db.Exec("update tj_corpinfo set tj_status = 0 where ID = ?", id).Debug()

	return ret.Error
}

// QueryCorpMedinfo ...
func (CorpinfoModel) QueryCorpMedinfo(dto *CorpMedQuery) (*[]TjCorpmedexaminfo, error) {
	// var ret gorm.DB
	var infos []TjCorpmedexaminfo

	ret := db.Table("tj_corpmedexaminfo").Where("1 = 1")
	if dto.Stdate > 0 {
		ret = ret.Where("tj_testdate >= ?", dto.Stdate)
	}

	if dto.Enddate > 0 {
		ret = ret.Where("tj_testdate <= ?", dto.Enddate)
	}

	if dto.Corpnum > 0 {
		if dto.Corpnum == 2 {
			ret = ret.Where("tj_corpnum > ?", dto.Corpnum)
		} else {
			ret = ret.Where("tj_corpnum = ?", dto.Corpnum)
		}
	}

	if dto.Status > -2 {
		ret = ret.Where("tj_status = ?", dto.Status)
	}

	if dto.HidePersonal == 1 {
		ret = ret.Where("ID > 2")
	}

	rt := ret.Debug().Find(&infos)
	if rt.Error != nil {
		logger.Log.Errorf("错误:%+v", rt.Error)
		return nil, rt.Error
	}
	return &infos, nil
}

// CreateCorpMedinfo ...
func (CorpinfoModel) CreateCorpMedinfo(info *TjCorpmedexaminfo) (*TjCorpmedexaminfo, error) {

	logger.Log.Infof("添加企业体检信息: %+v", info)
	ret := db.Table("tj_corpmedexaminfo").Debug().Create(info)
	if ret.Error != nil {
		logger.Log.Errorf("错误:%+v", ret.Error)
		return nil, ret.Error
	}

	return info, nil
}

// UpdateCorpMedinfo ...
func (CorpinfoModel) UpdateCorpMedinfo(info *TjCorpmedexaminfo) error {

	logger.Log.Infof("更新企业体检信息: %+v", info)
	ret := db.Table("tj_corpmedexaminfo").Debug().Save(info)

	return ret.Error
}

// DeleteCorpMedinfo ...
func (CorpinfoModel) DeleteCorpMedinfo(id int) error {

	logger.Log.Infof("删除企业信息, id%d", id)
	ret := db.Exec("update tj_corpmedexaminfo set tj_status = 0 where ID = ?", id).Debug()

	return ret.Error
}
