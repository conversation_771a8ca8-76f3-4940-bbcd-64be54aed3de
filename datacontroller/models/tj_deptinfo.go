package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// DeptModel ...
type DeptModel struct{}

// TjDepartinfo 科室代码
type TjDepartinfo struct {
	ID            int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjDeptid      string `gorm:"unique;column:tj_deptid;type:varchar(10);not null" json:"tj_deptid"`   // 科室编号
	TjDeptname    string `gorm:"column:tj_deptname;type:varchar(40);not null" json:"tj_deptname"`      // 科室名称
	TjShoworder   int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`        // 显示顺序
	TjDepttype    int    `gorm:"column:tj_depttype;type:int(11);not null" json:"tj_depttype"`          // 科室类型（检查，检验与功能）
	TjDeptinfo    string `gorm:"column:tj_deptinfo;type:varchar(100);not null" json:"tj_deptinfo"`     // department information
	TjPricinple   int    `gorm:"column:tj_pricinple;type:int(10);not null" json:"tj_pricinple"`        // 科室负责人
	TjDeptaddr    string `gorm:"column:tj_deptaddr;type:varchar(45);not null" json:"tj_deptaddr"`
	TjPyjm        string `gorm:"column:tj_pyjm;type:varchar(20);not null" json:"tj_pyjm"`
	TjZdym        string `gorm:"column:tj_zdym;type:varchar(20);not null" json:"tj_zdym"`
	TjFlag        int    `gorm:"column:tj_flag;type:int(11);not null" json:"tj_flag"`               // 有效无效标志
	TjOperator    int    `gorm:"column:tj_operator;type:int(10);not null" json:"tj_operator"`       // 最后修改人
	TjModdate     int64  `gorm:"column:tj_moddate;type:bigint(20);not null" json:"tj_moddate"`      // 修改日期
	TjDiagtype    int    `gorm:"column:tj_diagtype;type:int(11);not null" json:"tj_diagtype"`       // 小结类型 自动、手动
	TjReportmode  int    `gorm:"column:tj_reportmode;type:int(11);not null" json:"tj_reportmode"`   // 报告格式
	TjReportorder int    `gorm:"column:tj_reportorder;type:int(11);not null" json:"tj_reportorder"` // 报告顺序
	TjSex         int    `gorm:"column:tj_sex;type:int(11);not null" json:"tj_sex"`                 // 适用性别 0：全部 1：男性 2：女性
}

// TableName get sql table name.获取数据库表名
func (m *TjDepartinfo) TableName() string {
	return "tj_departinfo"
}

// TjDeptitem 科室与项目关系表
type TjDeptitem struct {
	ID          int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjItemid    string `gorm:"unique;column:tj_itemid;type:varchar(12);not null" json:"tj_itemid"`
	TjDeptid    string `gorm:"column:tj_deptid;type:varchar(10);not null" json:"tj_deptid"`
	TjShoworder int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`
}

// TableName get sql table name.获取数据库表名
func (TjDeptitem) TableName() string {
	return "tj_deptitem"
}

// ********************  tj_departinfo ********************************* BEGIN

// GetDepartinfobyId ...
func (DeptModel) GetDepartinfobyId(dto int) (*TjDepartinfo, error) {
	var infos TjDepartinfo
	// var ret *gorm.DB

	// ret = db.Table("tj_diseases").Where("1 = 1")
	// Get first matched record
	result := db.Table("tj_departinfo").Where("id = ?", dto).First(&infos)
	// result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// GetDepartinfo ...
func (DeptModel) GetDepartinfo(deptid string) (*[]TjDepartinfo, error) {
	var infos []TjDepartinfo
	var ret *gorm.DB

	ret = db.Table("tj_departinfo").Where("tj_flag = 1")

	if deptid != "" && deptid != "0" {
		ret = ret.Where("tj_deptid = ?", deptid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// GetDepartinfo ...
func (DeptModel) GetDepartinfos(deptids []string) ([]TjDepartinfo, error) {
	var infos []TjDepartinfo
	var ret *gorm.DB

	ret = db.Table("tj_departinfo").Where("tj_flag = 1")

	if len(deptids) > 0 {
		ret = ret.Where("tj_deptid in (?)", deptids)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return infos, nil
}

// InsertDepartinfo ...
func (DeptModel) InsertDepartinfo(info *TjDepartinfo) (*TjDepartinfo, error) {
	logger.Log.Infof("Save tj_departinfo:%+v", info)
	result := db.Table("tj_departinfo").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateDepartinfo ...
func (DeptModel) UpdateDepartinfo(info *TjDepartinfo) error {

	logger.Log.Infof("update tj_departinfo:%+v", info)
	result := db.Table("tj_departinfo").Debug().Save(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return result.Error
	}
	return nil
}

// DeleteDepartinfo ...
func (DeptModel) DeleteDepartinfo(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	// ret := db.Exec("delete from tj_departinfo where id = ?", id).Debug()
	ret := db.Exec("update tj_departinfo set tj_flag = 0 where id = ?", id).Debug()
	return ret.Error

}

// ********************  tj_departinfo ********************************* END

// ********************  tj_deptitem ********************************* BEGIN

// GetDeptitem ...
func (DeptModel) GetDeptitem(dto *DeptItemDTO) (*[]TjDeptitem, error) {
	var infos []TjDeptitem
	var ret *gorm.DB

	ret = db.Table("tj_deptitem").Where("1 = 1")

	if dto.Deptid != "" {
		ret = ret.Where("tj_deptid = ?", dto.Deptid)
	}

	if dto.Itemid != "" {
		ret = ret.Where("tj_itemid = ?", dto.Itemid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// InsertDeptitem ...
func (DeptModel) InsertDeptitem(info *TjDeptitem) (*TjDeptitem, error) {
	logger.Log.Infof("Save tj_deptitem:%+v", info)
	result := db.Table("tj_deptitem").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateDeptitem ...
func (DeptModel) UpdateDeptitem(info *TjDeptitem) error {

	logger.Log.Infof("update tj_deptitem:%+v", info)
	result := db.Table("tj_deptitem").Debug().Save(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return result.Error
	}
	return nil
}

// DeleteDeptitem ...
func (DeptModel) DeleteDeptitem(dto *DeptItemDTO) error {
	logger.Log.Infof("删除,pnum:%+v", dto)
	ret := db.Exec("delete from tj_deptitem where tj_itemid = ? and tj_deptid = ?", dto.Itemid, dto.Deptid).Debug()

	return ret.Error

}

// ********************  tj_deptitem ********************************* END
