package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// DiseaseModel ...
type DiseaseModel struct{}

// TjDiseases 所有疾病情况信息表
type TjDiseases struct {
	ID                int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"`      // primary key
	TjDiscode         string `gorm:"column:tj_discode;type:varchar(25);not null" json:"tj_discode"`             // 自动诊断疾病编码
	TjDisname         string `gorm:"column:tj_disname;type:varchar(250);not null" json:"tj_disname"`            // 名称
	TjDisnum          string `gorm:"column:tj_disnum;type:varchar(20);not null" json:"tj_disnum"`               // 编号
	TjDeptid          int    `gorm:"column:tj_deptid;type:int(10);not null" json:"tj_deptid"`                   // 所在科室
	TjType            string `gorm:"column:tj_type;type:varchar(10);not null" json:"tj_type"`                   // 疾病类型
	TjContent         string `gorm:"column:tj_content;type:varchar(800);not null" json:"tj_content"`            // 建议内容
	TjOccudiseaseflag int    `gorm:"column:tj_occudiseaseflag;type:int(11);not null" json:"tj_occudiseaseflag"` // 职业病标志
	TjStartage        int    `gorm:"column:tj_startage;type:int(11);not null" json:"tj_startage"`
	TjEndage          int    `gorm:"column:tj_endage;type:int(11);not null" json:"tj_endage"`
	TjSex             int    `gorm:"column:tj_sex;type:int(2);not null" json:"tj_sex"`
	TjCareer          string `gorm:"column:tj_career;type:varchar(50);not null" json:"tj_career"` // 职业
	TjMarriage        int    `gorm:"column:tj_marriage;type:int(2);not null" json:"tj_marriage"`
	TjPyjm            string `gorm:"column:tj_pyjm;type:varchar(100);not null" json:"tj_pyjm"` // 拼音简码
	TjZdym            string `gorm:"column:tj_zdym;type:varchar(100);not null" json:"tj_zdym"`
	TjTypeid          int    `gorm:"column:tj_typeid;type:int(10);not null" json:"tj_typeid"`
	TjOpinion         string `gorm:"column:tj_opinion;type:varchar(450);not null" json:"tj_opinion"`
}

// TableName get sql table name.获取数据库表名
func (TjDiseases) TableName() string {
	return "tj_diseases"
}

// ********************  TjDiseases ********************************* BEGIN

// GetDiseases ...
func (DiseaseModel) GetDiseases(dto *DiseaseDTO) (*[]TjDiseases, error) {
	var infos []TjDiseases
	var ret *gorm.DB

	ret = db.Table("tj_diseases").Where("1 = 1")

	if len(dto.Ids) > 0 {
		ret = ret.Where("id in (?)", dto.Ids)
	}

	if len(dto.Deptid) > 0 {
		ret = ret.Where("tj_deptid in (?)", dto.Deptid)
	}

	if len(dto.Typeids) > 0 {
		ret = ret.Where("tj_typeid in (?)", dto.Typeids)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// GetDiseases ...
func (DiseaseModel) GetDiseasebyId(dto int) (*TjDiseases, error) {
	var infos TjDiseases
	// var ret *gorm.DB

	// ret = db.Table("tj_diseases").Where("1 = 1")
	// Get first matched record
	result := db.Table("tj_diseases").Where("id = ?", dto).First(&infos)
	// result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// GetDiseases ...
func (DiseaseModel) GetDisease(dto string) (*TjDiseases, error) {
	var infos TjDiseases
	// var ret *gorm.DB

	// ret = db.Table("tj_diseases").Where("1 = 1")
	// Get first matched record
	result := db.Table("tj_diseases").Where("tj_discode = ?", dto).First(&infos)
	// result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// InsertDisease ...
func (DiseaseModel) InsertDisease(info *TjDiseases) (*TjDiseases, error) {
	logger.Log.Infof("Save tj_diseases:%+v", info)
	result := db.Table("tj_diseases").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateDisease ...
func (DiseaseModel) UpdateDisease(info *TjDiseases) error {

	logger.Log.Infof("update tj_diseases:%+v", info)
	result := db.Table("tj_diseases").Debug().Save(info)
	if result.Error != nil {
		logger.Log.Errorf("错误:%+v", result.Error)
		return result.Error
	}
	return nil
}

// DeleteDisease ...
func (DiseaseModel) DeleteDisease(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_diseases where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjDiseases ********************************* END
