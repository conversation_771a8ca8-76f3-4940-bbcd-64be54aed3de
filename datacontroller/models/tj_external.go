package models

import "gorm.io/gorm"

// TjExternal waibu peizhi
type TjExternal struct {
	ID          int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjExtcode   string `gorm:"column:tj_extcode;type:varchar(40);not null" json:"tj_extcode"`        // 体检编号
	TjExtname   string `gorm:"column:tj_extname;type:varchar(100);not null" json:"tj_extname"`       // 项目编号
	TjExttype   int    `gorm:"column:tj_exttype;type:int(11);not null" json:"tj_exttype"`            // 类型 1：lis 2：pacs
	TjExtstatus int    `gorm:"column:tj_extstatus;type:int(11);not null" json:"tj_extstatus"`        // 状态 1：有效 0：关闭
}

// TableName get sql table name.获取数据库表名
func (TjExternal) TableName() string {
	return "tj_external"
}

// GetGroupinfo ...
func GetExternalInfo(exttype int) (*[]TjExternal, error) {
	var infos []TjExternal
	var ret *gorm.DB

	ret = db.Table("tj_external").Where("tj_extstatus = 1")

	if exttype > 0 {
		ret = ret.Where("tj_exttype = ?", exttype)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}
