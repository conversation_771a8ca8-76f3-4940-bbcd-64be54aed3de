package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// GroupModel ...
type GroupModel struct{}

// TjGroupinfo 组别信息管理
type TjGroupinfo struct {
	ID          int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjGname     string `gorm:"column:tj_gname;type:varchar(50);not null" json:"tj_gname"`            // 组名
	TjGmemo     string `gorm:"column:tj_gmemo;type:varchar(240);not null" json:"tj_gmemo"`           // 备注
	TjGoperator string `gorm:"column:tj_goperator;type:varchar(10);not null" json:"tj_goperator"`    // 最后修改人
	TjGmoddate  int64  `gorm:"column:tj_gmoddate;type:bigint(20);not null" json:"tj_gmoddate"`       // 最后修改日期
}

// TableName get sql table name.获取数据库表名
func (TjGroupinfo) TableName() string {
	return "tj_groupinfo"
}

// TjGroupright 组默认权限信息
type TjGroupright struct {
	ID         int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjGid      int    `gorm:"column:tj_gid;type:int(10);not null" json:"tj_gid"`                    // 组号
	TjRname    string `gorm:"column:tj_rname;type:varchar(50);not null" json:"tj_rname"`            // 权限(菜单名)
	TjVright   int    `gorm:"column:tj_vright;type:int(11);not null" json:"tj_vright"`              // 增删等权限
	TjOperator string `gorm:"column:tj_operator;type:varchar(10);not null" json:"tj_operator"`      // 最后修改人
	TjModdate  int64  `gorm:"column:tj_moddate;type:bigint(20);not null" json:"tj_moddate"`         // 最后修改日期
	TjMenuid   int    `gorm:"column:tj_menuid;type:int(10);not null" json:"tj_menuid"`              // 菜单id
}

// TableName get sql table name.获取数据库表名
func (TjGroupright) TableName() string {
	return "tj_groupright"
}

// ********************  TjGroupinfo ********************************* BEGIN

// GetGroupinfo ...
func (GroupModel) GetGroupinfo(id int) (*[]TjGroupinfo, error) {
	var infos []TjGroupinfo
	var ret *gorm.DB

	ret = db.Table("tj_groupinfo").Where("1 = 1")

	if id > 0 {
		ret = ret.Where("id = ?", id)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertGroupinfo ...
func (GroupModel) InsertGroupinfo(info *TjGroupinfo) (*TjGroupinfo, error) {
	logger.Log.Infof("Save tj_groupinfo:%+v", info)
	result := db.Table("tj_groupinfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateGroupinfo ...
func (GroupModel) UpdateGroupinfo(info *TjGroupinfo) error {

	logger.Log.Infof("update tj_groupinfo:%+v", info)
	result := db.Table("tj_groupinfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteGroupinfo ...
func (GroupModel) DeleteGroupinfo(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_groupinfo where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjGroupinfo ********************************* END

// ********************  Grouprights ********************************* BEGIN

// GetGrouprights ...
func (GroupModel) GetGrouprights(gid int) (*[]TjGroupright, error) {
	var infos []TjGroupright
	var ret *gorm.DB

	ret = db.Table("tj_groupright").Where("1 = 1")

	if gid > 0 {
		ret = ret.Where("tj_gid = ?", gid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertGroupright ...
func (GroupModel) InsertGroupright(info *[]TjGroupright) (*[]TjGroupright, error) {
	logger.Log.Infof("Save tj_groupright:%+v", info)
	result := db.Table("tj_groupright").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateGroupright ...
func (GroupModel) UpdateGroupright(info *TjGroupright) error {

	logger.Log.Infof("update tj_groupright:%+v", info)
	result := db.Table("tj_groupright").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteGroupright ...
func (GroupModel) DeleteGroupright(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_groupright where tj_gid = ?", id).Debug()

	return ret.Error

}

// ********************  Grouprights ********************************* END
