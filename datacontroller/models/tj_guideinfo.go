package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// GuideModel ...
type GuideModel struct{}

// TjGuideinfo 指引单
type TjGuideinfo struct {
	ID           int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjGnum       string `gorm:"unique;column:tj_gnum;type:varchar(10);not null" json:"tj_gnum"`       // 编码
	TjGname      string `gorm:"column:tj_gname;type:varchar(40);not null" json:"tj_gname"`            // 单据名称
	TjGdesc      string `gorm:"column:tj_gdesc;type:varchar(255);not null" json:"tj_gdesc"`           // 单据描述
	TjGshoworder int    `gorm:"column:tj_gshoworder;type:int(11);not null" json:"tj_gshoworder"`      // 显示顺序
}

// TableName get sql table name.获取数据库表名
func (TjGuideinfo) TableName() string {
	return "tj_guideinfo"
}

// ********************  TjGuideinfo ********************************* BEGIN

// Query ...
func (GuideModel) Query(id int) (*[]TjGuideinfo, error) {
	var infos []TjGuideinfo
	var ret *gorm.DB

	ret = db.Table("tj_guideinfo").Where("1 = 1")

	if id > 0 {
		ret = ret.Where("id = ?", id)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// Insert ...
func (GuideModel) Insert(info *TjGuideinfo) (*TjGuideinfo, error) {
	logger.Log.Infof("Save tj_guideinfo:%+v", info)
	result := db.Table("tj_guideinfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// Update ...
func (GuideModel) Update(info *TjGuideinfo) error {

	logger.Log.Infof("update tj_guideinfo:%+v", info)
	result := db.Table("tj_guideinfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// Delete ...
func (GuideModel) Delete(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_guideinfo where id = ?", id).Debug()

	return ret.Error

}

// ********************  tj_guideinfo ********************************* END
