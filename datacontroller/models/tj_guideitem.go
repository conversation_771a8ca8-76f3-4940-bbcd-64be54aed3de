package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// GuideItemModel ...
type GuideItemModel struct{}

// TjGuideitem 指引单与项目关系表
type TjGuideitem struct {
	ID        int    `gorm:"primary_key;auto_increment;column:id;type:int(11);not null" json:"id"` // 主键
	TjGUIDeid int    `gorm:"column:tj_guideid;type:int(11);not null" json:"tj_guideid"`            // 指引单ID
	TjItemid  string `gorm:"column:tj_itemid;type:varchar(45);not null" json:"tj_itemid"`          // 项目编号
}

// TableName get sql table name.获取数据库表名
func (TjGuideitem) TableName() string {
	return "tj_guideitem"
}

// ********************  content ********************************* BEGIN

// Query ...
func (GuideItemModel) Query(gid int) (*[]TjGuideitem, error) {
	var infos []TjGuideitem
	var ret *gorm.DB

	ret = db.Table("tj_guideitem").Where("1 = 1")

	if gid > 0 {
		ret = ret.Where("tj_guideid = ?", gid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// Insert ...
func (GuideItemModel) Insert(info *[]TjGuideitem) (*[]TjGuideitem, error) {
	logger.Log.Infof("Save tj_guideitem:%+v", info)
	result := db.Table("tj_guideitem").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// Update ...
func (GuideItemModel) Update(info *TjGuideitem) error {

	logger.Log.Infof("update tj_guideitem:%+v", info)
	result := db.Table("tj_guideitem").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// Delete ...
func (GuideItemModel) Delete(dto *GuideitemDTO) error {
	logger.Log.Infof("删除ID:%+v", dto)

	var ret *gorm.DB

	ret = db.Table("tj_guideitem").Where("1 = 1")

	if dto.Guideid > 0 {
		ret = ret.Where("tj_guideid = ?", dto.Guideid)
	}

	if dto.Itemid != "" {
		ret = ret.Where("tj_itemid = ?", dto.Itemid)
	}

	return ret.Debug().Delete(&TjGuideitem{}).Error

}

// ********************  tj_guideinfo ********************************* END
