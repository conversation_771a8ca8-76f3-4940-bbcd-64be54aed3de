package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// HazardModel ...
type HazardModel struct{}

// TjHazardinfo 毒害因素信息表
type TjHazardinfo struct {
	ID          int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjTid       int    `gorm:"column:tj_tid;type:int(10);not null" json:"tj_tid"`                    // 所属类别的编号
	TjHname     string `gorm:"column:tj_hname;type:varchar(30);not null" json:"tj_hname"`            // 毒害因素名称
	TjPyjm      string `gorm:"column:tj_pyjm;type:varchar(30);not null" json:"tj_pyjm"`              // 简拼
	TjShoworder int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`        // 显示顺序
	TjForbiden  string `gorm:"column:tj_forbiden;type:varchar(200);not null" json:"tj_forbiden"`
	TjMemo      string `gorm:"column:tj_memo;type:varchar(50);not null" json:"tj_memo"`
	TjExtcode   string `gorm:"column:tj_extcode;type:varchar(50);not null" json:"tj_extcode"` //外部的代码
}

// TableName 会将 struct 的表名重写为 `string`
func (TjHazardinfo) TableName() string {
	return "tj_hazardinfo"
}

// TjHazarditem 毒害因素检查项目信息表
type TjHazarditem struct {
	ID          int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjHid       int    `gorm:"column:tj_hid;type:int(10);not null" json:"tj_hid"`                    // 毒害因素的编号
	TjItemid    string `gorm:"column:tj_itemid;type:varchar(10);not null" json:"tj_itemid"`          // 检查项目的编号
	TjTesttype  int    `gorm:"column:tj_testtype;type:int(10);not null" json:"tj_testtype"`
	TjOflag     int8   `gorm:"column:tj_oflag;type:tinyint(4);not null" json:"tj_oflag"`      // 必检可选标志 0：可选 1：必查
	TjShoworder int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"` // 显示顺序
	TjMemo      string `gorm:"column:tj_memo;type:varchar(50);not null" json:"tj_memo"`
}

// TableName 会将 struct 的表名重写为 `string`
func (TjHazarditem) TableName() string {
	return "tj_hazarditem"
}

// TjHazardtype 毒害因素类别表
type TjHazardtype struct {
	ID          int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjTname     string `gorm:"column:tj_tname;type:varchar(30);not null" json:"tj_tname"`            // 类别名称
	TjPyjm      string `gorm:"column:tj_pyjm;type:varchar(30);not null" json:"tj_pyjm"`              // 简拼
	TjShoworder int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`        // 显示顺序
	TjMemo      string `gorm:"column:tj_memo;type:varchar(50);not null" json:"tj_memo"`
}

// TableName get sql table name.获取数据库表名
func (TjHazardtype) TableName() string {
	return "tj_hazardtype"
}

// TjHazarddisease 毒害因素职业禁忌与目标疾病信息表
type TjHazarddisease struct {
	ID              int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"`       // primary key
	TjHid           int    `gorm:"column:tj_hid;type:int(10);not null" json:"tj_hid"`                          // 毒害因素的编号
	TjTesttype      int    `gorm:"column:tj_testtype;type:int(4);not null" json:"tj_testtype"`                 // 体检类别
	TjForbidden     string `gorm:"column:tj_forbidden;type:varchar(100);not null" json:"tj_forbidden"`         // 职业禁忌名称
	TjTargetdisease string `gorm:"column:tj_targetdisease;type:varchar(100);not null" json:"tj_targetdisease"` // 目标疾病名称
	TjMemo          string `gorm:"column:tj_memo;type:varchar(50);not null" json:"tj_memo"`
}

// TableName get sql table name.获取数据库表名
func (TjHazarddisease) TableName() string {
	return "tj_hazarddisease"
}

// ********************  TjHazardinfo ********************************* BEGIN

// GetHazardinfo ...
func (HazardModel) GetHazardinfo(dto *HazardDTO) (*[]TjHazardinfo, error) {
	var infos []TjHazardinfo
	var ret *gorm.DB

	ret = db.Table("tj_hazardinfo").Where("1 = 1")

	if len(dto.Hids) > 0 {
		ret = ret.Where("id in (?)", dto.Hids)
	}

	if len(dto.Hnames) > 0 {
		ret = ret.Where("tj_hname in (?)", dto.Hnames)
	}

	if dto.Htype > 0 {
		ret = ret.Where("tj_tid = ?", dto.Htype)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertHazardinfo ...
func (HazardModel) InsertHazardinfo(info *TjHazardinfo) (*TjHazardinfo, error) {
	logger.Log.Infof("Save tj_hazardinfo:%+v", info)
	result := db.Table("tj_hazardinfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateHazardinfo ...
func (HazardModel) UpdateHazardinfo(info *TjHazardinfo) error {

	logger.Log.Infof("update tj_hazardinfo:%+v", info)
	result := db.Table("tj_hazardinfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteHazardinfo ...
func (HazardModel) DeleteHazardinfo(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_hazardinfo where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjHazardinfo ********************************* END

// ********************  TjHazarditem ********************************* BEGIN

// GetHazarditem ...
func (HazardModel) GetHazarditem(dto *HazardDIDTO) (*[]TjHazarditem, error) {
	var infos []TjHazarditem
	var ret *gorm.DB

	ret = db.Table("tj_hazarditem").Where("1 = 1")

	if len(dto.Hids) > 0 {
		ret = ret.Where("tj_hid in (?)", dto.Hids)
	}

	if dto.Type > 0 {
		ret = ret.Where("tj_testtype = ?", dto.Type)
	}

	if dto.Itemid != "" {
		ret = ret.Where("tj_itemid = ?", dto.Itemid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertHazarditem ...
func (HazardModel) InsertHazarditem(info *TjHazarditem) (*TjHazarditem, error) {
	logger.Log.Infof("Save tj_hazarditem:%+v", info)
	result := db.Table("tj_hazarditem").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateHazarditem ...
func (HazardModel) UpdateHazarditem(info *TjHazarditem) error {

	logger.Log.Infof("update tj_hazarditem:%+v", info)
	result := db.Table("tj_hazarditem").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteHazarditem ...
func (HazardModel) DeleteHazarditem(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_hazarditem where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjHazarditem ********************************* END

// ********************  TjHazardtype ********************************* BEGIN

// GetHazardtype ...
func (HazardModel) GetHazardtype(id int) (*[]TjHazardtype, error) {
	var infos []TjHazardtype
	var ret *gorm.DB

	ret = db.Table("tj_hazardtype").Where("1 = 1")

	if id > 0 {
		ret = ret.Where("id = ?", id)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertHazardtype ...
func (HazardModel) InsertHazardtype(info *TjHazardtype) (*TjHazardtype, error) {
	logger.Log.Infof("Save tj_hazardtype:%+v", info)
	result := db.Table("tj_hazardtype").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateHazardtype ...
func (HazardModel) UpdateHazardtype(info *TjHazardtype) error {

	logger.Log.Infof("update tj_hazardtype:%+v", info)
	result := db.Table("tj_hazardtype").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteHazardtype ...
func (HazardModel) DeleteHazardtype(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_hazardtype where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjHazardtype ********************************* END

// ********************  TjHazarddisease ********************************* BEGIN

// GetHazarddisease ...
func (HazardModel) GetHazarddisease(dto *HazardDIDTO) (*[]TjHazarddisease, error) {
	var infos []TjHazarddisease
	var ret *gorm.DB

	ret = db.Table("tj_hazarddisease").Where("1 = 1")
	if len(dto.Hids) > 0 {
		ret = ret.Where("tj_hid in (?)", dto.Hids)
	}

	if dto.Type > 0 {
		ret = ret.Where("tj_testtype = ?", dto.Type)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertHazarddisease ...
func (HazardModel) InsertHazarddisease(info *TjHazarddisease) (*TjHazarddisease, error) {
	logger.Log.Infof("Save tj_hazarddisease:%+v", info)
	result := db.Table("tj_hazarddisease").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateHazarddisease ...
func (HazardModel) UpdateHazarddisease(info *TjHazarddisease) error {

	logger.Log.Infof("update tj_hazarddisease:%+v", info)
	result := db.Table("tj_hazarddisease").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteHazarddisease ...
func (HazardModel) DeleteHazarddisease(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_hazarddisease where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjHazarddisease ********************************* END
