package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// InstrumentModel ...
type InstrumentModel struct{}

// TjInstrumentinfo 检验仪器表
type TjInstrumentinfo struct {
	ID         int     `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjStruid   string  `gorm:"column:tj_struid;type:varchar(16);not null" json:"tj_struid"`          // 仪器编号
	TjStruname string  `gorm:"column:tj_struname;type:varchar(50);not null" json:"tj_struname"`      // 仪器名称
	TjTypename string  `gorm:"column:tj_typename;type:varchar(100);not null" json:"tj_typename"`     // 仪器型号
	TjDeptid   string  `gorm:"column:tj_deptid;type:varchar(10);not null" json:"tj_deptid"`          // 科室编码
	TjMemo     string  `gorm:"column:tj_memo;type:varchar(30);not null" json:"tj_memo"`              // 备注
	TjCom      string  `gorm:"column:tj_com;type:varchar(10);not null" json:"tj_com"`                // COM接口
	TjLowvalue float32 `gorm:"column:tj_lowvalue;type:float;not null" json:"tj_lowvalue"`            // 接收下限
	TjUpvalue  float32 `gorm:"column:tj_upvalue;type:float;not null" json:"tj_upvalue"`              // 接收上限
	TjVlimited int     `gorm:"column:tj_vlimited;type:int(11);not null" json:"tj_vlimited"`          // 接收是否有限制
}

// TableName get sql table name.获取数据库表名
func (TjInstrumentinfo) TableName() string {
	return "tj_instrumentinfo"
}

// ********************  TjInstrumentinfo ********************************* BEGIN

// GetInstrumentinfo ... find by tj_struid
func (InstrumentModel) GetInstrumentinfo(pnum string) (*[]TjInstrumentinfo, error) {
	var infos []TjInstrumentinfo
	var ret *gorm.DB

	ret = db.Table("tj_instrumentinfo").Where("1 = 1")

	if pnum != "" && pnum != "0" {
		ret = ret.Where("tj_struid = ?", pnum)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertInstrumentinfo ...
func (InstrumentModel) InsertInstrumentinfo(info *TjInstrumentinfo) (*TjInstrumentinfo, error) {
	logger.Log.Infof("Save tj_instrumentinfo:%+v", info)
	result := db.Table("tj_instrumentinfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateInstrumentinfo ...
func (InstrumentModel) UpdateInstrumentinfo(info *TjInstrumentinfo) error {

	logger.Log.Infof("update tj_instrumentinfo:%+v", info)
	result := db.Table("tj_instrumentinfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteInstrumentinfo ...
func (InstrumentModel) DeleteInstrumentinfo(struid string) error {
	logger.Log.Infof("删除ID:%d", struid)
	ret := db.Exec("delete from tj_instrumentinfo where tj_struid = ?", struid).Debug()

	return ret.Error

}

// ********************  TjInstrumentinfo ********************************* END
