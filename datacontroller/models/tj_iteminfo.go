package models

import (
	"errors"
	"fmt"
	"utility/logger"

	"gorm.io/gorm"
)

// ItemsModel ...
type ItemsModel struct{}

// TjIteminfo 项目代码表
type TjIteminfo struct {
	ID              int     `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"`       // primary key
	TjItemid        string  `gorm:"column:tj_itemid;type:varchar(12);not null" json:"tj_itemid"`                // 项目编号
	TjItemtype      int     `gorm:"column:tj_itemtype;type:int(10);not null" json:"tj_itemtype"`                // 项目类别
	TjItemname      string  `gorm:"column:tj_itemname;type:varchar(60);not null" json:"tj_itemname"`            // 项目名称
	TjItemunit      string  `gorm:"column:tj_itemunit;type:varchar(20);not null" json:"tj_itemunit"`            // 项目单位
	TjShoworder     int     `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`              // 显示顺序
	TjSex           int     `gorm:"column:tj_sex;type:int(11);not null" json:"tj_sex"`                          // 适用性别、男？女？全部？
	TjItemprice     float32 `gorm:"column:tj_itemprice;type:float;not null" json:"tj_itemprice"`                // 单价
	TjValuetype     string  `gorm:"column:tj_valuetype;type:varchar(1);not null" json:"tj_valuetype"`           // 数值类型 数值？字符？
	TjDefaultresult string  `gorm:"column:tj_defaultresult;type:varchar(200);not null" json:"tj_defaultresult"` // 默认结果
	TjReftype       string  `gorm:"column:tj_reftype;type:varchar(10);not null" json:"tj_reftype"`              // 参考类别 >x?
	TjUppervalue    string  `gorm:"column:tj_uppervalue;type:varchar(15);not null" json:"tj_uppervalue"`        // 上限值
	TjLowvalue      string  `gorm:"column:tj_lowvalue;type:varchar(15);not null" json:"tj_lowvalue"`            // 下限值
	TjHighoffset    string  `gorm:"column:tj_highoffset;type:varchar(20);not null" json:"tj_highoffset"`        // 偏高提示
	TjLowoffset     string  `gorm:"column:tj_lowoffset;type:varchar(20);not null" json:"tj_lowoffset"`          // 偏低提示
	TjOkflag        int     `gorm:"column:tj_okflag;type:int(11);not null" json:"tj_okflag"`                    // 有效无效标志
	TjBarflag       int     `gorm:"column:tj_barflag;type:int(11);not null" json:"tj_barflag"`                  // 是否打印条码标志(检验科项目)
	TjCombineflag   int     `gorm:"column:tj_combineflag;type:int(11);not null" json:"tj_combineflag"`          // 是否组合项目 0:非组合， 1：组合
	TjAutodiagflag  int     `gorm:"column:tj_autodiagflag;type:int(11);not null" json:"tj_autodiagflag"`        // 自动小结
	TjLisnum        string  `gorm:"column:tj_lisnum;type:varchar(200);not null" json:"tj_lisnum"`               // 对应的lis编号，用来数据接收使用
	TjPyjm          string  `gorm:"column:tj_pyjm;type:varchar(20);not null" json:"tj_pyjm"`
	TjZdym          string  `gorm:"column:tj_zdym;type:varchar(20);not null" json:"tj_zdym"`
	TjMemo          string  `gorm:"column:tj_memo;type:varchar(100);not null" json:"tj_memo"`     // 备注
	TjOperator      int     `gorm:"column:tj_operator;type:int(4);not null" json:"tj_operator"`   // 添加人员
	TjModdate       int     `gorm:"column:tj_moddate;type:bigint(20);not null" json:"tj_moddate"` // 最后修改日期
	SampleCode      string  `gorm:"column:sample_code;type:varchar(5);not null" json:"sample_code"`
	// TjExtcode       string  `gorm:"column:tj_extcode;type:varchar(50);not null" json:"tj_extcode"` //外部的代码
}

// TableName 会将 struct 的表名重写为 `string`
func (TjIteminfo) TableName() string {
	return "tj_iteminfo"
}

// BeforeDelete ...
func (TjIteminfo) BeforeDelete(tx *gorm.DB) (err error) {

	logger.Log.Infof("开始删除项目信息")
	return
}

// TjItemtype 项目类别
type TjItemtype struct {
	ID          int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjTypeid    int    `gorm:"unique;column:tj_typeid;type:int(10);not null" json:"tj_typeid"`       // 编码
	TjTypename  string `gorm:"column:tj_typename;type:varchar(60);not null" json:"tj_typename"`      // 名称
	TjDeptid    string `gorm:"column:tj_deptid;type:varchar(10);not null" json:"tj_deptid"`          // 开展科室
	TjType      int    `gorm:"column:tj_type;type:int(11);not null" json:"tj_type"`                  // 项目类型（检查1、检验2、功能3）
	TjOutsource int    `gorm:"column:tj_outsource;type:int(11);not null" json:"tj_outsource"`        // 项目是否外包(外包项目有数据导入和接收)
	TjPyjm      string `gorm:"column:tj_pyjm;type:varchar(20);not null" json:"tj_pyjm"`
	TjZdym      string `gorm:"column:tj_zdym;type:varchar(20);not null" json:"tj_zdym"`
	TjShoworder int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`   // 显示顺序
	TjOperator  string `gorm:"column:tj_operator;type:varchar(10);not null" json:"tj_operator"` // 操作员
	TjModdate   int64  `gorm:"column:tj_moddate;type:bigint(20);not null" json:"tj_moddate"`    // 操作日期
}

// TableName get sql table name.获取数据库表名
func (TjItemtype) TableName() string {
	return "tj_itemtype"
}

// TjCombineinfo 组合明细（一个组合项目的明细）
type TjCombineinfo struct {
	ID          int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjCombid    string `gorm:"column:tj_combid;type:varchar(12);not null" json:"tj_combid"`          // 组合编号
	TjItemid    string `gorm:"column:tj_itemid;type:varchar(12);not null" json:"tj_itemid"`          // 项目编号
	TjShoworder int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`        // 显示顺序
}

// TableName get sql table name.获取数据库表名
func (TjCombineinfo) TableName() string {
	return "tj_combineinfo"
}

// GetIteminfos ...
func (ItemsModel) GetIteminfobyFlags(sflag string) (*[]TjIteminfo, error) {
	var infos []TjIteminfo
	var ret *gorm.DB

	ret = db.Table("tj_iteminfo").Where("tj_okflag = 1")
	if sflag != "-1" {
		ret = ret.Where("tj_combineflag = ?", sflag)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// GetIteminfos ...
func (ItemsModel) GetIteminfos(itemids []string) ([]TjIteminfo, error) {
	var infos []TjIteminfo
	var ret *gorm.DB

	ret = db.Table("tj_iteminfo").Where("tj_okflag = 1")
	if len(itemids) > 0 {
		ret = ret.Where("tj_itemid in (?)", itemids)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return infos, nil
}

// GetIteminfos ...
func (ItemsModel) GetIteminfosbyLisnum(lisnums []string) (*[]TjIteminfo, error) {
	var infos []TjIteminfo
	var ret *gorm.DB

	ret = db.Table("tj_iteminfo").Where("tj_okflag = 1")
	if len(lisnums) > 0 {
		ret = ret.Where("tj_lisnum in (?)", lisnums)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertIteminfo ...
func (ItemsModel) InsertIteminfo(info *TjIteminfo) (*TjIteminfo, error) {
	logger.Log.Infof("Save iteminfo:%+v", info)
	result := db.Table("tj_iteminfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateIteminfo ...
func (ItemsModel) UpdateIteminfo(info *TjIteminfo) error {

	logger.Log.Infof("update iteminfo:%+v", info)
	result := db.Table("tj_iteminfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteIteminfo ...
func (ItemsModel) DeleteIteminfo(id int) error {
	logger.Log.Infof("删除项目信息，项目ID:%d", id)
	ret := db.Exec("update tj_iteminfo set tj_okflag = 0 where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjItemtype ********************************* BEGIN

// GetItemtypes ...
func (ItemsModel) GetItemtypes(typeid int) (*[]TjItemtype, error) {
	var infos []TjItemtype
	var ret *gorm.DB

	ret = db.Table("tj_itemtype").Where("1 = 1")

	if typeid > 0 {
		ret = ret.Where("tj_typeid = ?", typeid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertItemtype ...
func (ItemsModel) InsertItemtype(info *TjItemtype) (*TjItemtype, error) {
	logger.Log.Infof("Save tj_itemtype:%+v", info)
	result := db.Table("tj_itemtype").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateItemtype ...
func (ItemsModel) UpdateItemtype(info *TjItemtype) error {

	logger.Log.Infof("update tj_itemtype:%+v", info)
	result := db.Table("tj_itemtype").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteItemtype ...
func (ItemsModel) DeleteItemtype(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_itemtype where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjItemtype ********************************* END

// ********************  TjCombineinfo ********************************* BEGIN

// GetCombineinfo ...
func (ItemsModel) GetCombineinfo(combids []string) (*[]TjCombineinfo, error) {
	var infos []TjCombineinfo
	var ret *gorm.DB

	ret = db.Table("tj_combineinfo").Where("1 = 1")

	if len(combids) > 0 {
		ret = ret.Where("tj_combid in ?", combids)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertCombineinfo ...
func (ItemsModel) InsertCombineinfo(info *[]TjCombineinfo) (*[]TjCombineinfo, error) {
	logger.Log.Infof("Save tj_combineinfo:%+v", info)
	result := db.Table("tj_combineinfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateCombineinfo ...
func (ItemsModel) UpdateCombineinfo(info *TjCombineinfo) error {

	logger.Log.Infof("update tj_combineinfo:%+v", info)
	result := db.Table("tj_combineinfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteCombineinfo ...
func (ItemsModel) DeleteCombineinfo(dto *ItemdelDTO) error {
	logger.Log.Infof("删除,combid:%+v", dto)

	if dto.Combid == "" && dto.Itemid == "" {
		return errors.New("条件为空，不能删除")
	}
	var ret *gorm.DB
	ret = db.Table("tj_combineinfo").Where("1 = 1")
	if dto.Combid != "" {
		ret = ret.Where("tj_combid = ?", dto.Combid)
	}

	if dto.Itemid != "" {
		ret = ret.Where("tj_itemid = ?", dto.Itemid)
	}

	return ret.Debug().Delete(&TjCombineinfo{}).Error
	// ret := db.Exec("delete from tj_combineinfo where tj_combid = ?", combid).Debug()

	// return ret.Error

}

// DeleteCheckiteminfos ... FindByTestID
func GetCombineinfoByCombid(synid string) (*TjCombineinfo, error) {
	if synid == "" {
		return nil, fmt.Errorf("不能找到组合项目：%s得明细", synid)
	}
	var ret *gorm.DB
	retdata := TjCombineinfo{}
	ret = db.Table("tj_combineinfo").Where("tj_combid = ?", synid).First(&retdata)
	if ret.Error != nil {
		return nil, ret.Error
	}

	return &retdata, nil

}

// ********************  TjCombineinfo ********************************* END
