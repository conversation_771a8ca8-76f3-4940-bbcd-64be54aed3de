package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// TjItemrefinfo 仪器项目编号和系统项目编号对照表
type TjItemrefinfo struct {
	ID         int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjStruid   string `gorm:"column:tj_struid;type:varchar(16);not null" json:"tj_struid"`          // 仪器编号
	TjStrucode string `gorm:"column:tj_strucode;type:varchar(30);not null" json:"tj_strucode"`      // 仪器项目编号
	TjItemid   string `gorm:"column:tj_itemid;type:varchar(12);not null" json:"tj_itemid"`          // 系统项目编号
	TjMemo     string `gorm:"column:tj_memo;type:varchar(30);not null" json:"tj_memo"`
}

// TableName get sql table name.获取数据库表名
func (TjItemrefinfo) TableName() string {
	return "tj_itemrefinfo"
}

// ********************  TjItemrefinfo ********************************* BEGIN

// GetItemrefinfos ...
func (ItemsModel) GetItemrefinfos(dto *ItemrefDTO) (*[]TjItemrefinfo, error) {
	var infos []TjItemrefinfo
	var ret *gorm.DB

	ret = db.Table("tj_itemrefinfo").Where("1 = 1")
	if dto.Struid != "" {
		ret = ret.Where("tj_struid = ?", dto.Struid)
	}

	if dto.Strucode != "" {
		ret = ret.Where("tj_strucode = ?", dto.Strucode)
	}

	if dto.Itemid != "" {
		ret = ret.Where("tj_itemid = ?", dto.Itemid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertItemrefinfo ...
func (ItemsModel) InsertItemrefinfo(info *TjItemrefinfo) (*TjItemrefinfo, error) {
	logger.Log.Infof("Save tj_itemrefinfo:%+v", info)
	result := db.Table("tj_itemrefinfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateItemrefinfo ...
func (ItemsModel) UpdateItemrefinfo(info *TjItemrefinfo) error {

	logger.Log.Infof("update tj_itemrefinfo:%+v", info)
	result := db.Table("tj_itemrefinfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteItemrefinfo ...
func (ItemsModel) DeleteItemrefinfo(dto *ItemrefDelDTO) error {
	logger.Log.Infof("删除项目信息，项目ID:%+v", dto)

	var ret *gorm.DB

	ret = db.Table("tj_itemrefinfo").Where("1 = 1")

	if dto.Struid != "" {
		ret = ret.Where("tj_struid = ?", dto.Struid)
	}

	if dto.ID > 0 {
		ret = ret.Where("id = ?", dto.ID)
	}

	result := ret.Debug().Delete(&TjItemrefinfo{})

	// ret := db.Exec("delete from tj_itemrefinfo where tj_struid = ?", struid).Debug()

	return result.Error

}

// // DeleteItemrefinfo ...
// func (ItemsModel) DeleteItemrefinfo(struid string) error {
// 	logger.Log.Infof("删除项目信息，项目ID:%d", struid)
// 	ret := db.Exec("delete from tj_itemrefinfo where tj_struid = ?", struid).Debug()

// 	return ret.Error

// }

// // DeleteItemrefinfoByid ...
// func (ItemsModel) DeleteItemrefinfoByid(id int) error {
// 	logger.Log.Infof("删除项目信息，项目ID:%d", id)
// 	ret := db.Exec("delete from tj_itemrefinfo where id = ?", id).Debug()

// 	return ret.Error

// }

// ********************  TjItemrefinfo ********************************* END
