package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// TjItemresultinfo 检查项目结果表
type TjItemresultinfo struct {
	ID           int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjItemid     string `gorm:"column:tj_itemid;type:varchar(12);not null" json:"tj_itemid"`
	TjItemresult string `gorm:"column:tj_itemresult;type:varchar(200);not null" json:"tj_itemresult"`
	TjSuminfo    string `gorm:"column:tj_suminfo;type:varchar(200);not null" json:"tj_suminfo"` // 小结信息
	TjSumflag    int    `gorm:"column:tj_sumflag;type:int(11);not null" json:"tj_sumflag"`      // 是否自动小结
	TjShoworder  int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`
}

// TableName get sql table name.获取数据库表名
func (TjItemresultinfo) TableName() string {
	return "tj_itemresultinfo"
}

// ********************  tj_itemresultinfo ********************************* BEGIN

// GetItemresultinfo ...
func (ItemsModel) GetItemresultinfo(itemid string) (*[]TjItemresultinfo, error) {
	var infos []TjItemresultinfo
	var ret *gorm.DB

	ret = db.Table("tj_itemresultinfo").Where("1 = 1")

	if itemid != "" {
		ret = ret.Where("tj_itemid = ?", itemid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertItemresultinfo ...
func (ItemsModel) InsertItemresultinfo(info *TjItemresultinfo) (*TjItemresultinfo, error) {
	logger.Log.Infof("Save tj_itemresultinfo:%+v", info)
	result := db.Table("tj_itemresultinfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateItemresultinfo ...
func (ItemsModel) UpdateItemresultinfo(info *TjItemresultinfo) error {

	logger.Log.Infof("update tj_itemresultinfo:%+v", info)
	result := db.Table("tj_itemresultinfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteItemresultinfo ...
func (ItemsModel) DeleteItemresultinfo(id int) error {
	logger.Log.Infof("删除项目信息，项目ID:%d", id)
	ret := db.Exec("delete from tj_itemresultinfo where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjItemresultinfo ********************************* END
