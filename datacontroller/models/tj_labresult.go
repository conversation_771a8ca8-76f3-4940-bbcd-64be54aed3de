package models

import (
	"errors"
	"log"
	"utility/logger"

	"gorm.io/gorm"
)

// LabModel ...
type LabModel struct{}

// TjLabresult 实验室结果导入表
type TjLabresult struct {
	ID              int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjClinicid      string `gorm:"column:tj_clinicid;type:varchar(100);not null" json:"tj_clinicid"`
	TjTestid        string `gorm:"column:tj_testid;type:varchar(100);not null" json:"tj_testid"` // 检查号
	TjPatientname   string `gorm:"column:tj_patientname;type:varchar(100);not null" json:"tj_patientname"`
	TjSex           string `gorm:"column:tj_sex;type:varchar(40);not null" json:"tj_sex"`
	TjOrigrec       string `gorm:"column:tj_origrec;type:varchar(100);not null" json:"tj_origrec"` // 对应的项目lis编号
	TjItemid        string `gorm:"column:tj_itemid;type:varchar(100);not null" json:"tj_itemid"`   // 对应的项目编号
	TjAnalyte       string `gorm:"column:tj_analyte;type:varchar(200);not null" json:"tj_analyte"` // 体检项目名称
	TjShortname     string `gorm:"column:tj_shortname;type:varchar(100);not null" json:"tj_shortname"`
	TjUnits         string `gorm:"column:tj_units;type:varchar(100);not null" json:"tj_units"`
	TjFinal         string `gorm:"column:tj_final;type:varchar(100);not null" json:"tj_final"` // 结果
	TjRn10          string `gorm:"column:tj_rn10;type:varchar(100);not null" json:"tj_rn10"`
	TjCkfwL         string `gorm:"column:tj_ckfw_l;type:varchar(45);not null" json:"tj_ckfw_l"`
	TjCkfwH         string `gorm:"column:tj_ckfw_h;type:varchar(45);not null" json:"tj_ckfw_h"`
	TjCkfw          string `gorm:"column:tj_ckfw;type:varchar(45);not null" json:"tj_ckfw"` // 参考范围
	TjAbnormalflag  int    `gorm:"column:tj_abnormalflag;type:int(11);not null" json:"tj_abnormalflag"`
	TjDisplowhigh   string `gorm:"column:tj_displowhigh;type:varchar(100);not null" json:"tj_displowhigh"`
	TjSenddate      int64  `gorm:"column:tj_senddate;type:bigint(20);not null" json:"tj_senddate"` // 送检日期
	TjOrdno         string `gorm:"column:tj_ordno;type:varchar(100);not null" json:"tj_ordno"`
	TjTestgroup     string `gorm:"column:tj_testgroup;type:varchar(200);not null" json:"tj_testgroup"`
	TjCheckdoctor   string `gorm:"column:tj_checkdoctor;type:varchar(50);not null" json:"tj_checkdoctor"`      // 检查者(直接用名字显示)
	TjRecheckdoctor string `gorm:"column:tj_recheckdoctor;type:varchar(100);not null" json:"tj_recheckdoctor"` // 复查医生(直接用名字显示)
	TjImporter      string `gorm:"column:tj_importer;type:varchar(100);not null" json:"tj_importer"`           // 导入者(直接用名字显示)目前导入者就是复查医生
	TjImportdate    int64  `gorm:"column:tj_importdate;type:bigint(20);not null" json:"tj_importdate"`         // 导入日期
	TjIsreceived    int    `gorm:"column:tj_isreceived;type:int(11);not null" json:"tj_isreceived"`            // 是否已经接收
	TjReceivdate    int64  `gorm:"column:tj_receivdate;type:bigint(20);not null" json:"tj_receivdate"`         // 接收日期
}

// TableName get sql table name.获取数据库表名
func (m *TjLabresult) TableName() string {
	return "tj_labresult"
}

// ********************  TjLabresult ********************************* BEGIN

// GetLabresult ...
func (LabModel) GetLabresult(dto *LabresultDTO) (*[]TjLabresult, error) {
	var infos []TjLabresult
	var ret *gorm.DB

	ret = db.Table("tj_labresult").Where("1 = 1")

	if dto.Testid != "" {
		ret = ret.Where("tj_testid = ?", dto.Testid)
	}

	if dto.DtStart != "" && dto.DtStart != "0" {
		ret = ret.Where("tj_senddate >= ?", dto.DtStart)
	}

	if dto.DtEnd != "" && dto.DtEnd != "0" {
		ret = ret.Where("tj_senddate <= ?", dto.DtEnd)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Infof("Get tj_labresult error:%+v", result.Error)
		return nil, result.Error
	}

	return &infos, nil
}

// InsertLabresult ...
func (LabModel) InsertLabresult(info *[]TjLabresult) (*[]TjLabresult, error) {
	// logger.Log.Infof("Save tj_labresult:%+v", info)
	// var subinfo []TjLabresult
	var retinfo []TjLabresult

	ibatch := 500
	// log.Printf("总条数：%d\n", len(*info))
	inum := len(*info) / ibatch
	// log.Printf("总条数除 %d：%d\n", ibatch, inum)
	var err error
	// totallen := 0
	for i := 0; i <= inum; i++ {
		iend := (i + 1) * ibatch
		if iend > len(*info) {
			iend = len(*info)
		}
		subinfo := (*info)[i*ibatch : iend]
		if len(subinfo) <= 0 {
			break
		}
		// log.Printf("sub slice（切片）长度:%d\n", len(subinfo))
		err = db.Table("tj_labresult").Create(&subinfo).Error
		if err != nil {
			log.Printf("错误：%+v\n", err)
			break
		}
		retinfo = append(retinfo, subinfo...)
		// for _, v := range subinfo {
		// 	retinfo = append(retinfo, v)
		// }
	}

	if err != nil {
		logger.Log.Errorf("Save tj_labresult error:%+v", err)
		return nil, err
	}

	return &retinfo, nil
}

// // UpdateLabresult ...
// func (LabModel) UpdateLabresult(info *TjLabresult) error {

// 	logger.Log.Infof("update tj_labresult:%+v", info)
// 	result := db.Table("tj_labresult").Debug().Save(info)
// 	if result.Error != nil {
// 		return result.Error
// 	}
// 	return nil
// }

// DeleteLabresult ...
func (LabModel) DeleteLabresult(testids []string) error {
	if len(testids) <= 0 {
		return errors.New("testids are empty,not allowed to delete")
	}
	logger.Log.Infof("删除ID:%+v", testids)
	ret := db.Exec("delete from tj_labresult where tj_testid in (?)", testids).Debug()

	return ret.Error

}

// DeleteLabresult ...
func (LabModel) DeleteLabresultbyIDs(testids, origids []string) error {
	if len(testids) <= 0 {
		return errors.New("testids are empty,not allowed to delete")
	}
	logger.Log.Infof("删除数据 testid:%+v, itemcode:%+v", testids, origids)
	ret := db.Exec("delete from tj_labresult where tj_testid in (?) and tj_origrec in (?)", testids, origids)

	return ret.Error

}

// ********************  TjBarnameinfo ********************************* END
