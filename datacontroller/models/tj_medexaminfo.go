package models

import (
	"errors"
	"utility/logger"

	"gorm.io/gorm"
)

// MedexaminfoModel ...
type MedexaminfoModel struct{}

// TjMedexaminfo 患者体检信息表（患者每次体检的内容信息）
type TjMedexaminfo struct {
	ID              int64  `gorm:"column:id;auto_increment;primary_key;not null" json:"id"`            // primary key
	TjTestid        string `gorm:"unique;column:tj_testid;type:varchar(20);not null" json:"tj_testid"` // 检查编号(每次体检的编号)
	TjPid           string `gorm:"column:tj_pid;type:varchar(20);not null" json:"tj_pid"`
	TjAge           int    `gorm:"column:tj_age;type:int(11);not null" json:"tj_age"`                          // 年龄
	TjTestcat       int    `gorm:"column:tj_testcat;type:int(11);not null" json:"tj_testcat"`                  // 体检类别, 字典10
	TjTesttype      int    `gorm:"column:tj_testtype;type:int(11);not null" json:"tj_testtype"`                // 体检类型, 字典16
	TjTestsource    int    `gorm:"column:tj_testsource;type:int(11);not null" json:"tj_testsource"`            // 体检来源
	TjCorpnum       int    `gorm:"column:tj_corpnum;type:int(10);not null" json:"tj_corpnum"`                  // 企业id
	TjEmpid         string `gorm:"column:tj_empid;type:varchar(20);not null" json:"tj_empid"`                  // 工号
	TjWorkage       string `gorm:"column:tj_workage;type:varchar(50);not null" json:"tj_workage"`              // 工龄
	TjWtcode        string `gorm:"column:tj_wtcode;type:varchar(45);not null" json:"tj_wtcode"`                // 工种代码
	TjWorktype      string `gorm:"column:tj_worktype;type:varchar(256);not null" json:"tj_worktype"`           // 工种
	TjPoisionfactor string `gorm:"column:tj_poisionfactor;type:varchar(512);not null" json:"tj_poisionfactor"` // 毒害因素
	TjPoisionage    string `gorm:"column:tj_poisionage;type:varchar(50);not null" json:"tj_poisionage"`        // 接害工龄
	TjRecorddate    int64  `gorm:"column:tj_recorddate;type:bigint(20);not null" json:"tj_recorddate"`         // 登记日期，也就是预约日期
	TjRecorder      string `gorm:"column:tj_recorder;type:varchar(32);not null" json:"tj_recorder"`            // 登记人
	TjTestdate      int64  `gorm:"column:tj_testdate;type:bigint(20);not null" json:"tj_testdate"`             // 体检日期
	TjExpdate       int64  `gorm:"column:tj_expdate;type:bigint(20);not null" json:"tj_expdate"`               // 有效日期
	TjCompleted     int    `gorm:"column:tj_completed;type:int(4);not null" json:"tj_completed"`               // 已完成
	TjTotal         int    `gorm:"column:tj_total;type:int(4);not null" json:"tj_total"`                       // 总
	TjSubdate       int64  `gorm:"column:tj_subdate;type:bigint(20);not null" json:"tj_subdate"`               // 预约体检日期
	TjCheckstatus   int    `gorm:"column:tj_checkstatus;type:int(11);not null" json:"tj_checkstatus"`          // 体检状态 体检状态 1：预约 2：登记 3：正在体检 4：体检结束 5: 以总检 6：已报告，0：无进度
	TjPrintflag     int    `gorm:"column:tj_printflag;type:int(11);not null" json:"tj_printflag"`              // 打印标志 0：未打印，1：已打印
	TjPrinttimes    int    `gorm:"column:tj_printtimes;type:int(11);not null" json:"tj_printtimes"`            // 打印次数
	TjRptnum        string `gorm:"column:tj_rptnum;type:varchar(64);not null" json:"tj_rptnum"`                // 所属报告编号
	TjPeid          int    `gorm:"column:tj_peid;type:int(10);not null" json:"tj_peid"`                        // 所属单位体检编号，默认为0
	TjIsrecheck     int    `gorm:"column:tj_isrecheck;type:int(20);not null" json:"tj_isrecheck"`              // 是否复查， 0：否 1：是
	TjOldtestid     string `gorm:"column:tj_oldtestid;type:varchar(64);not null" json:"tj_oldtestid"`          //tj_oldtestid
	TjRechecktimes  int    `gorm:"column:tj_rechecktimes;type:int(4);not null" json:"tj_rechecktimes"`
	TjPush          int    `gorm:"column:tj_push;type:tinyint(1);not null" json:"tj_push"`                // 0：不是推送用户，1：是推送用户
	TjNum           int    `gorm:"column:tj_num;type:bigint(20);not null" json:"tj_num"`                  // 推送过来的预约编号
	TjPushstatus    int    `gorm:"column:tj_pushstatus;type:tinyint(1);not null" json:"tj_pushstatus"`    // 推送体检的状态
	TjUpload        int    `gorm:"column:tj_upload;type:tinyint(1);not null" json:"tj_upload"`            // 数据提交状态 0：未上传，1：已上传, 2：无需上传
	TjSyncstatus    int    `gorm:"column:tj_syncstatus;type:tinyint(1);not null" json:"tj_syncstatus"`    // 数据同步状态 0：未同步，1：预约 2：登记 3：正在体检 4：体检结束 5: 以总检 6：已报告
	TjPaymethod     int    `gorm:"column:tj_paymethod;type:tinyint(1);not null" json:"tj_paymethod"`      //支付方式 1：个人支付 2：企业支付
	TjPackagename   string `gorm:"column:tj_packagename;type:varchar(50);not null" json:"tj_packagename"` //套餐名称
	TjAdditional    string `gorm:"column:tj_additional;type:varchar(128);not null" json:"tj_additional"`  //tj_additional
}

// TableName get sql table name.获取数据库名字
func (TjMedexaminfo) TableName() string {
	return "tj_medexaminfo"
}

// FindMedinfoByTestID ... FindMedinfoByTestID
func (MedexaminfoModel) FindMedinfoByTestID(testids []string) ([]TjMedexaminfo, error) {
	if len(testids) <= 0 {
		return nil, errors.New("empty testids")
	}
	var infos []TjMedexaminfo
	// var ret *gorm.DB

	ret := db.Where("tj_testid in (?)", testids).Find(&infos)

	// ret.Find(&infos)
	return infos, ret.Error
}

// FindMedinfo ... FindByTestID
// func FindMedinfo(dtstart string, dtend string, testid []string, cpme int, corpid int,
// 	testtype int, statuslow int, statushigh int, pid []string, pname string, isrecheck int, rptnum string) (*[]TjMedexaminfo, error) {

// FindMedinfo ... FindByTestID
func (MedexaminfoModel) FindMedinfo(dto *MedQueryDTO) ([]TjMedexaminfo, error) {
	// func FindMedinfo(query *service.MedinfoDTO) (*[]TjMedexaminfo, error)
	if dto == nil {
		return nil, errors.New("empty condition")
	}
	var infos []TjMedexaminfo
	var ret *gorm.DB

	ret = db.Table("tj_medexaminfo").Where("1=1")

	if dto.Dtstart != "" && dto.Dtstart != "0" {
		ret = ret.Where("tj_testdate >= ?", dto.Dtstart)
	}

	if dto.Dtend != "" && dto.Dtend != "0" {
		ret = ret.Where("tj_testdate < ?", dto.Dtend)
	}

	if len(dto.Testid) > 0 {
		ret = ret.Where("tj_testid in (?)", dto.Testid)
	}

	if len(dto.Pid) > 0 {
		ret = ret.Where("tj_pid in (?)", dto.Pid)
	}

	if dto.Cpme > 0 {
		if dto.Cpme != 2 {
			ret = ret.Where("tj_peid = ?", dto.Cpme)
		} else {
			ret = ret.Where("tj_peid > 2")
		}
	}

	if dto.Corpid > 0 {
		if dto.Corpid == 1 {
			ret = ret.Where("tj_corpnum = 1")
		} else if dto.Corpid == 2 {
			ret = ret.Where("tj_corpnum >= 2")
		} else if dto.Corpid > 2 {
			ret = ret.Where("tj_corpnum = ?", dto.Corpid)
		}
	}

	if dto.Isrecheck > 0 {
		ret = ret.Where("tj_isrecheck > 0")
	}

	if dto.Rptnum != "" {
		ret = ret.Where("tj_rptnum like ?", "%"+dto.Rptnum+"%")
	}

	if dto.Testtype > 0 {
		ret = ret.Where("tj_testtype = ?", dto.Testtype)
	}

	if dto.Statuslow >= 0 {
		ret = ret.Where("tj_checkstatus >= ?", dto.Statuslow)
	}
	if dto.Statushigh > 0 {
		ret = ret.Where("tj_checkstatus <= ?", dto.Statushigh)
	}

	if dto.Pname != "" {
		// var patients []TjPatient
		// lrt := db.Table("tj_patient").Select("tj_pid").Where("tj_pname like ?", "%"+pname+"%").Find(&patients)
		// fmt.Printf("query 111: %+v\n", lrt.QueryExpr())
		// fmt.Printf("%+v\n", patients)
		// subQuery := db.Select("tj_pid").Where("tj_pname LIKE '?'", "%"+pname+"%").Table("tj_patient")
		// fmt.Printf("query 222: %s\n", subQuery.QueryExpr())
		ret = ret.Where("tj_pid in (select tj_pid from tj_patient where tj_pname like ?)", "%"+dto.Pname+"%").Debug()
		// ret = ret.Where("tj_pid in ?", db.Table("tj_patient").Select("tj_pid").Where("tj_pname like ?", "%"+pname+"%").map)
	}

	// ret = ret.Where("tj_checkstatus <> -1")

	// logger.Log.Debugf("%+v", )
	ret.Find(&infos)
	// logger.Log.Printf("查询结果：%+v\n", infos)
	return infos, ret.Error
}

// CreateMedinfo ... insert new tj_medexaminfo
func (MedexaminfoModel) CreateMedinfo(info *TjMedexaminfo) (*TjMedexaminfo, error) {
	if info == nil {
		return nil, errors.New("medinfo model is empty")
	}
	result := db.Table("tj_medexaminfo").Create(info)

	// logger.Log.Printf("insert medexaminfo ID: [%d]", info.ID)
	// logger.Log.Printf("insert medexaminfo: [%+v]", info)

	logger.Log.Infof("Insert medexaminfo: %+v", info)

	if result.Error != nil {
		logger.Log.Infof("Insert tj_medexaminfo error:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateMedinfo ... UpdateMedinfo new tj_medexaminfo
func (MedexaminfoModel) UpdateMedinfo(info *TjMedexaminfo) (*TjMedexaminfo, error) {
	if info == nil {
		return nil, errors.New("medinfo model is empty")
	}
	result := db.Table("tj_medexaminfo").Save(info)

	// logger.Log.Printf("insert medexaminfo ID: [%d]", info.ID)
	// logger.Log.Printf("insert medexaminfo: [%+v]", info)

	logger.Log.Infof("Update medexaminfo: %+v", info)

	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateMedinfoStatus ... UpdateMedinfo new tj_medexaminfo
func (MedexaminfoModel) UpdateMedinfoStatus(testid string, status int) error {

	result := db.Exec("update tj_medexaminfo set tj_checkstatus = ? where tj_testid = ?", status, testid).Debug()

	if result.Error != nil {
		logger.Log.Infof("Save tj_medexaminfo error:%+v", result.Error)
		return result.Error
	}

	logger.Log.Infof("更新体检信息状态成功，体检编号: %s，新状态：%d", testid, status)
	return nil
}

// UpdateMedinfoReportStatus ... UpdateMedinfo new tj_medexaminfo
func (MedexaminfoModel) UpdateMedinfoReportStatus(dto *MedReportDTO) error {

	result := db.Exec("update tj_medexaminfo set tj_rptnum = ?, tj_checkstatus = ? where tj_testid in ?", dto.Rptnum, dto.Status, dto.Testids).Debug()

	if result.Error != nil {
		logger.Log.Infof("Save tj_medexaminfo error:%+v", result.Error)
		return result.Error
	}

	logger.Log.Infof("更新体检信息状态成功，%+v", dto)
	return nil
}

// UpdateMedinfoAppointStatus ... UpdateMedinfo new tj_medexaminfo
func (MedexaminfoModel) UpdateMedinfoAppointStatus(peid int, status int, testdate int) error {

	result := db.Exec("update tj_medexaminfo set tj_checkstatus = status, tj_testdate = ? where tj_peid = ?", status, testdate, peid).Debug()

	if result.Error != nil {
		return result.Error
	}

	logger.Log.Infof("更新体检预约状态成功，体检单位编号: %d，体检日期：%d", peid, testdate)
	return nil
}

// DeleteMedinfo ... UpdateMedinfo new tj_medexaminfo
func (MedexaminfoModel) DeleteMedinfo(testids []string) error {

	if len(testids) <= 0 {
		return errors.New("empty testids")
	}
	result := db.Exec("update tj_medexaminfo set tj_checkstatus = -1 where tj_testid in (?)", testids).Debug()

	if result.Error != nil {
		return result.Error
	}

	logger.Log.Infof("删除体检信息成功，体检编号: %s", testids)
	return nil
}
