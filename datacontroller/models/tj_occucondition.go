package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// TjOccucondition 职业检查异常的判断条件
type TjOccucondition struct {
	ID          int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjTesttype  int    `gorm:"column:tj_testtype;type:int(10);not null" json:"tj_testtype"`          // 体检类型
	TjPoision   int    `gorm:"column:tj_poision;type:int(10);not null" json:"tj_poision"`            // 毒害因素
	TjSex       int    `gorm:"column:tj_sex;type:int(10);not null" json:"tj_sex"`                    // 性别
	TjItemid    string `gorm:"column:tj_itemid;type:varchar(10);not null" json:"tj_itemid"`          // 体检项目
	TjCondition string `gorm:"column:tj_condition;type:varchar(10);not null" json:"tj_condition"`    // 参考运算
	TjRefvalue  string `gorm:"column:tj_refvalue;type:varchar(20);not null" json:"tj_refvalue"`      // 参考值
	TjConnector string `gorm:"column:tj_connector;type:varchar(20);not null" json:"tj_connector"`    // 连接符 (and or)
}

// TableName get sql table name.获取数据库表名
func (TjOccucondition) TableName() string {
	return "tj_occucondition"
}

// ********************  TjOccucondition ********************************* BEGIN

// GetOccucondition ...
func (DiseaseModel) GetOccucondition(dto *OccuconditionDTO) (*[]TjOccucondition, error) {
	var infos []TjOccucondition
	var ret *gorm.DB

	ret = db.Table("tj_occucondition").Where("1 = 1")

	if dto.Testtype > 0 {
		ret = ret.Where("tj_testtype = ?", dto.Testtype)
	}

	if dto.Poisionid > 0 {
		ret = ret.Where("tj_poision = ?", dto.Poisionid)
	}

	if dto.Itemid != "" {
		ret = ret.Where("tj_itemid = ?", dto.Itemid)
	}
	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertOccucondition ...
func (DiseaseModel) InsertOccucondition(info *TjOccucondition) (*TjOccucondition, error) {
	logger.Log.Infof("Save tj_occucondition:%+v", info)
	result := db.Table("tj_occucondition").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateOccucondition ...
func (DiseaseModel) UpdateOccucondition(info *TjOccucondition) error {

	logger.Log.Infof("update tj_occucondition:%+v", info)
	result := db.Table("tj_occucondition").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteOccucondition ...
func (DiseaseModel) DeleteOccucondition(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_occucondition where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjOccucondition ********************************* END
