package models

import (
	"utility/logger"

	"gorm.io/gorm"
)

// PackageModel ...
type PackageModel struct{}

// TjPackageinfo 套餐信息表
type TjPackageinfo struct {
	ID          int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjPnum      string `gorm:"column:tj_pnum;type:varchar(15);not null" json:"tj_pnum"`              // 编号
	TjPname     string `gorm:"column:tj_pname;type:varchar(60);not null" json:"tj_pname"`            // 套餐名称
	TjPrice     string `gorm:"column:tj_price;type:varchar(20);not null" json:"tj_price"`            // 价格
	TjType      int    `gorm:"column:tj_type;type:int(11);not null" json:"tj_type"`                  // 套餐类别 男性1？女性2? 通用3？
	TjMemo      string `gorm:"column:tj_memo;type:varchar(256);not null" json:"tj_memo"`             // 备注
	TjShoworder int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`        // 显示顺序
	TjFitrange  int    `gorm:"column:tj_fitrange;type:int(11);not null" json:"tj_fitrange"`          // 适用范围。个人？单位？通用
	TjPyjm      string `gorm:"column:tj_pyjm;type:varchar(30);not null" json:"tj_pyjm"`
	TjZdym      string `gorm:"column:tj_zdym;type:varchar(10);not null" json:"tj_zdym"`
	TjFlag      int    `gorm:"column:tj_flag;type:int(11);not null" json:"tj_flag"`          // 有效标志
	TjOperator  int    `gorm:"column:tj_operator;type:int(10);not null" json:"tj_operator"`  // 最后修改人
	TjModdate   int64  `gorm:"column:tj_moddate;type:bigint(20);not null" json:"tj_moddate"` // 修改日期
}

// TableName get sql table name.获取数据库表名
func (TjPackageinfo) TableName() string {
	return "tj_packageinfo"
}

// TjPackagedetail 套餐明细
type TjPackagedetail struct {
	ID          int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjPnum      int    `gorm:"column:tj_pnum;type:int(10);not null" json:"tj_pnum"`                  // 套餐编号
	TjItemid    string `gorm:"column:tj_itemid;type:varchar(12);not null" json:"tj_itemid"`          // 项目编号
	TjDeptid    string `gorm:"column:tj_deptid;type:varchar(10);not null" json:"tj_deptid"`          // 科室编码
	TjShoworder int    `gorm:"column:tj_showorder;type:int(11);not null" json:"tj_showorder"`        // 显示顺序
}

// TableName get sql table name.获取数据库表名
func (TjPackagedetail) TableName() string {
	return "tj_packagedetail"
}

// ********************  TjPackageinfo ********************************* BEGIN

// GetPackageinfo ...
func (PackageModel) GetPackageinfo(pnum string) (*[]TjPackageinfo, error) {
	var infos []TjPackageinfo
	var ret *gorm.DB

	ret = db.Table("tj_packageinfo").Where("1 = 1")

	if pnum != "" && pnum != "0" {
		ret = ret.Where("tj_pnum = ?", pnum)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertPackageinfo ...
func (PackageModel) InsertPackageinfo(info *TjPackageinfo) (*TjPackageinfo, error) {
	logger.Log.Infof("Save tj_packageinfo:%+v", info)
	result := db.Table("tj_packageinfo").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdatePackageinfo ...
func (PackageModel) UpdatePackageinfo(info *TjPackageinfo) error {

	logger.Log.Infof("update tj_packageinfo:%+v", info)
	result := db.Table("tj_packageinfo").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeletePackageinfo ...
func (PackageModel) DeletePackageinfo(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("delete from tj_packageinfo where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjPackageinfo ********************************* END

// ********************  TjPackagedetail ********************************* BEGIN

// GetPackagedetails ...
func (PackageModel) GetPackagedetails(pnum int) (*[]TjPackagedetail, error) {
	var infos []TjPackagedetail
	var ret *gorm.DB

	ret = db.Table("tj_packagedetail").Where("1 = 1")

	if pnum > 0 {
		ret = ret.Where("tj_pnum = ?", pnum)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertPackagedetail ...
func (PackageModel) InsertPackagedetail(info *TjPackagedetail) (*TjPackagedetail, error) {
	logger.Log.Infof("Save tj_packagedetail:%+v", info)
	result := db.Table("tj_packagedetail").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdatePackagedetail ...
func (PackageModel) UpdatePackagedetail(info *TjPackagedetail) error {

	logger.Log.Infof("update tj_packagedetail:%+v", info)
	result := db.Table("tj_packagedetail").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeletePackagedetail ...
func (PackageModel) DeletePackagedetail(dto *PackagedelDTO) error {
	logger.Log.Infof("删除 :%+v", dto)
	// ret := db.Exec("delete from tj_packagedetail where tj_pnum = ?", pnum).Debug()
	var ret *gorm.DB

	ret = db.Table("tj_packagedetail").Where("1 = 1")

	if dto.Pnum > 0 {
		ret = ret.Where("tj_pnum = ?", dto.Pnum)
	}

	if dto.ID > 0 {
		ret = ret.Where("id = ?", dto.ID)
	}

	result := ret.Debug().Delete(&TjPackagedetail{})
	return result.Error

}

// // DeletePackagedetailbynum ...
// func (PackageModel) DeletePackagedetailbynum(pnum int) error {
// 	logger.Log.Infof("删除,pnum:%d", pnum)
// 	ret := db.Exec("delete from tj_packagedetail where tj_pnum = ?", pnum).Debug()

// 	return ret.Error

// }

// // DeletePackagedetailByID ...
// func (PackageModel) DeletePackagedetailByID(id int) error {
// 	logger.Log.Infof("删除,pnum:%d", id)
// 	ret := db.Exec("delete from tj_packagedetail where id = ?", id).Debug()

// 	return ret.Error

// }

// ********************  tj_packagedetail ********************************* END
