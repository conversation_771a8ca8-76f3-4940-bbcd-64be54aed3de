package models

import (
	"errors"
	"utility/logger"
)

// PacsModel ...
type PacsModel struct{}

// TjPacsresult [...]
type TjPacsresult struct {
	ID               int64  `gorm:"primary_key;auto_increment;column:id;type:bigint(10);not null" json:"id"` // primary key
	TjTestid         string `gorm:"column:tj_testid;type:varchar(20);not null" json:"tj_testid"`
	TjPatientname    string `gorm:"column:tj_patientname;type:varchar(50);not null" json:"tj_patientname"`
	TjItemid         string `gorm:"column:tj_itemid;type:varchar(20);not null" json:"tj_itemid"`     // 项目id
	TjItemname       string `gorm:"column:tj_itemname;type:varchar(50);not null" json:"tj_itemname"` // 项目名称
	TjJclx           string `gorm:"column:tj_jclx;type:varchar(15);not null" json:"tj_jclx"`         // 检查类型：US,ES,ECG,DR,CT,CR
	TjJcxm           string `gorm:"column:tj_jcxm;type:varchar(50);not null" json:"tj_jcxm"`         // 检查项目
	TjJcmc           string `gorm:"column:tj_jcmc;type:varchar(150);not null" json:"tj_jcmc"`        // 检查名称
	TjJcys           string `gorm:"column:tj_jcys;type:varchar(25);not null" json:"tj_jcys"`         // 检查医生
	TjSxys           string `gorm:"column:tj_sxys;type:varchar(25);not null" json:"tj_sxys"`         // 书写医生
	TjBgys           string `gorm:"column:tj_bgys;type:varchar(25);not null" json:"tj_bgys"`         // 审核医生
	TjSfyc           int32  `gorm:"column:tj_sfyc;type:int(4);not null" json:"tj_sfyc"`              // 报告日期
	TjImporter       string `gorm:"column:tj_importer;type:varchar(25);not null" json:"tj_importer"`
	TjImagesight     string `gorm:"column:tj_imagesight;type:varchar(2000);not null" json:"tj_imagesight"`         // 影像所见
	TjImagediagnosis string `gorm:"column:tj_imagediagnosis;type:varchar(2000);not null" json:"tj_imagediagnosis"` // 检查结果
	TjBgrq           int64  `gorm:"column:tj_bgrq;type:bigint(20);not null" json:"tj_bgrq"`                        // 报告日期
	TjImportdate     int64  `gorm:"column:tj_importdate;type:bigint(20);not null" json:"tj_importdate"`            // 导入日期
}

// TableName get sql table name.获取数据库表名
func (TjPacsresult) TableName() string {
	return "tj_pacsresult"
}

// FindPacsResults ...
func (PacsModel) FindPacsResults(testid string) (*[]TjPacsresult, error) {
	if testid == "" {
		return nil, errors.New("体检编号不能为空")
	}
	var infos []TjPacsresult
	err := db.Debug().Table("tj_pacsresult").Where("tj_testid = ?", testid).Find(&infos).Error

	if err != nil {
		return nil, err
	}
	return &infos, nil
}

// CreatePacsResults ...
func (PacsModel) CreatePacsResults(infos *[]TjPacsresult) (*[]TjPacsresult, error) {
	if infos == nil {
		return nil, errors.New("插入信息不能为空")
	}
	logger.Log.Infof("开始插入pacsreuslt：%+v", infos)
	err := db.Debug().Table("tj_pacsresult").Create(infos).Error

	if err != nil {
		logger.Log.Errorf("插入pacsresult错误：%+v", err)
		return nil, err
	}
	return infos, nil
}

// DeletePacsResult ...
func (PacsModel) DeletePacsResult(testid string) error {
	if testid == "" {
		return errors.New("体检编号不能为空")
	}

	err := db.Table("tj_pacsresult").Where("tj_testid = ?", testid).Delete(&TjPacsresult{}).Error

	if err != nil {
		return err
	}
	return nil
}

// DeletePacsResultbyIDS ...
func (PacsModel) DeletePacsResultbyIDS(testid, jclx []string) error {
	if len(testid) <= 0 {
		return errors.New("体检编号不能为空")
	}

	err := db.Table("tj_pacsresult").Where("tj_testid in (?) and tj_jclx in (?)", testid, jclx).Delete(&TjPacsresult{}).Error

	if err != nil {
		logger.Log.Errorf("错误：%+v", err)
		return err
	}
	return nil
}
