package models

import (
	"errors"
	"utility/logger"

	"gorm.io/gorm"
)

// PatientModel ...
type PatientModel struct{}

// TjPatient 患者信息表
type TjPatient struct {
	ID          int    `gorm:"column:id;auto_increment;primary_key;not null" json:"id"`           // primary key
	TjPid       string `gorm:"unique;column:tj_pid;type:varchar(20);not null" json:"tj_pid"`      // 患者编号(体检号)
	TjPname     string `gorm:"column:tj_pname;type:varchar(20);not null" json:"tj_pname"`         // 姓名
	TjPsex      int    `gorm:"column:tj_psex;type:int(11);not null" json:"tj_psex"`               // 性别
	TjPmarriage int    `gorm:"column:tj_pmarriage;type:int(11);not null" json:"tj_pmarriage"`     // 婚姻状况
	TjNation    string `gorm:"column:tj_nation;type:varchar(20);not null" json:"tj_nation"`       // 民族
	TjPtestnum  int    `gorm:"column:tj_ptestnum;type:int(11);not null" json:"tj_ptestnum"`       // 体检次数
	TjPaddress  string `gorm:"column:tj_paddress;type:varchar(256);not null" json:"tj_paddress"`  // 地址
	TjPphone    string `gorm:"column:tj_pphone;type:varchar(32);not null" json:"tj_pphone"`       // 联系电话
	TjPemail    string `gorm:"column:tj_pemail;type:varchar(100);not null" json:"tj_pemail"`      // email邮箱
	TjPbirthday string `gorm:"column:tj_pbirthday;type:varchar(20);not null" json:"tj_pbirthday"` // 出生年月
	TjPidcard   string `gorm:"column:tj_pidcard;type:varchar(25);not null" json:"tj_pidcard"`     // 身份证号
	TjCardtype  int    `gorm:"column:tj_cardtype;type:int(10);not null" json:"tj_cardtype"`       // 登记人
	TjPcareer   string `gorm:"column:tj_pcareer;type:varchar(600);not null" json:"tj_pcareer"`    // 职业
	TjPmobile   string `gorm:"column:tj_pmobile;type:varchar(80);not null" json:"tj_pmobile"`     // 手机号码
	TjPhoto     string `gorm:"column:tj_photo;type:varchar(150);not null" json:"tj_photo"`        // 体检者照片信息
	TjCryptflag int    `gorm:"column:tj_cryptflag;type:tinyint(4);not null" json:"tj_cryptflag"`  // 是否加密 0：未加密 1：加密
	TjPopdate   int    `gorm:"column:tj_popdate;type:bigint(20);not null" json:"tj_popdate"`      // 登记日期
	TjStaffid   int    `gorm:"column:tj_staffid;type:int(10);not null" json:"tj_staffid"`         // 登记人
	TjPmemo     string `gorm:"column:tj_pmemo;type:varchar(20);not null" json:"tj_pmemo"`         // 备注
	TjSyncflag  int    `gorm:"column:tj_syncflag;type:tinyint(1);not null" json:"tj_syncflag"`    // 数据提交状态 0：未同步，1：已同步
}

// TableName get sql table name.获取数据库名字
func (TjPatient) TableName() string {
	return "tj_patient"
}

// TjPatienthazards 毒害因素检查项目信息表
type TjPatienthazards struct {
	ID             int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjTestid       string `gorm:"column:tj_testid;type:varchar(20);not null" json:"tj_testid"`          // 体检编号
	TjHid          int    `gorm:"column:tj_hid;type:int(10);not null" json:"tj_hid"`                    // 毒害因素编号
	TjPoisionage   string `gorm:"column:tj_poisionage;type:varchar(50);not null" json:"tj_poisionage"`
	TjTypeid       int    `gorm:"column:tj_typeid;type:int(4);not null" json:"tj_typeid"` // 结果编码
	TjDiseases     string `gorm:"column:tj_diseases;type:varchar(50);not null" json:"tj_diseases"`
	TjRecheckitems string `gorm:"column:tj_recheckitems;type:varchar(50);not null" json:"tj_recheckitems"`
}

// TableName get sql table name.获取数据库表名
func (TjPatienthazards) TableName() string {
	return "tj_patienthazards"
}

// TjDiseaseinfo 疾病情况表
type TjDiseaseinfo struct {
	ID       int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	TjTestid string `gorm:"column:tj_testid;type:varchar(20);not null" json:"tj_testid"`          // 检查编码
	TjDisid  int    `gorm:"column:tj_disid;type:int(10);not null" json:"tj_disid"`                // 疾病编码
	// TjDiseases    TjDiseases `gorm:"joinForeignKey:tj_disid;foreignKey:ID" json:"tj_diseases_list"`                                             // 所有疾病情况信息表
	TjDiseasenum  string `gorm:"column:tj_diseasenum;type:varchar(20);not null" json:"tj_diseasenum"`    // 疾病编码
	TjDiseasename string `gorm:"column:tj_diseasename;type:varchar(400);not null" json:"tj_diseasename"` // 疾病名字
	TjSuggestion  string `gorm:"column:tj_suggestion;type:varchar(2000);not null" json:"tj_suggestion"`  // 建议信息
	TjDeptid      string `gorm:"column:tj_deptid;type:varchar(12);not null" json:"tj_deptid"`            // 科室编号
	TjIsdisease   int    `gorm:"column:tj_isdisease;type:int(11);not null" json:"tj_isdisease"`          // 是否疾病
	TjIsoccu      int    `gorm:"column:tj_isoccu;type:int(11);not null" json:"tj_isoccu"`                // 是否职业病
	TjTypeid      int    `gorm:"column:tj_typeid;type:int(10);not null" json:"tj_typeid"`
	TjOpinion     string `gorm:"column:tj_opinion;type:varchar(450);not null" json:"tj_opinion"`
	TjShoworder   int    `gorm:"column:tj_showorder;type:int(10);not null" json:"tj_showorder"`
}

// TableName get sql table name.获取数据库表名
func (TjDiseaseinfo) TableName() string {
	return "tj_diseaseinfo"
}

// CreatePatientinfo ... InsertPatientinfo
func (PatientModel) CreatePatientinfo(info *TjPatient) (*TjPatient, error) {

	logger.Log.Infof("添加体检者信息: %+v", info)
	ret := db.Table("tj_patient").Debug().Create(info)
	if ret.Error != nil {
		logger.Log.Infof("Save tj_patient error:%+v", ret.Error)
		return nil, ret.Error
	}

	return info, nil
}

// UpdatePatientinfo ... UpdatePatientinfo
func (PatientModel) UpdatePatientinfo(info *TjPatient) (*TjPatient, error) {
	if info == nil {
		return nil, errors.New("TjPatient model is empty")
	}
	result := db.Table("tj_patient").Save(info)

	// log.Printf("insert medexaminfo ID: [%d]", info.ID)
	// log.Printf("insert medexaminfo: [%+v]", info)

	// logger.Log.Infof("Update TjPatient: %+v", info)

	if result.Error != nil {
		logger.Log.Infof("update tj_patient error:%+v", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// GetPatients ... GetPatientsbyPids
func (PatientModel) GetPatients(dto *PtQueryDTO) ([]TjPatient, error) {
	var infos []TjPatient
	var ret *gorm.DB
	// log.Printf("开始查找体检者信息:%+v\n", dto)
	ret = db.Table("tj_patient").Where("1=1")
	if dto.Pname != "" {
		ret = ret.Where("tj_pname like ?", "%"+dto.Pname+"%")
	}
	if len(dto.Pids) > 0 {
		ret = ret.Where("tj_pid in (?)", dto.Pids)
	}
	if len(dto.Testids) > 0 {
		ret = ret.Where("tj_pid in (select tj_pid from tj_medexaminfo where tj_testid in (?))", dto.Testids)
	}
	if dto.Idcard != "" {
		ret = ret.Where("tj_pidcard = ?", dto.Idcard)
	}

	ret.Find(&infos)
	return infos, ret.Error
}

// GetPatientsbyIdcard ... GetPatientsbyIdcard
func (PatientModel) GetPatientsbyIdcard(pval *[]string) (*[]TjPatient, error) {
	var infos []TjPatient
	// var ret *gorm.DB

	ret := db.Table("tj_patient").Debug().Where("tj_pidcard in (?)", pval).Find(&infos)

	// logger.Log.Debugf("%+v", ret.QueryExpr())
	// ret.Debug().Find(&infos)
	return &infos, ret.Error
}

// GetPatientsbyIdcard ... GetPatientsbyIdcard
func (PatientModel) GetPatientsbyPid(pid string) (TjPatient, error) {
	var infos TjPatient
	// var ret *gorm.DB

	ret := db.Table("tj_patient").Debug().Where("tj_pid = ?", pid).First(&infos)

	// logger.Log.Debugf("%+v", ret.QueryExpr())
	// ret.Debug().Find(&infos)
	return infos, ret.Error
}

// ***************patient hazards ************************

// CreatePatientHazards ... insert patient hazards
func (PatientModel) CreatePatientHazards(pval *[]TjPatienthazards) (*[]TjPatienthazards, error) {
	ret := db.Table("tj_patienthazards").Create(pval)

	return pval, ret.Error
}

func (PatientModel) UpdatePatientHazards(pval *TjPatienthazards) error {
	ret := db.Table("tj_patienthazards").Save(pval)

	return ret.Error
}

// GetPatientHazards ...
func (PatientModel) GetPatientHazards(testid []string) ([]TjPatienthazards, error) {
	var infos []TjPatienthazards
	//db.Where("name <> ?", "jinzhu").Find(&users)
	ret := db.Table("tj_patienthazards").Debug().Where("tj_testid in (?)", testid).Find(&infos)
	if ret.Error != nil {
		logger.Log.Infof("Save tj_patienthazards error:%+v", ret.Error)
		return nil, ret.Error
	}

	return infos, nil
}

// DeletePatientHazards ... 删除体检者的毒害因素
func (PatientModel) DeletePatientHazards(testid []string) error {
	//// Delete with additional conditions
	//db.Where("name = ?", "jinzhu").Delete(&email)
	if len(testid) <= 0 {
		return errors.New("没有体检编号，不能删除")
	}
	logger.Log.Infof("删除体检者毒害因素: %s", testid)
	ret := db.Table("tj_patienthazards").Where("tj_testid in (?)", testid).Delete(TjPatienthazards{})
	return ret.Error
}

// ***************patient disease ************************

// CreatePatientDiseases ... insert patient hazards
func (PatientModel) CreatePatientDiseases(pval *[]TjDiseaseinfo) (*[]TjDiseaseinfo, error) {
	if len(*pval) <= 0 {
		return nil, errors.New("empty data")
	}
	ret := db.Table("tj_diseaseinfo").Create(pval)

	return pval, ret.Error
}

// SavePatientDiseases ...
func (PatientModel) SavePatientDiseases(pval *TjDiseaseinfo) (*TjDiseaseinfo, error) {
	if pval == nil {
		return nil, errors.New("empty query dto")
	}
	ret := db.Table("tj_diseaseinfo").Save(pval).Debug()

	return pval, ret.Error
}

// GetPatientDiseases ...
func (PatientModel) GetPatientDiseases(dto *PatientDiseaseDTO) (*[]TjDiseaseinfo, error) {
	if dto == nil {
		return nil, errors.New("empty query dto")
	}
	var infos []TjDiseaseinfo
	ret := db.Table("tj_diseaseinfo").Where("1 = 1")
	if dto.Testid != "" {
		ret = ret.Where("tj_testid = ?", dto.Testid)
	}
	if dto.Deptid != "" {
		ret = ret.Where("tj_deptid = ?", dto.Deptid)
	}
	if dto.Disid > 0 {
		ret = ret.Where("tj_disid = ?", dto.Disid)
	}
	if dto.ID > 0 {
		ret = ret.Where("ID = ?", dto.ID)
	}
	ret.Find(&infos)
	if ret.Error != nil {
		return nil, ret.Error
	}
	return &infos, nil
}

// DeletePatientDiseases ...
func (PatientModel) DeletePatientDiseases(dto *PatientDiseaseDTO) error {
	// var infos []TjDiseaseinfo
	if dto == nil {
		return errors.New("empty query dto")
	}
	ret := db.Table("tj_diseaseinfo").Where("1 = 1")
	if dto.Testid != "" {
		ret = ret.Where("tj_testid = ?", dto.Testid)
	}
	if dto.Deptid != "" {
		ret = ret.Where("tj_deptid = ?", dto.Deptid)
	}
	if dto.Disid > 0 {
		ret = ret.Where("tj_disid = ?", dto.Disid)
	}
	if dto.ID > 0 {
		ret = ret.Where("ID = ?", dto.ID)
	}
	ret.Delete(TjDiseaseinfo{})
	if ret.Error != nil {
		return ret.Error
	}
	return nil
}

// DeletePatientDisease ...
func (PatientModel) DeletePatientDisease(testid []string, disids []int) error {
	// var infos []TjDiseaseinfo
	ret := db.Table("tj_diseaseinfo").Where("1 = 1")

	if len(testid) > 0 {
		ret = ret.Where("tj_testid in (?)", testid)
	}
	if len(disids) > 0 {
		ret = ret.Where("tj_disid in (?)", disids)
	}

	ret.Debug().Delete(TjDiseaseinfo{})
	if ret.Error != nil {
		return ret.Error
	}
	return nil
}

// // DeletePatientDiseases ... 删除体检者的疾病
// func DeletePatientDiseases(testid string, deptid string) error {
// 	//// Delete with additional conditions
// 	//db.Where("name = ?", "jinzhu").Delete(&email)
// 	if len(testid) <= 0 {
// 		return errors.New("没有体检编号，不能删除")
// 	}
// 	logger.Log.Infof("删除体检者疾病信息,体检号：%s，科室：%s", testid, deptid)
// 	ret := db.Where("tj_testid = ? and tj_deptid = ?", testid, deptid).Delete(TjDiseaseinfo{})
// 	return ret.Error
// }

// // DeletePatientDisease ... 删除体检者的疾病信息
// func DeletePatientDisease(id int) error {
// 	//// Delete with additional conditions
// 	//db.Where("name = ?", "jinzhu").Delete(&email)
// 	if id <= 0 {
// 		return errors.New("没有ID，不能删除")
// 	}
// 	logger.Log.Infof("删除体检者疾病信息: %d", id)
// 	ret := db.Where("ID = ?", id).Delete(TjDiseaseinfo{})
// 	return ret.Error
// }
