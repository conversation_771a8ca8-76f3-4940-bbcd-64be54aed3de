package models

import (
	"errors"
	// "nodip_ego/api/app"
	"utility/logger"
)

type QuestionnairModel struct {
	Healthyinfo TjHealthyInfo         `json:"healthyinfo"`
	Ocuhis      []TjOccupationHistory `json:"ocuhis"`
	Dishis      []TjDiseaseHistory    `json:"dishis"`
	Marriage    []TjMarriageHistory   `json:"marriage"`
}

// TjHealthInfo ... 问卷检查信息
type TjHealthyInfo struct {
	ID                int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	TjTestID          string `gorm:"column:tj_testid" json:"tj_testid"`
	TjPID             string `gorm:"column:tj_pid" json:"tj_pid"`
	TjSurveydate      int    `gorm:"column:tj_surveydate" json:"tj_surveydate"`
	TjSmoke           int    `gorm:"column:tj_smoke" json:"tj_smoke"`
	TjSmokenum        string `gorm:"column:tj_smokenum" json:"tj_smokenum"`
	TjSmokeyear       string `gorm:"column:tj_smokeyear" json:"tj_smokeyear"`
	TjDrink           int    `gorm:"column:tj_drink" json:"tj_drink"`
	TjDrinknum        string `gorm:"column:tj_drinknum" json:"tj_drinknum"`
	TjDrinkyear       string `gorm:"column:tj_drinkyear" json:"tj_drinkyear"`
	TjChildrennum     string `gorm:"column:tj_childrennum" json:"tj_childrennum"`
	TjAbortionnum     string `gorm:"column:tj_abortionnum" json:"tj_abortionnum"`
	TjStillbirthnum   string `gorm:"column:tj_stillbirthnum" json:"tj_stillbirthnum"`
	TjPrematurenum    string `gorm:"column:tj_prematurenum" json:"tj_prematurenum"`
	TjAbnormalnum     string `gorm:"column:tj_abnormalnum" json:"tj_abnormalnum"`
	TjChildrenhealthy string `gorm:"column:tj_childrenhealthy" json:"tj_childrenhealthy"`
	TjMenarcheage     string `gorm:"column:tj_menarcheage" json:"tj_menarcheage"`
	TjPeriod          string `gorm:"column:tj_period" json:"tj_period"`
	TjCycle           string `gorm:"column:tj_cycle" json:"tj_cycle"`
	TjMenopauseage    string `gorm:"column:tj_menopauseage" json:"tj_menopauseage"`
	TjModtime         int64  `gorm:"column:tj_modtime" json:"tj_modtime"`
}

// TableName 重写表名
func (TjHealthyInfo) TableName() string {
	return "tj_healthyinfo"
}

// TjOccupationHistory ... 职业史
type TjOccupationHistory struct {
	ID              int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	TjTestID        string `gorm:"column:tj_testid" json:"tj_testid"`
	TjStartdate     int    `gorm:"column:tj_startdate" json:"tj_startdate"`
	TjEnddate       int    `gorm:"column:tj_enddate" json:"tj_enddate"`
	TjCorpname      string `gorm:"column:tj_corpname" json:"tj_corpname"`
	TjWorkshop      string `gorm:"column:tj_workshop" json:"tj_workshop"`
	TjWorktype      string `gorm:"column:tj_worktype" json:"tj_worktype"`
	TjWorktypename  string `gorm:"column:tj_worktypename" json:"tj_worktypename"`
	TjHarmful       string `gorm:"column:tj_harmful" json:"tj_harmful"`
	TjHarmfulname   string `gorm:"column:tj_harmfulname" json:"tj_harmfulname"`
	TjProtective    string `gorm:"column:tj_protective" json:"tj_protective"`
	TjRaddaynum     string `gorm:"column:tj_raddaynum" json:"tj_raddaynum"`
	TjRadtotalnum   string `gorm:"column:tj_radtotalnum" json:"tj_radtotalnum"`
	TjRadoverdose   string `gorm:"column:tj_radoverdose" json:"tj_radoverdose"`
	TjRadexposure   string `gorm:"column:tj_radexposure" json:"tj_radexposure"`
	TjRadcode       string `gorm:"column:tj_radcode" json:"tj_radcode"`
	TjRadtype       string `gorm:"column:tj_radtype" json:"tj_radtype"`
	TjRadprotective string `gorm:"column:tj_radprotective" json:"tj_radprotective"`
}

// TableName 重写表名
func (TjOccupationHistory) TableName() string {
	return "tj_occupationhistory"
}

// TjDiseaseHistory ... 既往病史,包括职业病史
type TjDiseaseHistory struct {
	ID          int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	TjTestID    string `gorm:"column:tj_testid" json:"tj_testid"`
	TjDate      int    `gorm:"column:tj_date" json:"tj_date"`
	TjDisname   string `gorm:"column:tj_disname" json:"tj_disname"`
	TjOrgname   string `gorm:"column:tj_orgname" json:"tj_orgname"`
	TjCurve     string `gorm:"column:tj_curve" json:"tj_curve"`         // 治疗经过
	TjIsRecover int    `gorm:"column:tj_isrecover" json:"tj_isrecover"` //是否痊愈
	TjFinalCode string `gorm:"column:tj_finalcode" json:"tj_finalcode"` //转归
	TjIsoccu    int    `gorm:"column:tj_isoccu" json:"tj_isoccu"`       //是否职业病 0:否， 1:是
}

// TableName 重写表名
func (TjDiseaseHistory) TableName() string {
	return "tj_diseasehistory"
}

// TjMarriageHistory ... 婚姻史
type TjMarriageHistory struct {
	ID                int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	TjTestID          string `gorm:"column:tj_testid" json:"tj_testid"`
	TjDate            int    `gorm:"column:tj_date" json:"tj_date"`
	TjSpouseRadiation string `gorm:"column:tj_spouseradiation" json:"tj_spouseradiation"`
	TjSpouseOccu      string `gorm:"column:tj_spouseoccu" json:"tj_spouseoccu"`
}

// TableName 重写表名
func (TjMarriageHistory) TableName() string {
	return "tj_marriagehistory"
}

// CreateQuestionnairinfo ... CreateQuestionnairinfo
func (QuestionnairModel) CreateQuestionnairinfo(info *TjHealthyInfo) (*TjHealthyInfo, error) {

	// logger.Log.Infof("添加问卷信息: %+v", info)
	ret := db.Table("tj_healthyinfo").Debug().Create(info)
	if ret.Error != nil {
		logger.Log.Errorf("Save tj_healthyinfo error:%+v", ret.Error)
		return nil, ret.Error
	}
	// info.ID = ret.inser
	return info, nil
}

func (QuestionnairModel) UpdateQuestionnairinfo(info *TjHealthyInfo) error {

	ret := db.Table("tj_healthyinfo").Debug().Save(info)
	if ret.Error != nil {
		return ret.Error
	}
	return nil
}

func (QuestionnairModel) QueryQuestionnairinfo(testid string) (TjHealthyInfo, error) {

	// logger.Log.Infof("查找问卷信息: %+v", testid)
	// var retinfo []TjHealthyInfo
	retinfo := TjHealthyInfo{}
	// Get first matched record
	ret := db.Where("tj_testid = ?", testid).First(&retinfo)

	if ret.Error != nil {
		logger.Log.Errorf("query tj_healthyinfo by testid error:%+v", ret)
		return retinfo, ret.Error
	}
	logger.Log.Infof("query healthyinfo result:%+v", retinfo)
	// if ret.Error != nil && ret.Error.Error() == "record not found"
	return retinfo, nil
	// if len(retinfo) > 0 {
	// 	return &retinfo[0], nil
	// } else {
	// 	return nil, nil
	// }
}

func (QuestionnairModel) QueryQuestionnairInfobyPid(pid string) (TjHealthyInfo, error) {

	logger.Log.Infof("根据档案号:%s查询问卷信息", pid)
	var retinfo []TjHealthyInfo
	// Get first matched record
	ret := db.Debug().Where("tj_pid = ? order by tj_testid desc", pid).Find(&retinfo)
	logger.Log.Infof("根据档案号:%s查询问卷信息的结果：%+v", pid, ret)

	if ret.Error != nil {
		logger.Log.Errorf("query tj_healthyinfo by pid error:%+v", ret)
		return TjHealthyInfo{}, ret.Error
	}
	// if ret.Error != nil && ret.Error.Error() == "record not found"

	if len(retinfo) > 0 {
		return retinfo[0], nil
	} else {
		return TjHealthyInfo{}, errors.New("Record NOT Found")
	}
}

// CreateQuestionnairinfo ... CreateQuestionnairinfo
func (QuestionnairModel) DeleteQuestionnairinfo(testid string) error {

	logger.Log.Infof("删除问卷信息，体检号: %s", testid)

	ret := db.Table("tj_healthyinfo").Where("tj_testid = ?", testid).Delete(TjHealthyInfo{})
	// DELETE from emails where email LIKE "%jinzhu%";
	if ret.Error != nil {
		logger.Log.Errorf("delete tj_healthyinfo error:%+v", ret.Error)
		return ret.Error
	}

	return nil
}

// CreateOccupationHistories ... CreateOccupationHistories
func (QuestionnairModel) CreateOccupationHistories(info []TjOccupationHistory) (*[]TjOccupationHistory, error) {

	// logger.Log.Infof("添加职业史信息: %+v", info)
	ret := db.Table("tj_occupationhistory").Debug().CreateInBatches(info, len(info))
	if ret.Error != nil {
		logger.Log.Errorf("添加职业史信息 error:%+v", ret.Error)
		return nil, ret.Error
	}

	return &info, nil
}

func (QuestionnairModel) QueryOccupationHistories(testid string) ([]TjOccupationHistory, error) {

	logger.Log.Infof("query 职业史信息: %s", testid)
	var retinfo []TjOccupationHistory
	// Get first matched record
	ret := db.Table("tj_occupationhistory").Debug().Where("tj_testid = ?", testid).Find(&retinfo)

	if ret.Error != nil {
		logger.Log.Errorf("query tj_occupationhistory error:%+v", ret.Error)
		return retinfo, ret.Error
	}

	return retinfo, nil
}

// DeleteOccupationHistories ... DeleteOccupationHistories
func (QuestionnairModel) DeleteOccupationHistories(testid string) error {

	logger.Log.Infof("删除职业史信息，体检号: %s", testid)

	ret := db.Table("tj_occupationhistory").Where("tj_testid = ?", testid).Delete(TjOccupationHistory{})
	// DELETE from emails where email LIKE "%jinzhu%";
	if ret.Error != nil {
		logger.Log.Errorf("delete tj_occupationhistory error:%+v", ret.Error)
		return ret.Error
	}

	return nil
}

// CreateDiseaseHistories ... CreateDiseaseHistories
func (QuestionnairModel) CreateDiseaseHistories(info []TjDiseaseHistory) (*[]TjDiseaseHistory, error) {

	logger.Log.Infof("添加既往史信息: %+v", info)
	ret := db.Table("tj_diseasehistory").Debug().CreateInBatches(info, len(info))
	if ret.Error != nil {
		logger.Log.Errorf("添加既往史信息 error:%+v", ret.Error)
		return nil, ret.Error
	}

	return &info, nil
}

func (QuestionnairModel) QueryDiseaseHistories(testid string) ([]TjDiseaseHistory, error) {

	logger.Log.Infof("query 既往史信息: %s", testid)
	var retinfo []TjDiseaseHistory
	// Get first matched record
	ret := db.Table("tj_diseasehistory").Debug().Where("tj_testid = ?", testid).Find(&retinfo)

	if ret.Error != nil {
		logger.Log.Errorf("query tj_diseasehistory error:%+v", ret.Error)
		return retinfo, ret.Error
	}

	return retinfo, nil
}

// DeleteDiseaseHistories ... DeleteDiseaseHistories
func (QuestionnairModel) DeleteDiseaseHistories(testid string) error {

	logger.Log.Infof("删除问卷信息，体检号: %s", testid)

	ret := db.Table("tj_diseasehistory").Where("tj_testid = ?", testid).Delete(TjDiseaseHistory{})
	// DELETE from emails where email LIKE "%jinzhu%";
	if ret.Error != nil {
		logger.Log.Errorf("delete tj_diseasehistory error:%+v", ret.Error)
		return ret.Error
	}

	return nil
}

// CreateMarriageHistories ... CreateMarriageHistories
func (QuestionnairModel) CreateMarriageHistories(info []TjMarriageHistory) (*[]TjMarriageHistory, error) {

	logger.Log.Infof("添加婚姻史信息: %+v", info)
	ret := db.Table("tj_marriagehistory").Debug().CreateInBatches(info, len(info))
	if ret.Error != nil {
		logger.Log.Errorf("添加婚姻史信息 error:%+v", ret.Error)
		return nil, ret.Error
	}

	return &info, nil
}

func (QuestionnairModel) QueryMarriageHistories(testid string) ([]TjMarriageHistory, error) {

	logger.Log.Infof("query 婚姻史信息: %s", testid)
	var retinfo []TjMarriageHistory
	// Get first matched record
	ret := db.Table("tj_marriagehistory").Debug().Where("tj_testid = ?", testid).Find(&retinfo)

	if ret.Error != nil {
		logger.Log.Errorf("query tj_marriagehistory error:%+v", ret.Error)
		return retinfo, ret.Error
	}

	return retinfo, nil
}

// DeleteMarriageHistories ... DeleteMarriageHistories
func (QuestionnairModel) DeleteMarriageHistories(testid string) error {

	logger.Log.Infof("删除婚姻史信息，体检号: %s", testid)

	ret := db.Table("tj_marriagehistory").Where("tj_testid = ?", testid).Delete(TjMarriageHistory{})
	// DELETE from emails where email LIKE "%jinzhu%";
	if ret.Error != nil {
		logger.Log.Errorf("delete tj_marriagehistory error:%+v", ret.Error)
		return ret.Error
	}

	return nil
}
