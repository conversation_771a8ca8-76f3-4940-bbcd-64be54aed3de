package models

import (
	"log"
	"utility/logger"

	"gorm.io/gorm"
)

// ReportModel ...
type ReportModel struct{}

// TjCorpoccureport 单位职业体检报告（包括职业病和非职业病）
type TjCorpoccureport struct {
	ID             int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjCorpid       string `gorm:"column:tj_corpid;type:varchar(20);not null" json:"tj_corpid"`          // 单位编号
	TjCorpname     string `gorm:"column:tj_corpname;type:varchar(100);not null" json:"tj_corpname"`     // 用人单位
	TjWtcorpname   string `gorm:"column:tj_wtcorpname;type:varchar(50);not null" json:"tj_wtcorpname"`  // 委托单位名字
	TjStarttime    int64  `gorm:"column:tj_starttime;type:bigint(20);not null" json:"tj_starttime"`
	TjEndtime      int64  `gorm:"column:tj_endtime;type:bigint(20);not null" json:"tj_endtime"`
	TjTestyear     int    `gorm:"column:tj_testyear;type:int(11);not null" json:"tj_testyear"`             // 体检年份
	TjTesttype     int    `gorm:"column:tj_testtype;type:int(11);not null" json:"tj_testtype"`             // 体检类型
	TjTypename     string `gorm:"column:tj_typename;type:varchar(300);not null" json:"tj_typename"`        // 类型中文名字
	TjPoisions     string `gorm:"column:tj_poisions;type:mediumtext;not null" json:"tj_poisions"`          // 毒害因素
	TjTestdate     string `gorm:"column:tj_testdate;type:varchar(260);not null" json:"tj_testdate"`        // 体检日期
	TjTestaddress  string `gorm:"column:tj_testaddress;type:varchar(200);not null" json:"tj_testaddress"`  // 体检地址
	TjPeoplenum    int    `gorm:"column:tj_peoplenum;type:int(11);not null" json:"tj_peoplenum"`           // 体检人数
	TjApeoplenum   int    `gorm:"column:tj_apeoplenum;type:int(10);not null" json:"tj_apeoplenum"`         // 应检人数
	TjTestitems    string `gorm:"column:tj_testitems;type:varchar(500);not null" json:"tj_testitems"`      // 体检项目
	TjEvalaw       string `gorm:"column:tj_evalaw;type:mediumtext;not null" json:"tj_evalaw"`              // 评价依据
	TjTestlaw      string `gorm:"column:tj_testlaw;type:mediumtext;not null" json:"tj_testlaw"`            // 体检依据
	TjResult       string `gorm:"column:tj_result;type:mediumtext;not null" json:"tj_result"`              // 体检结论
	TjCreatedate   int64  `gorm:"column:tj_createdate;type:bigint(20);not null" json:"tj_createdate"`      // 创建时间
	TjModdate      int64  `gorm:"column:tj_moddate;type:bigint(20);not null" json:"tj_moddate"`            // 修改时间
	TjCreator      string `gorm:"column:tj_creator;type:varchar(40);not null" json:"tj_creator"`           // 体检人
	TjModifier     string `gorm:"column:tj_modifier;type:varchar(40);not null" json:"tj_modifier"`         // 修改人
	TjReportnum    string `gorm:"column:tj_reportnum;type:varchar(100);not null" json:"tj_reportnum"`      // 报告编号
	TjStatus       int    `gorm:"column:tj_status;type:int(11);not null" json:"tj_status"`                 // 状态标志
	TjPeid         int    `gorm:"column:tj_peid;type:int(11);not null" json:"tj_peid"`                     // 所属单位体检编号
	TjPages        int    `gorm:"column:tj_pages;type:int(11);not null" json:"tj_pages"`                   // 报表页数
	TjIsrecheck    int    `gorm:"column:tj_isrecheck;type:int(10);not null" json:"tj_isrecheck"`           // 是否复查
	TjOrptid       int    `gorm:"column:tj_orptid;type:int(10);not null" json:"tj_orptid"`                 // 是否复查
	TjReportnumint string `gorm:"column:tj_reportnumint;type:varchar(45);not null" json:"tj_reportnumint"` // 报告内部编号
	TjPyjm         string `gorm:"column:tj_pyjm;type:varchar(45);not null" json:"tj_pyjm"`                 // 单位的拼音简码
	TjReporttype   int    `gorm:"column:tj_reporttype;type:int(11);not null" json:"tj_reporttype"`         // 报告类型：0,职业健康体检报告 1，健康体检报告
	TjSyncflag     int    `gorm:"column:tj_syncflag;type:tinyint(1);not null" json:"tj_syncflag"`          // 数据提交状态 0：未同步，1：已同步
	TjMemo         string `gorm:"column:tj_memo;type:varchar(500);not null" json:"tj_memo"`                //备注说明
}

// TableName get sql table name.获取数据库表名
func (TjCorpoccureport) TableName() string {
	return "tj_corpoccureport"
}

// TjCorpoccureportFc 企业报告和体检人员关联关系表
type TjCorpoccureportFc struct {
	ID          int64  `gorm:"primary_key;auto_increment;column:id;type:bigint(20);not null" json:"id"`
	TjOldTestid string `gorm:"column:tj_old_testid;type:varchar(20);not null" json:"tj_old_testid"`
	TjReportID  int64  `gorm:"column:tj_report_id;type:bigint(4);not null" json:"tj_report_id"`
	TjOldRptnum string `gorm:"column:tj_old_rptnum;type:varchar(20);not null" json:"tj_old_rptnum"`
	TjNewTestid string `gorm:"column:tj_new_testid;type:varchar(20);not null" json:"tj_new_testid"`
	TjNewRptnum string `gorm:"column:tj_new_rptnum;type:varchar(20);not null" json:"tj_new_rptnum"`
	Cdatetime   int64  `gorm:"column:cdatetime;type:bigint(20);not null" json:"cdatetime"`
}

// TableName get sql table name.获取数据库表名
func (TjCorpoccureportFc) TableName() string {
	return "tj_corpoccureport_fc"
}

// TjCorpoccureportInfo 企业报告和体检人员关联关系表
type TjCorpoccureportInfo struct {
	ID         int64  `gorm:"primary_key;auto_increment;column:id;type:bigint(20);not null" json:"id"`
	TjReportID int64  `gorm:"column:tj_report_id;type:bigint(20);not null" json:"tj_report_id"`
	TjTestID   string `gorm:"column:tj_test_id;type:varchar(20);not null" json:"tj_test_id"` // testid
	TjRptnum   string `gorm:"column:tj_rptnum;type:varchar(45);not null" json:"tj_rptnum"`
}

// TableName get sql table name.获取数据库表名
func (TjCorpoccureportInfo) TableName() string {
	return "tj_corpoccureport_info"
}

// ********************  TjCorpoccureport ********************************* BEGIN

func (ReportModel) GetCorpoccureportinfo(report_id int64, report_num string) (*TjCorpoccureport, error) {
	var rpt TjCorpoccureport
	var ret *gorm.DB

	// logger.Log.Infof("开始根据报告编号查找报告信息")
	ret = db.Table("tj_corpoccureport").Where("1 = 1")
	if report_id > 0 {
		ret = ret.Where("ID = ?", report_id)
	}

	if report_num != "" {
		ret = ret.Where("tj_reportnumint = ?", report_num)
	}

	result := ret.Debug().First(&rpt)
	if result.Error != nil {
		logger.Log.Errorf("错误信息：%+v\n", result.Error)
		return nil, result.Error
	}

	// logger.Log.Infof("完成")
	return &rpt, nil
}

// GetCorpoccureport ...
func (ReportModel) GetCorpoccureport(dto *ReportDTO) ([]TjCorpoccureport, error) {
	var infos []TjCorpoccureport
	var ret *gorm.DB

	ret = db.Table("tj_corpoccureport").Where("tj_status = 1")

	if len(dto.Reportids) > 0 {
		ret = ret.Where("id in ?", dto.Reportids)
	}

	if dto.Dtstart != "" && dto.Dtstart != "0" {
		ret = ret.Where("tj_createdate >= ?", dto.Dtstart)
	}

	if dto.Dtend != "" && dto.Dtend != "0" {
		ret = ret.Where("tj_createdate <= ?", dto.Dtend)
	}

	if dto.Reporttype > -1 {
		ret = ret.Where("tj_reporttype = ?", dto.Reporttype)
	}

	if dto.Corp != "" && dto.Corp != "2" {
		ret = ret.Where("tj_corpid = ?", dto.Corp)
	}

	if dto.Type != "" && dto.Type != "0" {
		ret = ret.Where("tj_testtype = ?", dto.Type)
	}

	if dto.Patientname != "" {
		//1.首先根据姓名从tj_patient表中查找体检者信息
		//2.根据tj_pid从tj_medexaminfo表中查找体检者信息
		//3.根据tj_rptnum获取报告信息
		var ptdto PtQueryDTO
		ptdto.Pname = dto.Patientname
		log.Printf("根据姓名查找体检者信息：%+v\n", ptdto)
		var patientModel = new(PatientModel)

		pt, err := patientModel.GetPatients(&ptdto)
		if err != nil {
			return nil, err
		}
		if len(pt) > 0 {
			var mddto MedQueryDTO
			for _, v := range pt {
				mddto.Pid = append(mddto.Pid, v.TjPid)
			}
			log.Printf("根据Tid查找体检信息：%+v\n", mddto)
			var medinfoModel = new(MedexaminfoModel)
			med, err := medinfoModel.FindMedinfo(&mddto)
			if err != nil {
				return nil, err
			}
			var rptnum []string
			if len(med) > 0 {
				for _, v := range med {
					if v.TjRptnum == "" {
						continue
					}
					rptnum = append(rptnum, v.TjRptnum)
				}
			}
			log.Printf("根据reportnum查找报告信息：%+v\n", mddto)
			// if len(rptnum) > 0 {
			ret = ret.Where("tj_reportnumint in (?)", rptnum)
			// }
		} else {
			return infos, nil
		}
	}

	if dto.Testid != "" {
		var mddto MedQueryDTO
		mddto.Testid = append(mddto.Testid, dto.Testid)
		var medinfoModel = new(MedexaminfoModel)
		log.Printf("根据testid查找体检信息：%+v\n", mddto)
		med, err := medinfoModel.FindMedinfo(&mddto)
		if err != nil {
			logger.Log.Errorf("错误信息：%+v\n", err)
			return nil, err
		}
		var rptnum []string
		if len(med) > 0 {
			for _, v := range med {
				if v.TjRptnum == "" {
					continue
				}
				rptnum = append(rptnum, v.TjRptnum)
			}
		}
		log.Printf("根据reportnum查找报告信息：%+v\n", mddto)
		// if len(rptnum) > 0 {
		ret = ret.Where("tj_reportnumint in (?)", rptnum)
		// }
	}

	if dto.Reportnum != "" {
		ret = ret.Where("tj_reportnumint like ?", "%"+dto.Reportnum+"%")
	}

	if dto.Orptid > 0 {
		ret = ret.Where("tj_orptid = ?", dto.Orptid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		logger.Log.Errorf("错误信息：%+v\n", result.Error)
		return nil, result.Error
	}

	return infos, nil
}

// InsertCorpoccureport ...
func (ReportModel) InsertCorpoccureport(info *TjCorpoccureport) (*TjCorpoccureport, error) {
	logger.Log.Infof("Save tj_corpoccureport:%+v", info)
	result := db.Table("tj_corpoccureport").Debug().Create(info)
	if result.Error != nil {
		logger.Log.Errorf("错误信息：%+v\n", result.Error)
		return nil, result.Error
	}

	return info, nil
}

// UpdateCorpoccureport ...
func (ReportModel) UpdateCorpoccureport(info *TjCorpoccureport) error {

	logger.Log.Infof("update tj_corpoccureport:%+v", info)
	result := db.Table("tj_corpoccureport").Debug().Save(info)
	if result.Error != nil {
		logger.Log.Errorf("错误信息：%+v\n", result.Error)
		return result.Error
	}
	return nil
}

// DeleteCorpoccureport ...
func (ReportModel) DeleteCorpoccureport(id int) error {
	logger.Log.Infof("删除ID:%d", id)

	// var ret *gorm.DB
	// ret = db.Table("tj_corpoccureport").Where("1 = 1")
	// if len(dto.Binum) > 0 {
	// 	ret = ret.Where("tj_binum in (?)", dto.Binum)
	// }
	// if len(dto.Itemid) > 0 {
	// 	ret = ret.Where("tj_itemid in (?)", dto.Itemid)
	// }

	// result := ret.Delete(&TjCorpoccureport{}).Debug()
	result := db.Exec("delete from tj_corpoccureport where id = ?", id).Debug()

	return result.Error

}

// ********************  TjCorpoccureport ********************************* END

// ********************  TjCorpoccureportInfo ********************************* BEGIN

// GetCorpoccureportInfo ...
func (ReportModel) GetCorpoccureportInfo(dto *ReportinfoDTO) ([]TjCorpoccureportInfo, error) {
	var infos []TjCorpoccureportInfo
	var ret *gorm.DB

	ret = db.Table("tj_corpoccureport_info").Where("1 = 1")

	if len(dto.Reportids) > 0 {
		ret = ret.Where("tj_report_id in ?", dto.Reportids)
	}

	if len(dto.Testids) > 0 {
		ret = ret.Where("tj_test_id in ?", dto.Testids)
	}
	result := ret.Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return infos, nil
}

// InsertCorpoccureportInfo ...
func (ReportModel) InsertCorpoccureportInfo(info *[]TjCorpoccureportInfo) (*[]TjCorpoccureportInfo, error) {
	// logger.Log.Infof("Save tj_corpoccureport_info:%+v", info)

	result := db.Table("tj_corpoccureport_info").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	//update tj_medexaminfo表中的status和report num
	// strsql := s := fmt.Sprintf("a %s", "string")
	// strsql :=  fmt.Sprintf("update tj_medexaminfo set tj_rptnum = '', tj_checkstatus = %d where tj_testid = '%s'", info.
	return info, nil
}

func (ReportModel) DeleteCorpoccureportInfos(testids []string) error {
	// logger.Log.Infof("删除体检号:%+v", testids)
	ret := db.Exec("delete from tj_corpoccureport_info where tj_test_id in ?", testids).Debug()

	return ret.Error

}

// UpdateCorpoccureportInfo ...
func (ReportModel) UpdateCorpoccureportInfo(info *TjCorpoccureportInfo) error {

	// logger.Log.Infof("update tj_corpoccureport_info:%+v", info)
	result := db.Table("tj_corpoccureport_info").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteCorpoccureportInfo ...
func (ReportModel) DeleteCorpoccureportInfo(rptid string) error {
	// logger.Log.Infof("删除ID:%s", rptid)
	ret := db.Exec("delete from tj_corpoccureport_info where tj_report_id = ?", rptid).Debug()

	return ret.Error

}

// ********************  TjCorpoccureportInfo ********************************* END

// ********************  TjCorpoccureportFc ********************************* BEGIN

// GetCorpoccureportFc ...
func (ReportModel) GetCorpoccureportFc(dto *ReportinfoDTO) (*[]TjCorpoccureportFc, error) {
	var infos []TjCorpoccureportFc
	var ret *gorm.DB

	ret = db.Table("tj_corpoccureport_fc").Where("1 = 1")

	if len(dto.Reportids) > 0 {
		ret = ret.Where("tj_old_rptnum in ?", dto.Reportids)
	}

	if len(dto.Testids) > 0 {
		ret = ret.Where("tj_old_testid in ?", dto.Testids)
	}

	if len(dto.Rptids) > 0 {
		ret = ret.Where("tj_report_id in ?", dto.Rptids)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertCorpoccureportFc ...
func (ReportModel) InsertCorpoccureportFc(info *TjCorpoccureportFc) (*TjCorpoccureportFc, error) {
	logger.Log.Infof("Save tj_corpoccureport_fc:%+v", info)
	result := db.Table("tj_corpoccureport_fc").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateCorpoccureportFc ...
func (ReportModel) UpdateCorpoccureportFc(info *TjCorpoccureportFc) error {

	logger.Log.Infof("update tj_corpoccureport_fc:%+v", info)
	result := db.Table("tj_corpoccureport_fc").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteCorpoccureportFc ...
func (ReportModel) DeleteCorpoccureportFc(newtestid []string) error {

	logger.Log.Infof("删除ID:%s", newtestid)
	ret := db.Exec("delete from tj_corpoccureport_fc where tj_new_testid in ?", newtestid).Debug()
	logger.Log.Infof("删除复查信息结果：%+v", ret)

	return ret.Error

}

// ********************  TjCorpoccureportFc ********************************* END
