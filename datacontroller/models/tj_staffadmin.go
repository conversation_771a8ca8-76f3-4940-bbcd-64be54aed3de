package models

import (
	"time"
	"utility/logger"

	"gorm.io/gorm"
	// "html"
	// "log"
	// "strings"
	// "time"
)

// TjStaffadmin 员工管理
type TjStaffadmin struct {
	ID             int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"`  // primary key
	TjStaffno      string `gorm:"column:tj_staffno;type:varchar(10);not null" json:"tj_staffno"`         //  工号(用户ID)
	TjStaffname    string `gorm:"column:tj_staffname;type:varchar(40);not null" json:"tj_staffname"`     // 名字
	TjSex          int    `gorm:"column:tj_sex;type:int(11);not null" json:"tj_sex"`                     // 性别
	TjDeptid       int    `gorm:"column:tj_deptid;type:int(10);not null" json:"tj_deptid"`               // 科室编码
	TjGroupid      int    `gorm:"column:tj_groupid;type:int(11);not null" json:"tj_groupid"`             // 组别ID
	TjPassword     string `gorm:"column:tj_password;type:varchar(128);not null" json:"tj_password"`      // password
	TjRole         int    `gorm:"column:tj_role;type:int(11);not null" json:"tj_role"`                   // 角色
	TjCheckallflag int    `gorm:"column:tj_checkallflag;type:int(11);not null" json:"tj_checkallflag"`   // 总检权限
	TjTitle        string `gorm:"column:tj_title;type:varchar(100);not null" json:"tj_title"`            // 职位
	TjStatus       int    `gorm:"column:tj_status;type:int(11);not null" json:"tj_status"`               // 状态，有效或者无效
	TjOperator     string `gorm:"column:tj_operator;type:varchar(10);not null" json:"tj_operator"`       // 最后修改人
	TjModdate      int64  `gorm:"column:tj_moddate;type:bigint(20);not null" json:"tj_moddate"`          // 最后修改日期
	TjMemo         string `gorm:"column:tj_memo;type:varchar(300);not null" json:"tj_memo"`              // 备注
	TjIsadmin      uint8  `gorm:"column:tj_isadmin;type:tinyint(3) unsigned;not null" json:"tj_isadmin"` // 是否管理员
	LoginSession   string `gorm:"column:login_session;type:varchar(100);not null" json:"login_session"`
	TjEsign        string `gorm:"column:tj_esign;type:varchar(55);not null" json:"tj_esign"` // 电子签名照的文件名
}

// TableName get sql table name.获取数据库表名
func (m *TjStaffadmin) TableName() string {
	return "tj_staffadmin"
}

// TjStaffright 用户权限信息表
type TjStaffright struct {
	ID         int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjStaffid  int    `gorm:"column:tj_staffid;type:int(10);not null" json:"tj_staffid"`            // 用户编码
	TjRname    string `gorm:"column:tj_rname;type:varchar(50);not null" json:"tj_rname"`            // 权限名称(菜单项)
	TjRight    int    `gorm:"column:tj_right;type:int(11);not null" json:"tj_right"`                // 权限标志
	TjOperator int    `gorm:"column:tj_operator;type:int(10);not null" json:"tj_operator"`          // 最后修改人
	TjModdate  int64  `gorm:"column:tj_moddate;type:bigint(20);not null" json:"tj_moddate"`         // 最后修改日期
	TjMenuid   int    `gorm:"column:tj_menuid;type:int(10);not null" json:"tj_menuid"`              // 菜单id
}

// TableName get sql table name.获取数据库表名
func (TjStaffright) TableName() string {
	return "tj_staffright"
}

// TjStaffdept 员工权属科室信息表
type TjStaffdept struct {
	ID        int    `gorm:"primary_key;auto_increment;column:id;type:int(10);not null" json:"id"` // primary key
	TjStaffid int    `gorm:"column:tj_staffid;type:int(10);not null" json:"tj_staffid"`
	TjDeptid  string `gorm:"column:tj_deptid;type:varchar(10);not null" json:"tj_deptid"`
}

// TableName get sql table name.获取数据库表名
func (TjStaffdept) TableName() string {
	return "tj_staffdept"
}

// StaffadminModel ...
type StaffadminModel struct{}

// LoginHistory ... LoginHistory
type LoginHistory struct {
	ID             int    `gorm:"column:id;auto_increment;primary_key" json:"id"`
	Userid         int    `gorm:"column:user_id" json:"user_id"`
	UserTerminal   string `gorm:"column:user_terminal" json:"user_terminal"`
	LoginTimestamp int64  `gorm:"column:login_timestamp" json:"login_timestamp"`
}

// TableName 会将 User 的表名重写为 `profiles`
func (LoginHistory) TableName() string {
	return "login_history"
}

// FindByNo ... tjstaffadmin
func (m StaffadminModel) FindByNo(uno string) (*TjStaffadmin, error) {
	var err error
	var u = TjStaffadmin{}
	err = db.Debug().Model(TjStaffadmin{}).Where("tj_staffno = ?", uno).Take(&u).Error
	if err != nil {
		return &TjStaffadmin{}, err
	}
	// if gorm.IsRecordNotFoundError(err) {
	// 	return &TjStaffadmin{}, errors.New("User Not Found")
	// }
	return &u, err
}

// FindByNo ... tjstaffadmin
func (m StaffadminModel) FindByID(id string) (TjStaffadmin, error) {
	var err error
	var u = TjStaffadmin{}
	err = db.Debug().Model(TjStaffadmin{}).Where("id = ?", id).Take(&u).Error
	if err != nil {
		return TjStaffadmin{}, err
	}
	// if gorm.IsRecordNotFoundError(err) {
	// 	return &TjStaffadmin{}, errors.New("User Not Found")
	// }
	return u, err
}

// UpdateLoginSession ... tjstaffadmin
func (m StaffadminModel) UpdateLoginSession(staff *TjStaffadmin) error {

	ret := db.Model(staff).Update("login_session", staff.LoginSession)
	return ret.Error
}

// SaveLoginHistory ... tjstaffadmin
func (m StaffadminModel) SaveLoginHistory(staff *TjStaffadmin) (*LoginHistory, error) {

	lhis := LoginHistory{
		Userid:         staff.ID,
		UserTerminal:   "X86",
		LoginTimestamp: time.Now().Unix(),
	}
	result := db.Table("login_history").Debug().Create(&lhis)
	// println("insert result: %s", result)
	// fmt.Printf("插入历史记录结果：%+v\n", lhis) // Print with Variable Name

	return &lhis, result.Error
}

// ********************  TjStaffadmin ********************************* BEGIN

// GetStaffadmins ...
func (StaffadminModel) GetStaffadmins(groupid int) (*[]TjStaffadmin, error) {
	var infos []TjStaffadmin
	var ret *gorm.DB

	ret = db.Table("tj_staffadmin").Where("tj_status = 1")

	if groupid > 0 {
		ret = ret.Where("tj_groupid = ?", groupid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// GetStaffadmins ...
func (StaffadminModel) GetStaffadminsbyIDs(groupid int) (*[]TjStaffadmin, error) {
	var infos []TjStaffadmin
	var ret *gorm.DB

	ret = db.Table("tj_staffadmin").Where("tj_status = 1")

	if groupid > 0 {
		ret = ret.Where("tj_groupid = ?", groupid)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertStaffadmin ...
func (StaffadminModel) InsertStaffadmin(info *TjStaffadmin) (*TjStaffadmin, error) {
	logger.Log.Infof("Save tj_staffadmin:%+v", info)
	result := db.Table("tj_staffadmin").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// UpdateStaffadmin ...
func (StaffadminModel) UpdateStaffadmin(info *TjStaffadmin) error {

	logger.Log.Infof("update tj_staffadmin:%+v", info)
	result := db.Table("tj_staffadmin").Debug().Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteStaffadmin ...
func (StaffadminModel) DeleteStaffadmin(id int) error {
	logger.Log.Infof("删除ID:%d", id)
	ret := db.Exec("update tj_staffadmin set tj_status = 0 where id = ?", id).Debug()

	return ret.Error

}

// ********************  TjStaffadmin ********************************* END

// ********************  TjStaffright ********************************* BEGIN

// GetStaffrights ...
func (StaffadminModel) GetStaffrights(id int) (*[]TjStaffright, error) {
	var infos []TjStaffright
	var ret *gorm.DB

	ret = db.Table("tj_staffright").Where("1 = 1")

	if id > 0 {
		ret = ret.Where("tj_staffid = ?", id)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertStaffrights ...
func (StaffadminModel) InsertStaffrights(info *[]TjStaffright) (*[]TjStaffright, error) {
	logger.Log.Infof("Save tj_staffright:%+v", info)
	result := db.Table("tj_staffright").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// // UpdateStaffright ...
// func (StaffadminModel) UpdateStaffright(info *TjStaffright) error {

// 	logger.Log.Infof("update tj_staffright:%+v", info)
// 	result := db.Table("tj_staffright").Debug().Save(info)
// 	if result.Error != nil {
// 		return result.Error
// 	}
// 	return nil
// }

// DeleteStaffright ...
func (StaffadminModel) DeleteStaffright(staffid int) error {
	logger.Log.Infof("删除ID:%d", staffid)
	ret := db.Exec("delete from tj_staffright where tj_staffid = ?", staffid).Debug()

	return ret.Error

}

// ********************  TjStaffright ********************************* END

// ********************  TjStaffdept ********************************* BEGIN

// GetStaffdept ...
func (StaffadminModel) GetStaffdept(dto *StaffDeptDTO) (*[]TjStaffdept, error) {
	var infos []TjStaffdept
	var ret *gorm.DB

	ret = db.Table("tj_staffdept").Where("1 = 1")

	if dto.StaffID > 0 {
		ret = ret.Where("tj_staffid = ?", dto.StaffID)
	}

	if dto.DeptID != "" && dto.DeptID != "0" {
		ret = ret.Where("tj_deptid = ?", dto.DeptID)
	}

	result := ret.Debug().Find(&infos)

	if result.Error != nil {
		return nil, result.Error
	}

	return &infos, nil
}

// InsertStaffdept ...
func (StaffadminModel) InsertStaffdept(info *TjStaffdept) (*TjStaffdept, error) {
	logger.Log.Infof("Save tj_staffdept:%+v", info)
	result := db.Table("tj_staffdept").Debug().Create(info)
	if result.Error != nil {
		return nil, result.Error
	}

	return info, nil
}

// // UpdateStaffdept ...
// func (StaffadminModel) UpdateStaffdept(info *TjStaffdept) error {

// 	logger.Log.Infof("update tj_staffdept:%+v", info)
// 	result := db.Table("tj_staffdept").Debug().Save(info)
// 	if result.Error != nil {
// 		return result.Error
// 	}
// 	return nil
// }

// DeleteStaffdept ...
func (StaffadminModel) DeleteStaffdept(id int) error {
	logger.Log.Infof("删除 tj_staffdept ID:%d", id)
	ret := db.Exec("delete from tj_staffdept where id = ?", id).Debug()

	return ret.Error

}

// ********************  Staffdept ********************************* END
