package models

import (
	"errors"
	"utility/logger"

	"gorm.io/gorm"
)

// TjTestsummary 体检小结表(科室小结表)
type TjTestsummary struct {
	ID            int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"`  // primary key
	TjTestid      string `gorm:"column:tj_testid;type:varchar(20);not null" json:"tj_testid"`           // 体检号
	TjDeptid      string `gorm:"column:tj_deptid;type:varchar(10);not null" json:"tj_deptid"`           // 科室编号
	TjSummary     string `gorm:"column:tj_summary;type:varchar(4000);not null" json:"tj_summary"`       // 小结内容
	TjSuggestion  string `gorm:"column:tj_suggestion;type:varchar(4000);not null" json:"tj_suggestion"` // 诊断建议
	TjIsfinished  int    `gorm:"column:tj_isfinished;type:int(11);not null" json:"tj_isfinished"`       // 结束标志
	TjDoctorID    string `gorm:"column:tj_doctorid;type:varchar(10);not null" json:"tj_doctorid"`
	TjDoctor      string `gorm:"column:tj_doctor;type:varchar(10);not null" json:"tj_doctor"`           // 小结医生
	TjDate        int64  `gorm:"column:tj_date;type:bigint(20);not null" json:"tj_date"`                // 小结日期
	TjForceend    int    `gorm:"column:tj_forceend;type:int(11);not null" json:"tj_forceend"`           // 是否强制结束
	TjCheckdoctor string `gorm:"column:tj_checkdoctor;type:varchar(20);not null" json:"tj_checkdoctor"` // 审核医生(confirm???)
	TjCheckdate   int64  `gorm:"column:tj_checkdate;type:bigint(20);not null" json:"tj_checkdate"`      // 审核日期 (confirm??? vb)
}

// TableName get sql table name.获取数据库表名
func (TjTestsummary) TableName() string {
	return "tj_testsummary"
}

// TestSummaryModel ...
type TestSummaryModel struct{}

// GetTestsummary ... query test summary
func (TestSummaryModel) GetTestsummary(testid string, deptid string) (*[]TjTestsummary, error) {
	if testid == "" {
		return nil, errors.New("体检号为空，不能查找")
	}
	var infos []TjTestsummary
	var ret *gorm.DB
	ret = db.Table("tj_testsummary").Where("tj_testid = ?", testid)

	if deptid != "" {
		ret = ret.Where("tj_deptid = ?", deptid)
	}

	result := ret.Debug().Find(&infos)
	if result.Error != nil {
		return nil, result.Error
	}
	return &infos, nil
}

// GetTestsummaries ... query test summary
func (TestSummaryModel) GetTestsummaries(dto *TsQueryDTO) (*[]TjTestsummary, error) {
	if dto == nil {
		return nil, errors.New("查找条件为空")
	}
	var infos []TjTestsummary
	var ret *gorm.DB

	ret = db.Table("tj_testsummary").Where("1=1")

	if dto.Stdate != "" && dto.Stdate != "0" {
		ret = ret.Where("tj_checkdate > ?", dto.Stdate)
	}
	if dto.Enddate != "" && dto.Enddate != "0" {
		ret = ret.Where("tj_checkdate < ?", dto.Enddate)
	}
	if len(dto.Testid) > 0 {
		ret = ret.Where("tj_testid in (?)", dto.Testid)
	}
	if len(dto.Deptid) > 0 {
		ret = ret.Where("tj_deptid in (?)", dto.Deptid)
	}

	if dto.IsFinished != -1 {
		ret = ret.Where("tj_isfinished = ?", dto.IsFinished)
	}

	if dto.IsForceend != -1 {
		ret = ret.Where("tj_forceend = ?", dto.IsForceend)
	}

	if dto.StaffNo != "" {
		ret = ret.Where("tj_doctorID = ?", dto.StaffNo)
	}
	ret.Debug().Find(&infos)
	return &infos, ret.Error
}

// CreateTestSummary ...
func (TestSummaryModel) CreateTestSummary(info *[]TjTestsummary) (*[]TjTestsummary, error) {
	if info == nil {
		return nil, errors.New("小结内容不能为空")
	}

	for idx := range *info {
		(*info)[idx].ID = 0 //set id to 0,for insert
	}

	logger.Log.Infof("Insert CreateTestSummary: %+v", info)

	ret := db.Table("tj_testsummary").Create(info).Debug()
	if ret.Error != nil {
		return nil, ret.Error
	}

	return info, nil
}

// UpdateTestSummary ...
func (TestSummaryModel) UpdateTestSummary(info *[]TjTestsummary) error {
	if info == nil {
		return errors.New("testsummary 为空")
	}

	logger.Log.Infof("UpdateTestSummary: %+v", info)
	ret := db.Table("tj_testsummary").Debug().Save(info)
	if ret.Error != nil {
		return ret.Error
	}

	return nil
}

// DeleteTestSummary ...
func (TestSummaryModel) DeleteTestSummary(testid string, detpid string) error {
	if testid == "" {
		return errors.New("体检编号为空，不能删除")
	}

	logger.Log.Infof("DeleteTestSummary, testid:%s, deptid:", testid, detpid)

	ret := db.Table("tj_testsummary").Where("tj_testid = ?", testid)
	if detpid != "" {
		ret = ret.Where("tj_deptid = ?", detpid)
	}
	ret.Debug().Delete(&TjTestsummary{})
	if ret.Error != nil {
		return ret.Error
	}

	return nil
}
