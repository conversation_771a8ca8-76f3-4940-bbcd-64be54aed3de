package models

import (
	"errors"
)

// v_tj_lisresult ...
type VTjLisresult struct {
	ID   int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	Tjbh string `gorm:"tjbh:tj_testid;type:varchar(20);not null" json:"tjbh"`
	Brxm string `gorm:"brxm:tj_testid;type:varchar(120);not null" json:"brxm"` //`json:"brxm"`
	Xmxh string `gorm:"xmxh:tj_testid;type:varchar(120);not null" json:"xmxh"` //`json:"xmxh"`
	Xmmc string `gorm:"xmmc:tj_testid;type:varchar(120);not null" json:"xmmc"` //`json:"xmmc"`
	Xmdw string `gorm:"xmdw:tj_testid;type:varchar(120);not null" json:"xmdw"` //`json:"xmdw"`
	Xmjg string `gorm:"xmjg:tj_testid;type:varchar(512);not null" json:"xmjg"` //`json:"xmjg"`
	Sfyc int    `gorm:"column:sfyc;type:int(11);not null" json:"sfyc"`         //`json:"sfyc"`
	Gdbj string `gorm:"gdbj:tj_testid;type:varchar(120);not null" json:"gdbj"` //`json:"gdbj"`
	Ckdz string `gorm:"ckdz:tj_testid;type:varchar(120);not null" json:"ckdz"` //`json:"ckdz"`
	Ckgz string `gorm:"ckgz:tj_testid;type:varchar(120);not null" json:"ckgz"` //`json:"ckgz"`
	Ckfw string `gorm:"ckfw:tj_testid;type:varchar(120);not null" json:"ckfw"` //`json:"ckfw"`
	Jyys string `gorm:"jyys:tj_testid;type:varchar(120);not null" json:"jyys"` //`json:"jyys"`
	Bgrq string `gorm:"bgrq:tj_testid;type:varchar(120);not null" json:"bgrq"` //`json:"bgrq"`
	Bgys string `gorm:"bgys:tj_testid;type:varchar(50);not null" json:"bgys"`  //`json:"bgys"`
}

// TableName get sql table name.获取数据库表名
func (VTjLisresult) TableName() string {
	return "v_tj_lisresult"
}

// TestSummaryModel ...
type VTjLisresultModel struct{}

// CreateTestSummary ...
func (VTjLisresultModel) InsertLisResults(info *[]VTjLisresult) (*[]VTjLisresult, error) {
	if info == nil {
		return nil, errors.New("内容不能为空")
	}

	if len(*info) <= 0 {
		return nil, errors.New("内容不能为空")
	}

	//先删除
	ret := db.Table("v_tj_lisresult").Where("tjbh = ?", (*info)[0].Tjbh)
	ret.Debug().Delete(&VTjLisresult{})
	if ret.Error != nil {
		return nil, ret.Error
	}

	//再插入
	for idx := range *info {
		(*info)[idx].ID = 0 //set id to 0,for insert
	}
	ret = db.Table("v_tj_lisresult").Create(info).Debug()
	if ret.Error != nil {
		return nil, ret.Error
	}

	return info, nil
}
