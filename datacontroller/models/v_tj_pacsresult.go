package models

import "errors"

// v_tj_lisresult ...
type VTjPacsresult struct {
	ID             int    `gorm:"column:id;primary_key;auto_increment;type:int(10);not null" json:"id"` // primary key
	Tjbh           string `gorm:"tjbh:tj_testid;type:varchar(20);not null" json:"tjbh"`
	Brxm           string `gorm:"brxm:tj_testid;type:varchar(120);not null" json:"brxm"`                      //`json:"brxm"`
	Jclx           string `gorm:"jclx:tj_testid;type:varchar(120);not null" json:"jclx"`                      //`json:"jclx"`
	Jcxm           string `gorm:"jcxm:tj_testid;type:varchar(250);not null" json:"jcxm"`                      //`json:"xmmc"`
	Jcmc           string `gorm:"jcmc:tj_testid;type:varchar(250);not null" json:"jcmc"`                      //`json:"xmdw"`
	Imagesight     string `gorm:"imagesight:tj_testid;type:varchar(512);not null" json:"imagesight"`          //`json:"imagesight"`
	Sfyc           int    `gorm:"column:sfyc;type:int(11);not null" json:"sfyc"`                              //`json:"sfyc"`
	Imagediagnosis string `gorm:"imagediagnosis:tj_testid;type:varchar(1024);not null" json:"imagediagnosis"` //`json:"imagediagnosis"`
	Jcys           string `gorm:"jcys:tj_testid;type:varchar(120);not null" json:"jcys"`                      //`json:"jcys"`
	Bgrq           string `gorm:"bgrq:tj_testid;type:varchar(120);not null" json:"bgrq"`                      //`json:"bgrq"`
	Sxys           string `gorm:"sxys:tj_testid;type:varchar(50);not null" json:"sxys"`                       //`json:"sxys"`
	Bgys           string `gorm:"bgys:tj_testid;type:varchar(50);not null" json:"bgys"`                       //`json:"bgys"`
}

// TableName get sql table name.获取数据库表名
func (VTjPacsresult) TableName() string {
	return "v_tj_pacsresult"
}

// TestSummaryModel ...
type VTjPacsresultModel struct{}

// CreateTestSummary ...
func (VTjPacsresultModel) InsertPacsResults(info *[]VTjPacsresult) (*[]VTjPacsresult, error) {
	if info == nil {
		return nil, errors.New("内容不能为空")
	}

	if len(*info) <= 0 {
		return nil, errors.New("内容不能为空")
	}

	//先删除
	ret := db.Table("v_tj_pacsresult").Where("tjbh = ?", (*info)[0].Tjbh)
	ret.Debug().Delete(&VTjLisresult{})
	if ret.Error != nil {
		return nil, ret.Error
	}

	//再插入
	for idx := range *info {
		(*info)[idx].ID = 0 //set id to 0,for insert
	}
	ret = db.Table("v_tj_pacsresult").Create(info).Debug()
	if ret.Error != nil {
		return nil, ret.Error
	}

	return info, nil
}
