package main

import (
	"fmt"
	"log"

	"github.com/unidoc/unipdf/v3/creator"
	"github.com/unidoc/unipdf/v3/model"
)

func testCellPagination() {
	// Create report fonts.
	font, err := model.NewStandard14Font("Helvetica")
	if err != nil {
		log.Fatal(err)
	}

	fontBold, err := model.NewStandard14Font("Helvetica-Bold")
	if err != nil {
		log.Fatal(err)
	}

	c := creator.New()
	c.<PERSON><PERSON><PERSON>(50, 50, 50, 50)

	// Create a chapter for the test
	ch := c.<PERSON>hapter("Cell Content Pagination Test")
	ch.SetMargins(0, 0, 50, 0)
	ch.GetHeading().SetFont(fontBold)
	ch.GetHeading().SetFontSize(18)

	// Create subchapter description
	desc := c.NewStyledParagraph()
	desc.SetMargins(0, 0, 20, 0)
	desc.Append("This test demonstrates how table cells handle content that spans multiple pages. ")
	desc.Append("The cell below contains a very long text that should be properly split across pages ")
	desc.Append("while maintaining proper borders and formatting.")

	ch.Add(desc)

	// Create table with very long content
	table := c.NewTable(2)
	table.SetColumnWidths(0.3, 0.7)
	table.SetMargins(0, 0, 10, 0)

	// Header row
	headerCell1 := table.NewCell()
	headerCell1.SetBorder(creator.CellBorderSideAll, creator.CellBorderStyleSingle, 1)
	headerCell1.SetHorizontalAlignment(creator.CellHorizontalAlignmentCenter)
	headerCell1.SetBackgroundColor(creator.ColorRGBFrom8bit(200, 200, 200))
	headerPara1 := c.NewStyledParagraph()
	headerPara1.Append("Column 1").Style.Font = fontBold
	headerCell1.SetContent(headerPara1)

	headerCell2 := table.NewCell()
	headerCell2.SetBorder(creator.CellBorderSideAll, creator.CellBorderStyleSingle, 1)
	headerCell2.SetHorizontalAlignment(creator.CellHorizontalAlignmentCenter)
	headerCell2.SetBackgroundColor(creator.ColorRGBFrom8bit(200, 200, 200))
	headerPara2 := c.NewStyledParagraph()
	headerPara2.Append("Column 2 - Long Content").Style.Font = fontBold
	headerCell2.SetContent(headerPara2)

	// Create a very long text content that will span multiple pages
	longText := "This is a very long text content that is designed to test the pagination functionality of table cells. "
	for i := 0; i < 50; i++ {
		longText += fmt.Sprintf("This is paragraph %d of the long content. It contains multiple sentences to ensure that the content is substantial enough to span across multiple pages. ", i+1)
		longText += "The text should be properly wrapped and split across pages while maintaining the cell borders and formatting. "
		longText += "Each paragraph adds more content to test the robustness of the pagination system. "
		longText += "The goal is to ensure that when a cell's content is too large to fit on a single page, it is properly split and displayed across multiple pages. "
	}

	// First data row with short content
	cell1 := table.NewCell()
	cell1.SetBorder(creator.CellBorderSideAll, creator.CellBorderStyleSingle, 1)
	para1 := c.NewStyledParagraph()
	chunk1 := para1.Append("Short content")
	chunk1.Style.Font = font
	cell1.SetContent(para1)

	cell2 := table.NewCell()
	cell2.SetBorder(creator.CellBorderSideAll, creator.CellBorderStyleSingle, 1)
	para2 := c.NewStyledParagraph()
	chunk2 := para2.Append("Another short content that fits in one page.")
	chunk2.Style.Font = font
	cell2.SetContent(para2)

	// Second data row with very long content
	cell3 := table.NewCell()
	cell3.SetBorder(creator.CellBorderSideAll, creator.CellBorderStyleSingle, 1)
	para3 := c.NewStyledParagraph()
	chunk3 := para3.Append("Medium content")
	chunk3.Style.Font = font
	cell3.SetContent(para3)

	cell4 := table.NewCell()
	cell4.SetBorder(creator.CellBorderSideAll, creator.CellBorderStyleSingle, 1)
	para4 := c.NewStyledParagraph()
	chunk4 := para4.Append(longText)
	chunk4.Style.Font = font
	cell4.SetContent(para4)

	// Third data row after the long content
	cell5 := table.NewCell()
	cell5.SetBorder(creator.CellBorderSideAll, creator.CellBorderStyleSingle, 1)
	para5 := c.NewStyledParagraph()
	chunk5 := para5.Append("After long content")
	chunk5.Style.Font = font
	cell5.SetContent(para5)

	cell6 := table.NewCell()
	cell6.SetBorder(creator.CellBorderSideAll, creator.CellBorderStyleSingle, 1)
	para6 := c.NewStyledParagraph()
	chunk6 := para6.Append("This content comes after the very long content to verify that the table continues properly after pagination.")
	chunk6.Style.Font = font
	cell6.SetContent(para6)

	ch.Add(table)

	// Draw chapter
	if err := c.Draw(ch); err != nil {
		log.Fatal(err)
	}

	// Write to output file
	if err := c.WriteToFile("cell-pagination-test.pdf"); err != nil {
		log.Fatal(err)
	}

	fmt.Println("Cell pagination test PDF generated successfully: cell-pagination-test.pdf")
}

func main() {
	testCellPagination()
}
