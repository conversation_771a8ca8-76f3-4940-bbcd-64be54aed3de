package app

import (
	"log"
	"os"
	"path/filepath"

	// "github.com/pelletier/go-toml"
	"github.com/BurntSushi/toml"
)

// Config ...
type Config struct {
	Database    Database           `json:"database"`
	Application Application        `json:"application"`
	Cdcupload   Cdcupload          `json:"cdcupload"`
	External    External           `json:"external"`
	Extlis      map[string]Extlis  `json:"extlis"`
	Extpacs     map[string]Extpacs `json:"extpacs"`
}

type External struct {
	Exttype   string `json:"exttype"`
	Serverurl string `json:"serverurl"`
	Appsn     string `json:"appsn"`
}

// Database ...
type Database struct {
	Url      string `json:"url"`
	Username string `json:"username"`
	Password string `json:"password"`
	Dbserver string `json:"dbserver"`
	Port     int64  `json:"port"`
	Dbname   string `json:"dbname"`
	Dbdriver string `json:"dbdriver"`
}

// Application ...
type Application struct {
	Apphost      string `json:"apphost"`
	Appport      int64  `json:"appport"`
	Enableauth   int    `json:"enableauth"`
	Production   int    `json:"production"`
	Logfile      string `json:"logfile"`
	Areaprovince string `json:"areaprovince"`
	Uploader     string `json:"uploader"`
	Pacsversion  string `json:"pacsversion"`
}

// Application ...
type Cdcupload struct {
	Creditcode string `json:"creditcode"`
	AgentId    string `json:"agentId"`
	AppKey     string `json:"appKey"`
	AppSecret  string `json:"appSecret"`
	IsSubmit   int    `json:"isSubmit"`
	Rpturl     string `json:"rpturl"`
	Filerul    string `json:"filerul"`
}

// Extlis ...
type Extlis struct {
	Dbtype      int    `json:"dbtype"`
	Servicename string `json:"servicename"`
	Dbserver    string `json:"dbserver"`
	Dbport      int    `json:"dbport"`
	Username    string `json:"username"`
	Password    string `json:"password"`
}

// Extpacs ...
type Extpacs struct {
	Dbtype      int    `json:"dbtype"`
	Servicename string `json:"servicename"`
	Dbserver    string `json:"dbserver"`
	Dbport      int    `json:"dbport"`
	Username    string `json:"username"`
	Password    string `json:"password"`
}

// Configurations ...
var Configurations = Config{}

func InitDirectory() {
	signpath := filepath.Join(".", DIRECTORY_SIGN)
	os.MkdirAll(signpath, os.ModePerm)
	// updatespath := filepath.Join(".", DIRECTORY_UPDATE)
	// os.MkdirAll(updatespath, os.ModePerm)
	reportspath := filepath.Join(".", DIRECTORY_REPORT)
	os.MkdirAll(reportspath, os.ModePerm)
	// filespath := filepath.Join(".", DIRECTORY_FILE)
	// os.MkdirAll(filespath, os.ModePerm)
}

// InitConfiguration ...
func InitConfiguration() {
	// config := Config{}
	if _, err := toml.DecodeFile("./config/default.toml", &Configurations); err != nil {
		log.Fatal("error loading config file:", err)
		return
	}
	// log.Printf("[info] start to read configuration...")
	// cfg, err := toml.LoadFile("./config/default.toml")
	// inputBytes, err := ioutil.ReadFile("./config/default.toml")
	// // fmt.Println(string(inputBytes))
	// if err != nil {
	// 	log.Fatal("error loading config file:", err)
	// 	// return config, err
	// }

	// toml.Unmarshal(inputBytes, &Configurations)

	// fmt.Printf("Configuration: %+v\n", Configurations)
	// database := cfg.Get("database").(*toml.Tree)
	// application := cfg.Get("application").(*toml.Tree)

	// var db = Database{
	// 	Url:      database.Get("url").(string),
	// 	Username: database.Get("username").(string),
	// 	Password: database.Get("password").(string),
	// 	Dbserver: database.Get("dbserver").(string),
	// 	Port:     database.Get("port").(int64),
	// 	Dbname:   database.Get("dbname").(string),
	// 	Dbdriver: database.Get("dbdriver").(string),
	// }

	// var app = Application{
	// 	Apphost: application.Get("app_host").(string),
	// 	Appport: application.Get("app_port").(int64),
	// }

	// Configurations.AppSettings = app
	// Configurations.DbSettings = db

	// fmt.Println(database)
	// fmt.Println(application)
	// config.application = cfg.Get("application") as Application
	// err = database.Unmarshal(&config.database)
	// if err != nil {
	// 	println(err)
	// }
	// err = application.Unmarshal(&config.application)
	// if err != nil {
	// 	println(err)
	// }
	// println("configuration: %v", config.Get("database"))
	// fmt.Println("===============")
	// fmt.Println(config.application.apphost)
	// fmt.Println(application)
	// return config, nil
}
