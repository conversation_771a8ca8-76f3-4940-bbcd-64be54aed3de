package app

// ENCKEY ...
var ENCKEY = []byte{0x8a, 0x12, 0xde, 0x5f, 0x1a, 0x46, 0x27, 0x38, 0x11, 0x17, 0x0c, 0x14, 0x05, 0x16, 0x17, 0x10, 0x8a, 0x12, 0xde, 0x5f, 0x1a, 0x46, 0x97, 0x38, 0x11, 0x27, 0x0c, 0x14, 0x05, 0x16, 0x17, 0x10}

// IV ...
var IV = []byte{0x59, 0x2d, 0xfa, 0x83, 0x39, 0x0f, 0x93, 0xb7, 0x1f, 0x15, 0xfe, 0x0c, 0x53, 0x32, 0x83, 0x99}

const (
	// CodeSuccess ...
	CodeSuccess = 200 //成功的状态码
	// CodeError ...
	CodeError = 201 //失败的状态码
)

const (
	MessageOk      = "OK"
	MessageLoginOk = "登录成功..."
	AUTHORIZATION  = "Authorization"
	Wzdams         = "DIAN"
)

const (
	DIRECTORY_REPORT = "./reports"
	// DIRECTORY_UPDATE = "./updates"
	// DIRECTORY_FILE   = "./files"
	DIRECTORY_SIGN = "./sign"
)

const (
	ExtOpType_DEL  = -1
	ExtOpType_ADD  = 1
	ExtOpType_EDIT = 2
)

const (
	URL_EXTERNAL_LIS        = "/publicapi/third/laboratory/apply"
	URL_EXTERNAL_PACS       = "/publicapi/third/inspection/apply"
	URL_EXTERNAL_HIS        = "/publicapi/third/order/apply"
	URL_EXTERNAL_HIS_CANCEL = "/publicapi/third/order/cancel"
	URL_DIAN_PEIS           = "/v2/occupy/checkIn"
	URL_DIAN_ORDERS         = "/v2/occupy/order/list"
)

const (
	ERROR_RECORDNOTFOUND = "record not found"
)

const (
	UpdatePoision = "p"
	UpdateCkitems = "c"
)

const (
	Noprogress  = 0 //体检状态0
	Appoint     = 1 //预约 1
	Register    = 2 //登记 2
	Examining   = 3 //正在体检 3
	Examined    = 4 //体检结束 4
	Allchecked  = 5 //已总检 5
	Reported    = 6 //已报告 6
	CDCUploaded = 7 //CDC已上报 7
	Synced      = 8 //已同步 8
	Printed     = 9 //已打印 9
)

const (
	Normal        = 0 //
	Recheck       = 1
	Forbidden     = 2
	Suspected     = 3
	OtherDisease  = 4
	Supplementary = 5
)
