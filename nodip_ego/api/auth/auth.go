package auth

import (
	"datacontroller/models"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"nodip_service/service"
	"strconv"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
)

var signedkey []byte

// LoadSignedKey ...
func LoadSignedKey() {
	var err error
	signedkey, err = ioutil.ReadFile("./config/secret.key")
	if err != nil {
		log.Println("[error] 不能加载./config/secret.key")
	}
}

// UserToken ... 用户的token
type UserToken struct {
	// issued at
	Iat int
	// expiration
	Exp int
	// data
	User         string
	Userid       int
	Isadmin      int
	LoginSession string
}

// CreateToken .. create
func CreateToken(authD models.TjStaffadmin) (string, error) {
	claims := jwt.MapClaims{}
	claims["authorized"] = true
	claims["user"] = authD.TjStaffno
	claims["userid"] = authD.ID
	claims["exp"] = time.Now().Add(time.Hour * 24 * 7).Unix()
	claims["iat"] = time.Now()
	claims["isadmin"] = 1
	claims["login_session"] = authD.LoginSession
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	return token.SignedString(signedkey)
}

// TokenValid .. create
func TokenValid(r *http.Request) error {
	token, err := VerifyToken(r)
	if err != nil {
		return err
	}
	if _, ok := token.Claims.(jwt.Claims); !ok && !token.Valid {
		return err
	}
	return nil
}

// VerifyToken .. create
func VerifyToken(r *http.Request) (*jwt.Token, error) {
	// fmt.Printf("请求信息:%+v\n", r) // Print with Variable Name
	tokenString := ExtractToken(r)
	// println("Token is:", tokenString)
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// fmt.Printf("parsed token:%+v\n", token)
		//Make sure that the token method conform to "SigningMethodHMAC"
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return signedkey, nil
	})
	if err != nil {
		return nil, err
	}
	// log.Println("verify token is done, pass")
	return token, nil
}

//ExtractToken ... get the token from the request body
func ExtractToken(r *http.Request) string {
	keys := r.URL.Query()

	token := keys.Get("Bearer ")
	if token != "" {
		return token
	}
	bearToken := r.Header.Get("Authorization")
	// fmt.Printf("Keys:%+v\n", bearToken)
	//normally Authorization the_token_xxx
	strArr := strings.Split(bearToken, " ")
	if len(strArr) == 2 {
		return strArr[1]
	}
	return ""
}

// ExtractUserToken ...
func ExtractUserToken(r *http.Request) (*UserToken, error) {
	token, err := VerifyToken(r)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(jwt.MapClaims) //the token claims should conform to MapClaims
	// log.Printf("Token Claims: %+v", claims)
	if ok && token.Valid {

		userid, err := strconv.ParseInt(fmt.Sprintf("%.f", claims["userid"]), 10, 64)
		if err != nil {
			return nil, err
		}
		isadmin, err := strconv.ParseInt(fmt.Sprintf("%.f", claims["isadmin"]), 10, 64)
		if err != nil {
			return nil, err
		}

		return &UserToken{
			LoginSession: claims["login_session"].(string),
			Userid:       int(userid),
			User:         claims["user"].(string),
			Isadmin:      int(isadmin),
		}, nil

	}
	return nil, nil
}

// ExtractTokenAuth ...
func ExtractTokenAuth(r *http.Request) (*UserToken, error) {
	token, err := VerifyToken(r)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(jwt.MapClaims) //the token claims should conform to MapClaims
	// log.Printf("Token Claims: %+v\n", claims)
	if ok && token.Valid {
		loginsession, ok := claims["login_session"].(string) //convert the interface to string
		if !ok {
			return nil, err
		}
		userno, ok := claims["user"].(string)
		if !ok {
			return nil, err
		}

		//检验用户得登录session
		ret, err := service.VerifyLogin(userno, loginsession)
		if err != nil {
			return nil, err
		}

		userid, err := strconv.ParseInt(fmt.Sprintf("%.f", claims["userid"]), 10, 64)
		if err != nil {
			return nil, err
		}
		isadmin, err := strconv.ParseInt(fmt.Sprintf("%.f", claims["isadmin"]), 10, 64)
		if err != nil {
			return nil, err
		}
		if ret {
			return &UserToken{
				LoginSession: claims["login_session"].(string),
				Userid:       int(userid),
				User:         claims["user"].(string),
				Isadmin:      int(isadmin),
			}, nil
		}
	}
	return nil, nil
}
