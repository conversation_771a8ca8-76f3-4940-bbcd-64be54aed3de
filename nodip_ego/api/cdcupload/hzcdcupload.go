package cdcupload

import (
	"bytes"
	"datacontroller/models"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"nodip_ego/api/app"
	"nodip_ego/api/docservice"
	"nodip_service/service"
	"os"
	"path/filepath"
	"strconv"
	"utility/logger"
)

type PeopleInfo struct {
	Name          string `json:"name"`
	Gender        string `json:"gender"`
	Age           string `json:"age"`
	WorkType      string `json:"workType"`
	WorkYears     string `json:"workYears"`
	HarmFactors   string `json:"harmFactors"`
	AbnormalIndex string `json:"abnormalIndex"`
	DedicalAdvice string `json:"dedicalAdvice"`
	Opinion       string `json:"opinion"`
	CwithO        string `json:"CwithO"`
	IDCard        string `json:"IDCard"`
}

type Report struct {
	EnterpriseName                string `json:"enterpriseName"`
	EnterpriseContactsPhoneNumber string `json:"enterpriseContactsPhoneNumber"`
	WorkAddress                   string `json:"workAddress"` //地址
	EnterpriseContactsName        string `json:"enterpriseContactsName"`
	ProjectNumber                 string `json:"projectNumber"` //报告编号
	Recheck                       bool   `json:"recheck"`
	CheckDate                     string `json:"checkDate"`
	ShouldCheckNum                int32  `json:"shouldCheckNum"`
	ActuallNum                    int32  `json:"actuallNum"`
	CheckType                     string `json:"checkType"`
	CheckEndDate                  string `json:"checkEndDate"`
	ApprovalDate                  string `json:"approvalDate"`
	CheckPlace                    string `json:"checkPlace"`
}

type UploadData struct {
	CreditCode     string       `json:"creditCode"`
	IsSubmit       bool         `json:"isSubmit"`
	Reportinfo     Report       `json:"report"`
	PeopleinfoList []PeopleInfo `json:"peopleInfoList"`
}

type ResponseData struct {
	Data    interface{} `json:"data"`
	Message string      `json:"message"`
}

var reportModel = new(models.ReportModel)
var corpModel = new(models.CorpinfoModel)

func DoHzCdcUpload(cdc *app.Cdcupload, report_id int64) (string, error) {
	logger.Log.Infof("cdc配置信息:%+v", *cdc)

	//报告信息
	rptinfo, err := reportModel.GetCorpoccureportinfo(report_id, "")
	if err != nil {
		logger.Log.Errorf("查找报告信息失败，错误：%+v", err)
		return "", err
	}

	//体检单位信息
	corpinfo, err := corpModel.QueryCorpinfobyID(rptinfo.TjCorpid, "")
	if err != nil {
		return "", err
	}

	dto := new(models.ReportinfoDTO)
	dto.Reportids = append(dto.Reportids, strconv.FormatInt(report_id, 10))
	testerinfos, err := reportModel.GetCorpoccureportInfo(dto)
	if err != nil {
		return "", err
	}
	var testids []string
	for _, val := range testerinfos {
		testids = append(testids, val.TjTestID)
	}
	if len(testerinfos) <= 0 {
		return "", errors.New("没有体检人员信息")
	}
	var cadto = new(models.CaQueryDTO)
	cadto.RetType = -1
	cadto.CaStatus = -1
	cadto.Testids = testids
	//所有总检信息
	checkallinfos, err := service.QueryCheckall(cadto)
	if err != nil {
		return "", err
	}
	//体检信息
	meddto := models.MedQueryDTO{}
	meddto.Cpme = -1
	meddto.Corpid = -1
	meddto.Testtype = -1
	meddto.Statuslow = -1
	meddto.Statushigh = -1
	meddto.Isrecheck = -1
	meddto.Testid = testids
	medinfos, err := service.QueryMedinfo(meddto)
	if err != nil {
		return "", err
	}
	//体检人员信息
	var ids models.PtQueryDTO
	ids.Testids = testids
	patients, err := service.QueryPatients(ids)
	if err != nil {
		return "", err
	}
	//字典信息
	dicts, err := service.GetDictionaries(0)
	if err != nil {
		return "", err
	}
	var pagestyle int32 = 1
	outdir := "./reports"
	rtpfile, err := docservice.GenerateOccupationalSummaryReport(rptinfo, &medinfos, &checkallinfos, &patients, corpinfo, &dicts, "", pagestyle, outdir)
	if err != nil {
		logger.Log.Errorf("generate report error:%+v", err)
		return "", err
	}
	logger.Log.Infof("Generated report file:%s", rtpfile)

	rpt, err := generateReportInfo(corpinfo, rptinfo)
	if err != nil {
		logger.Log.Errorf("generateReportInfo error:%+v", err)
		return "", err
	}
	projectnumber := rpt.ProjectNumber

	peoples, err := generatePeopleList(&medinfos, &checkallinfos, &patients)
	if err != nil {
		logger.Log.Errorf("generatePeopleList error:%+v", err)
		return "", err
	}
	issubmit := true
	if cdc.IsSubmit == 0 {
		issubmit = false
	}
	updata := UploadData{
		CreditCode:     cdc.Creditcode,
		IsSubmit:       issubmit,
		Reportinfo:     rpt,
		PeopleinfoList: peoples,
	}
	// logger.Log.Infof("Upload data:%+v", updata)
	err = uploadReportInfo(cdc, &updata)
	if err != nil {
		return "", err
	}
	err = uploadReportFile(cdc, projectnumber, rtpfile)
	if err != nil {
		return "", err
	}
	//do upload report
	return "OK", nil
}

func generateReportInfo(corpinfo *models.TjCorpinfo, rptinfo *models.TjCorpoccureport) (Report, error) {
	rpt := Report{}
	if corpinfo.TjPhone == "" {
		return rpt, errors.New("请填写企业联系电话")
	}
	if corpinfo.TjAddress == "" {
		return rpt, errors.New("请填写企业地址")
	}
	if rptinfo.TjReportnum == "" {
		return rpt, errors.New("报告编号有误")
	}
	if rptinfo.TjApeoplenum <= 0 {
		return rpt, errors.New("应检人数有误")
	}
	if rptinfo.TjPeoplenum <= 0 {
		return rpt, errors.New("实检人数有误")
	}
	isrecheck := false
	if rptinfo.TjIsrecheck == 1 {
		isrecheck = true
	}
	checktype := "0"
	if rptinfo.TjTesttype == 3 {
		checktype = "0"
	} else if rptinfo.TjTesttype == 4 {
		checktype = "1"
	} else if rptinfo.TjTesttype == 5 {
		checktype = "2"
	} else if rptinfo.TjTesttype == 6 {
		checktype = "4"
	}

	// projectnumber := fmt.Sprintf("职检字第(%s)号")
	rpt = Report{
		EnterpriseName:                rptinfo.TjCorpname,
		EnterpriseContactsPhoneNumber: corpinfo.TjPhone,
		WorkAddress:                   corpinfo.TjAddress, //地址
		EnterpriseContactsName:        corpinfo.TjContactor,
		ProjectNumber:                 rptinfo.TjReportnum, //报告编号
		Recheck:                       isrecheck,
		CheckDate:                     strconv.Itoa(int(rptinfo.TjStarttime * 1000)),
		ShouldCheckNum:                int32(rptinfo.TjApeoplenum),
		ActuallNum:                    int32(rptinfo.TjPeoplenum),
		CheckType:                     checktype,
		CheckEndDate:                  strconv.Itoa(int(rptinfo.TjEndtime * 1000)),
		ApprovalDate:                  strconv.Itoa(int(rptinfo.TjCreatedate * 1000)),
		CheckPlace:                    "",
	}

	return rpt, nil
}

func generatePeopleList(medinfos *[]models.TjMedexaminfo, checkallnews *[]models.TjCheckallnew, patients *[]models.TjPatient) ([]PeopleInfo, error) {
	var peoples []PeopleInfo

	for _, val := range *medinfos {
		var ptinfo models.TjPatient
		for _, ptval := range *patients {
			if val.TjPid == ptval.TjPid {
				ptinfo = ptval
				break
			}
		}
		gender := "0"
		if ptinfo.TjPsex == 2 {
			gender = "1"
		}
		var checkall models.TjCheckallnew
		for _, pval := range *checkallnews {
			if val.TjTestid == pval.TjTestid {
				checkall = pval
				break
			}
		}

		checkret := "正常"
		if checkall.TjTypeid == 1 {
			checkret = "需复查"
		} else if checkall.TjTypeid == 2 {
			checkret = "职业禁忌证"
		} else if checkall.TjTypeid == 3 {
			checkret = "疑似职业病"
		} else if checkall.TjTypeid == 4 {
			checkret = "其他疾病或异常"
		}
		abnormal := fmt.Sprintf("%s,%s", checkall.TjOcuabnormal, checkall.TjOthabnormal)
		if abnormal == "" {
			abnormal = "无"
		}
		suggestion := fmt.Sprintf("%s,%s", checkall.TjOcusuggestion, checkall.TjOthsuggestion)
		if suggestion == "" {
			suggestion = "无"
		}
		opinion := fmt.Sprintf("%s,%s", checkall.TjOcuopinion, checkall.TjOthopinion)
		if opinion == "" {
			opinion = "无"
		}
		people := PeopleInfo{
			Name:          ptinfo.TjPname,
			Gender:        gender,
			Age:           strconv.Itoa(val.TjAge),
			WorkType:      val.TjWorktype,
			WorkYears:     val.TjWorkage,
			HarmFactors:   val.TjPoisionfactor,
			AbnormalIndex: abnormal,
			DedicalAdvice: suggestion,
			Opinion:       opinion,
			CwithO:        checkret,
			IDCard:        ptinfo.TjPidcard,
		}
		peoples = append(peoples, people)
	}

	return peoples, nil
}

func uploadReportInfo(cdc *app.Cdcupload, report *UploadData) error {
	jsonVal, _ := json.Marshal(report)
	//do upload
	client := &http.Client{}
	req, err := http.NewRequest("POST", cdc.Rpturl, bytes.NewBuffer(jsonVal))
	req.Header.Add("accept", "application/oapi.zyws.v1.0+json")
	req.Header.Add("token", fmt.Sprintf("{\"agentId\":\"%s\",\"appKey\":\"%s\",\"appSecret\":\"%s\"}", cdc.AgentId, cdc.AppKey, cdc.AppSecret))
	req.Header.Add("User-Agent", "apifox/1.0.0 (https://www.apifox.cn)")
	req.Header.Add("Content-Type", "application/json")
	if err != nil {
		logger.Log.Errorf("Error:%+v", err)
		return err
	}
	res, err := client.Do(req)
	if err != nil {
		logger.Log.Errorf("Error:%+v", err)
		return err
	}
	defer res.Body.Close()
	// logger.Log.Infof("Response Code:%d", res.StatusCode)
	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		logger.Log.Errorf("Error:%+v", err)
		return err
	}
	logger.Log.Infof("report data response:%s", string(body))
	var resdata = ResponseData{}
	err = json.Unmarshal(body, &resdata)
	if err != nil {
		return err
	}
	if res.StatusCode != 201 {
		return errors.New(resdata.Message)
	}
	return nil
}

func uploadReportFile(cdc *app.Cdcupload, projnumber, filename string) error {
	method := "POST"

	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("info", fmt.Sprintf("{\"creditCode\":\"%s\",\"projectNumber\":\"%s\"}", cdc.Creditcode, projnumber))
	uploadfile := fmt.Sprintf("./reports/%s", filename)
	file, errFile2 := os.Open(uploadfile)
	if errFile2 != nil {
		return errFile2
	}
	defer file.Close()
	part2, errFile2 := writer.CreateFormFile("file", filepath.Base(uploadfile))
	if errFile2 != nil {
		return errFile2
	}
	_, errFile2 = io.Copy(part2, file)
	if errFile2 != nil {
		return errFile2
	}
	err := writer.Close()
	if err != nil {
		logger.Log.Errorf("error:%+v", err)
		return err
	}
	client := &http.Client{}
	req, err := http.NewRequest(method, cdc.Filerul, payload)

	if err != nil {
		logger.Log.Errorf("error:%+v", err)
		return err
	}
	req.Header.Add("accept", "application/oapi.zyws.v1.0+json")
	req.Header.Add("token", fmt.Sprintf("{\"agentId\":\"%s\",\"appKey\":\"%s\",\"appSecret\":\"%s\"}", cdc.AgentId, cdc.AppKey, cdc.AppSecret))
	req.Header.Add("User-Agent", "apifox/1.0.0 (https://www.apifox.cn)")

	req.Header.Set("Content-Type", writer.FormDataContentType())
	// logger.Log.Infof("Upload File Request:%+v", req)
	res, err := client.Do(req)
	if err != nil {
		logger.Log.Errorf("error:%+v", err)
		return err
	}
	defer res.Body.Close()

	// logger.Log.Infof("Response Code:%d", res.StatusCode)
	body, err := ioutil.ReadAll(res.Body)
	logger.Log.Infof("report file response:%s", string(body))
	if err != nil {
		logger.Log.Errorf("error:%+v", err)
		return err
	}
	var resdata = ResponseData{}
	err = json.Unmarshal(body, &resdata)
	if err != nil {
		return err
	}
	if res.StatusCode != 201 {
		return errors.New(resdata.Message)
	}
	return nil
}
