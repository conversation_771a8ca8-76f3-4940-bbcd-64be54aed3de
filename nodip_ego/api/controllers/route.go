package controllers

import (
	"nodip_ego/api/app"
	v1 "nodip_ego/api/controllers/v1"
	"nodip_ego/api/middlewares"
	"utility/logger"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// InitRouter ...
func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	cors_config := cors.DefaultConfig()
	cors_config.AllowHeaders = []string{"*"}
	cors_config.AllowAllOrigins = true
	cors_config.AllowMethods = []string{"*"}
	cors_config.AllowCredentials = true

	r.Use(cors.New(cors_config))
	// router.GET("/", controller.Index)
	// router.POST("/user", controller.CreateUser)
	// router.POST("/todo", middlewares.TokenAuthMiddleware(), controller.CreateTodo)

	//文件上传与下载
	r.GET("/api/file/:file", v1.DownloadFile)
	r.POST("/api/file/upload", v1.UploadFile)

	//检查并下载更新
	r.GET("/api/update/:file", v1.CheckUpdate)

	//登录
	r.POST("/api/auth/login", v1.Login)
	// r.POST("/api/auth/signup", v1.SignUp)
	// external := apiv1.Group("/external")
	{
		r.GET("/api/external/lis/:code", v1.ExternalGetLisItems)
		r.GET("/api/external/pacs/:code", v1.ExternalGetPacsItems)
		r.POST("/api/external/lis", v1.ExternalUploadLisResult)
		r.POST("/api/external/pacs", v1.ExternalUploadPacsResult)
		r.POST("/api/external/upload", v1.ExternalUpload)
		r.GET("/api/external/order", v1.ExternalGetOrderlist)
	}

	/*三方lis对接使用*/
	r.GET("/api/external/iteminfos/:flag", v1.QueryAllIteminfos)

	apiv1 := r.Group("/api")
	if app.Configurations.Application.Enableauth == 1 {
		logger.Log.Infof("启用token验证......")
		apiv1.Use(middlewares.TokenAuthMiddleware())
	} else {
		logger.Log.Infof("不启用token验证......")
		apiv1.Use(middlewares.TokenMiddleware())
	}

	{
		auth := apiv1.Group("/auth")
		auth.POST("/logout", v1.LogOut)
		auth.POST("/verify", v1.Verify)
		// apiv1.POST("/auth/logout", v1.LogOut)
		// apiv1.POST("/auth/verify", v1.Verify)
		// dict := apiv1.Group("/sys")
		// {

		docservice := apiv1.Group("/docs")
		{
			// docservice.GET("/ocu/:id", v1.GenerateCorpoccureportDocument)
			docservice.POST("/ocuv2", v1.GenerateCorpoccureportDocumentV2)
			docservice.GET("/download/:filename", v1.DownloadCorpoccureportDocument)
		}

		// }
		cdc := apiv1.Group("/cdc")
		{
			cdc.POST("/checkitem", v1.UpdateCdcCheckitems)
		}

		cdcupload := apiv1.Group("/cdcupload")
		{
			cdcupload.POST("/do", v1.CdcUpload)
		}

		extend := apiv1.Group("/extend")
		{
			extend.POST("/lis/result", v1.GetLisResultPost)
			extend.POST("/lis/xmxx", v1.GetLisXmxxPost)
			extend.GET("/lis/result/:tjbh", v1.GetLisResult)
			extend.GET("/lis/xmxx", v1.GetLisXmxx)
			extend.POST("/pacs/result", v1.GetPacsResult)
			extend.GET("/pacs/xmxx", v1.GetPacsResult)
		}

		infoconfig := apiv1.Group("/config")
		{
			infoconfig.GET("/infos", v1.GetInfoConfigs)
			// infoconfig.GET("/infos/:typeid", v1.GetInfoConfigs)
			infoconfig.GET("/info/code/:code", v1.GetInfoConfig)
			infoconfig.GET("/info/name/:iname", v1.GetInfoConfigByName)
			infoconfig.GET("/areas/info/:code", v1.GetAreaInfo)
			infoconfig.GET("/areas/parent/:code", v1.GetAreaInfos)
			infoconfig.GET("/dict/:type", v1.QueryDictionaries)
			infoconfig.GET("/dict/:type/:pid", v1.QueryDictionary)
			infoconfig.PUT("/dict", v1.UpdateDictionary)
			infoconfig.GET("/identity/:idname", v1.QueryIdentity)
			infoconfig.PUT("/identity", v1.UpdateIdentity)
			infoconfig.GET("/sample/:code", v1.QuerySampleTypes)
			infoconfig.GET("/extconfig/:itype", v1.QueryExternalConfigs)
		}

		medinfo := apiv1.Group("/medinfo")
		{
			medinfo.GET("/query/:testid", v1.QueryMedinfoByTestID)
			medinfo.POST("/query", v1.QueryMedinfo)
			medinfo.POST("", v1.InsertMedinfo)
			medinfo.POST("/status/:testid/:status", v1.UpdateMedinfoStatus)
			medinfo.POST("/appoint/:peid/:status/:testdate", v1.AppointMedinfo)
			medinfo.POST("/update/poision", v1.BatchUpdateMedPoision)
			medinfo.PUT("", v1.UpdateMedinfo)
			medinfo.DELETE("", v1.DeleteMedinfo)
			medinfo.PUT("/report", v1.UpdateMedinfoReportStatus)
			medinfo.POST("/register", v1.Register)
		}

		patient := apiv1.Group("/patient")
		{
			patient.POST("/query", v1.QueryPatients)
			patient.POST("", v1.InsertPatient)
			patient.PUT("", v1.UpdatePatient)
			patient.GET("/hazard", v1.QueryPatientHazards)
			patient.DELETE("/hazard", v1.DeletePatientHazards)
			patient.POST("/hazard", v1.InsertPatientHazards).PUT("/hazard", v1.UpdatePatientHazards)
			patient.GET("/disease", v1.QueryPatientDiseases)
			patient.PUT("/disease", v1.UpdatePatientDiseases)
			patient.DELETE("/disease", v1.DeletePatientDiseases)
			patient.POST("/disease", v1.InsertPatientDiseases)
		}
		//检查项目
		checkitem := apiv1.Group("/checkitem")
		{
			checkitem.POST("", v1.InsertCheckiteminfos)
			checkitem.POST("/query", v1.GetCheckiteminfos)
			// checkitem.POST("/batch", v1.InsertCheckiteminfos)
			checkitem.PUT("", v1.UpdateCheckiteminfos)
			checkitem.DELETE("", v1.DeleteCheckiteminfos)
		}

		ts := apiv1.Group("/testsummary")
		{
			ts.GET("", v1.QueryTestSummaries)
			ts.PUT("", v1.UpdateTestSummaries)
			ts.POST("", v1.InsertTestSummaries)
			ts.DELETE("", v1.DeleteTestSummaries)
		}

		ca := apiv1.Group("/checkall")
		{
			ca.GET("", v1.QueryCheckall)
			ca.PUT("", v1.UpdateCheckall)
			ca.POST("", v1.InsertCheckall)
		}

		item := apiv1.Group("/items")
		{
			iteminfo := item.Group("/info")
			{
				iteminfo.GET("", v1.QueryIteminfos)
				iteminfo.PUT("", v1.UpdateIteminfo)
				iteminfo.POST("", v1.InsertIteminfos)
				iteminfo.DELETE("/:id", v1.DeleteIteminfo)
			}
			itemtype := item.Group("/type")
			{
				itemtype.GET("/:id", v1.QueryItemtype)
				itemtype.PUT("", v1.UpdateItemtype)
				itemtype.POST("", v1.InsertItemtype)
				itemtype.DELETE("/:id", v1.DeleteItemtype)
			}
			combine := item.Group("/combine")
			{
				combine.GET("", v1.QueryCombineinfo)
				combine.PUT("", v1.UpdateCombineinfo)
				combine.POST("", v1.InsertCombineinfo)
				combine.DELETE("", v1.DeleteCombineinfo)
			}

			iresult := item.Group("/result")
			{
				iresult.GET("/:itemid", v1.QueryItemresultinfo)
				iresult.PUT("", v1.UpdateItemresultinfo)
				iresult.POST("", v1.InsertItemresultinfo)
				iresult.DELETE("/:id", v1.DeleteItemresultinfo)
			}
		}

		corp := apiv1.Group("/corpinfo")
		{
			corp.GET("", v1.QueryCorpinfo)
			corp.POST("", v1.InsertCorpinfo)
			corp.DELETE("/corp/:id", v1.DeleteCorpinfo)
			corp.PUT("", v1.UpdateCorpinfo)

			corp.GET("/med", v1.QueryCorpMedinfo)
			corp.POST("/med", v1.InsertCorpMedinfo)
			corp.DELETE("/med/:id", v1.DeleteCorpMedinfo)
			corp.PUT("/med", v1.UpdateCorpMedinfo)
		}

		ad := apiv1.Group("/audio")
		{
			ad.GET("/detail", v1.QueryAudiogramDetails)
			ad.POST("/detail", v1.InsertAudiogramDetails)
			ad.PUT("/result", v1.SaveAudiogramResult)
			ad.POST("/result", v1.InsertAudiogramResult)
			ad.GET("/result/:testid", v1.QueryAudiogramResult)
			ad.GET("/revise", v1.QueryAudiogramRevise)

			ad.GET("/summary", v1.QueryAudiogramSummary)
			ad.POST("/summary", v1.InsertAudiogramSummary)
			ad.PUT("/summary", v1.UpdateAudiogramSummary)
			ad.DELETE("/summary/:id", v1.DeleteAudiogramSummary)
		}
		// tj_pacsresult
		pacs := apiv1.Group("/pacs")
		{
			pacs.GET("/:testid", v1.QueryPacsresult)
			pacs.POST("", v1.SavePacsresult)
			pacs.DELETE("/:testid", v1.DeletePacsresult)
		}

		diseases := apiv1.Group("/diseases")
		{
			dises := diseases.Group("/dises")
			{
				dises.GET("", v1.QueryDiseases)
				dises.POST("", v1.InsertDiseases)
				dises.PUT("", v1.UpdateDiseases)
				dises.DELETE("/:id", v1.DeleteDiseases)
			}
			ocd := diseases.Group("/occucondition")
			{
				ocd.GET("", v1.QueryOccucondition)
				ocd.POST("", v1.InsertOccucondition)
				ocd.PUT("", v1.UpdateOccucondition)
				ocd.DELETE("/:id", v1.DeleteOccucondition)
			}
			autodiag := diseases.Group("/autodiag")
			{
				autodiag.GET("", v1.QueryAutodiagcondition)
				autodiag.POST("", v1.InsertAutodiagcondition)
				autodiag.PUT("", v1.UpdateAutodiagcondition)
				autodiag.DELETE("/:id", v1.DeleteAutodiagcondition)
			}
		}

		guide := apiv1.Group("/guide")
		{
			guideinfo := guide.Group("/info")
			{
				guideinfo.GET("/:id", v1.QueryGuideinfo)
				guideinfo.POST("", v1.InsertGuideinfo)
				guideinfo.PUT("", v1.UpdateGuideinfo)
				guideinfo.DELETE("/:id", v1.DeleteGuideinfo)
			}
			guideitem := guide.Group("/item")
			{
				guideitem.GET("/:id", v1.QueryGuideitem)
				guideitem.POST("", v1.InsertGuideitem)
				guideitem.PUT("", v1.UpdateGuideitem)
				guideitem.DELETE("", v1.DeleteGuideitem)
			}
		}

		hazard := apiv1.Group("/hazard")
		{
			hdtype := hazard.Group("/type")
			{
				hdtype.GET("/:id", v1.QueryHazardtype)
				hdtype.PUT("", v1.UpdateHazardtype)
				hdtype.POST("", v1.InsertHazardtype)
				hdtype.DELETE("/:id", v1.DeleteHazardtype)
			}

			hdinfo := hazard.Group("/info")
			{
				hdinfo.GET("", v1.QueryHazardinfo)
				hdinfo.POST("", v1.InsertHazardinfo)
				hdinfo.PUT("", v1.UpdateHazardinfo)
				hdinfo.DELETE("/:id", v1.DeleteHazardinfo)
			}

			hditem := hazard.Group("/item")
			{
				hditem.GET("", v1.QueryHazarditem)
				hditem.POST("", v1.InsertHazarditem)
				hditem.PUT("", v1.UpdateHazarditem)
				hditem.DELETE("/:id", v1.DeleteHazarditem)
			}

			hddis := hazard.Group("/disease")
			{
				hddis.GET("", v1.QueryHazarddisease)
				hddis.POST("", v1.InsertHazarddisease)
				hddis.PUT("", v1.UpdateHazarddisease)
				hddis.DELETE("/:id", v1.DeleteHazarddisease)
			}

			hdfactor := hazard.Group("/factor")
			{
				hdfactor.POST("", v1.UpdateCdcHazardfactor)
			}
		}

		dept := apiv1.Group("/depart")
		{
			dinfo := dept.Group("/info")
			{
				dinfo.GET("/:deptid", v1.QueryDepartinfo)
				dinfo.POST("", v1.InsertDepartinfo)
				dinfo.PUT("", v1.UpdateDepartinfo)
				dinfo.DELETE("/:id", v1.DeleteDepartinfo)
			}
			ditems := dept.Group("/items")
			{
				ditems.GET("", v1.QueryDeptitem)
				ditems.POST("", v1.InsertDeptitem)
				ditems.PUT("", v1.UpdateDeptitem)
				ditems.DELETE("", v1.DeleteDeptitem)
			}
		}

		instrument := apiv1.Group("/instrument")
		{
			instrumentinfo := instrument.Group("/info")
			{
				instrumentinfo.GET("/:struid", v1.QueryInstrumentinfo)
				instrumentinfo.POST("", v1.InsertInstrumentinfo)
				instrumentinfo.PUT("", v1.UpdateInstrumentinfo)
				instrumentinfo.DELETE("/:struid", v1.DeleteInstrumentinfo)
			}

			itemref := instrument.Group("/ref")
			{
				itemref.GET("", v1.QueryItemrefinfo)
				itemref.POST("", v1.InsertItemrefinfo)
				itemref.PUT("", v1.UpdateItemrefinfo)
				itemref.DELETE("", v1.DeleteItemrefinfo)
			}
		}

		packageinfo := apiv1.Group("/package")
		{
			pinfo := packageinfo.Group("/info")
			{
				pinfo.GET("/:id", v1.QueryPackageinfo)
				pinfo.POST("", v1.InsertPackageinfo)
				pinfo.PUT("", v1.UpdatePackageinfo)
				pinfo.DELETE("/:id", v1.DeletePackageinfo)
			}

			pdetail := packageinfo.Group("/detail")
			{
				pdetail.GET("/:id", v1.QueryPackagedetail)
				pdetail.POST("", v1.InsertPackagedetail)
				pdetail.PUT("", v1.UpdatePackagedetail)
				pdetail.DELETE("", v1.DeletePackagedetail)
			}
		}

		bar := apiv1.Group("/bar")
		{
			barname := bar.Group("/info")
			{
				barname.GET("/:id", v1.QueryBarnameinfo)
				barname.POST("", v1.InsertBarnameinfo)
				barname.PUT("", v1.UpdateBarnameinfo)
				barname.DELETE("/:id", v1.DeleteBarnameinfo)
			}

			bardetail := bar.Group("/detail")
			{
				bardetail.GET("", v1.QueryBardetail)
				bardetail.POST("", v1.InsertBardetail)
				bardetail.PUT("", v1.UpdateBardetail)
				bardetail.DELETE("", v1.DeleteBardetail)
			}
			baritem := bar.Group("/items")
			{
				baritem.GET("/:id", v1.QueryBaritems)
				baritem.POST("", v1.InsertBaritems)
				baritem.PUT("", v1.UpdateBaritems)
				baritem.DELETE("/:binum", v1.DeleteBaritems)
			}
		}

		labresult := apiv1.Group("/labresult")
		{
			labresult.GET("", v1.QueryLabresult)
			labresult.POST("", v1.InsertLabresult)
			labresult.DELETE("", v1.DeleteLabresult)
		}

		group := apiv1.Group("/group")
		{
			ginfo := group.Group("/info")
			{
				ginfo.GET("/:id", v1.QueryGroupinfo)
				ginfo.POST("", v1.InsertGroupinfo)
				ginfo.PUT("", v1.UpdateGroupinfo)
				ginfo.DELETE("/:id", v1.DeleteGroupinfo)
			}
			grights := group.Group("/rights")
			{
				grights.GET("/:id", v1.QueryGroupright)
				grights.POST("", v1.InsertGroupright)
				grights.PUT("", v1.UpdateGroupright)
				grights.DELETE("/:id", v1.DeleteGroupright)
			}
		}

		staff := apiv1.Group("/staff")
		{
			staff.GET("/infos/:groupid", v1.QueryStaffadmins)
			sinfo := staff.Group("/info")
			{
				sinfo.GET("/:staffno", v1.QueryStaffadmin)
				sinfo.POST("", v1.InsertStaffadmin)
				sinfo.PUT("", v1.UpdateStaffadmin)
				sinfo.DELETE("/:id", v1.DeleteStaffadmin)
			}

			sright := staff.Group("/rights")
			{
				sright.GET("/:staffid", v1.QueryStaffright)
				sright.POST("", v1.InsertStaffright)
				sright.DELETE("/:staffid", v1.DeleteStaffright)
			}

			sdept := staff.Group("/dept")
			{
				sdept.GET("", v1.QueryStaffdept)
				sdept.POST("", v1.InsertStaffdept)
				sdept.DELETE("/:id", v1.DeleteStaffdept)
			}
		}

		report := apiv1.Group("/report")
		{
			corpoccu := report.Group(("/corp"))
			{
				corpoccu.POST("/query", v1.QueryCorpoccureport)
				corpoccu.POST("", v1.InsertCorpoccureport)
				corpoccu.PUT("", v1.UpdateCorpoccureport)
				corpoccu.DELETE("/:id", v1.DeleteCorpoccureport)
			}
			rptinfo := report.Group(("/info"))
			{
				rptinfo.POST("/query", v1.QueryCorpoccureportInfo)
				rptinfo.POST("", v1.InsertCorpoccureportInfo)
				rptinfo.PUT("", v1.UpdateCorpoccureportInfo)
				rptinfo.DELETE("/:rptid", v1.DeleteCorpoccureportInfo)
			}
			rptfc := report.Group(("/fc"))
			{
				rptfc.POST("/query", v1.QueryCorpoccureportFc)
				rptfc.POST("", v1.InsertCorpoccureportFc)
				rptfc.PUT("", v1.UpdateCorpoccureportFc)
				rptfc.DELETE("/:testid", v1.DeleteCorpoccureportFc)
			}
		}

		questionnair := apiv1.Group("/questionnair")
		{

			questionnair.GET("", v1.QueryQuestionnairInfo)
			questionnair.POST("", v1.InsertQuestionnairInfo)

			ocuhis := questionnair.Group("ocuhis")
			{
				ocuhis.GET("", v1.QueryOccupationHistories)
				ocuhis.POST("", v1.InsertOccupationHistories)
			}
			dishis := questionnair.Group("dishis")
			{
				dishis.GET("", v1.QueryDiseaseHistories)
				dishis.POST("", v1.InsertDiseaseHistories)
			}
			marriage := questionnair.Group("marriage")
			{
				marriage.GET("", v1.QueryMarriageHistories)
				marriage.POST("", v1.InsertMarriageHistories)
			}
		}

		sign := apiv1.Group("/sign")
		{
			sign.GET("/download/:filename", v1.DownloadElectronicSign)
			sign.POST("/download", v1.DownloadSignPhoto)
			sign.POST("/upload", v1.UploadSignPhoto)
		}
	}
	return r
}
