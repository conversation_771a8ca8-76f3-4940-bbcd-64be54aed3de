package v1

import (
	"datacontroller/models"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/app"
	"nodip_ego/api/auth"
	"nodip_service/service"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"
	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

// TokenBodyResponse ... TokenBodyResponse
type TokenBodyResponse struct {
	Token     string              `json:"token"`
	TokenType string              `json:"token_type"`
	User      models.TjStaffadmin `json:"user"`
}

// Login ... Login
func Login(c *gin.Context) {

	// var u model.User
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	// app.SugarLogger.Infof("用户登录:%s", string(body))
	// fmt.Println("request body: ", string(body))

	info, err := service.Login(body)

	if err != nil {
		response.ERROR(c, http.StatusOK, err)
	} else {
		// service.
		ret, err := auth.CreateToken(*info)
		if err != nil {
			response.ERROR(c, http.StatusOK, err)
			return
		}
		// println("user token:", ret)
		// json.Marshal("token": ret, "token_type":"bearer", user: info)
		var tk = TokenBodyResponse{}
		tk.Token = ret
		tk.TokenType = "bearer"
		tk.User = *info
		response.JSONModel(c, "Login successfully", tk)
		// c.JSON(http.StatusOK,)
	}
}

// LogOut ... LogOut
func LogOut(c *gin.Context) {
	au, err := auth.ExtractUserToken(c.Request)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	err = service.LogOut(au.User)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	response.JSONModel(c, "Successfully logged out", "")
}

// Verify ... Verify
func Verify(c *gin.Context) {

	_, err := auth.ExtractTokenAuth(c.Request)
	if err != nil {
		// c.JSON(http.StatusUnauthorized, "You need to be authorized to access this route")
		// err := new error("")
		response.ERROR(c, http.StatusUnauthorized, err)
	} else {
		response.JSONModel(c, app.MessageOk, "")
	}

	// authstr := strings.Split(c.Request.Header["Authorization"][0], " ")[1]
	// if authstr == "" {
	// 	response.ERROR(c, http.StatusOK, errors.New("没有传入验证token"))
	// }
	// fmt.Printf("request: %+v\n", c.Request)
	// fmt.Printf("request:【%s】\n", authstr)
}
