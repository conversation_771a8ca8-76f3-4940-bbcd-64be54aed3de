package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QueryAudiogramDetails ...
func QueryAudiogramDetails(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.QueryAudiogramDetails(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// InsertAudiogramDetails ...
func InsertAudiogramDetails(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.<PERSON><PERSON><PERSON>())
		return
	}

	ret, err := service.InsertAudiogramDetails(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// InsertAudiogramResult ...
func InsertAudiogramResult(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.InsertAudiogramResult(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// SaveAudiogramResult ...
func SaveAudiogramResult(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.SaveAudiogramResult(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// QueryAudiogramResult ...
func QueryAudiogramResult(c *gin.Context) {
	testid := c.Param("testid")

	if testid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有体检编号，不能查找"))
	}

	ret, err := service.QueryAudiogramResult(testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// *************************QueryAudiogramRevises ******************************

// QueryAudiogramRevise ...
func QueryAudiogramRevise(c *gin.Context) {

	ret, err := service.QueryAudiogramRevises()
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// *************************QueryAudiogramRevises ******************************

// QueryAudiogramSummary ...
func QueryAudiogramSummary(c *gin.Context) {
	// body, err := ioutil.ReadAll(c.Request.Body)
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, err.Error())
	// 	return
	// }

	ret, err := service.QueryAudiogramSummary()
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// InsertAudiogramSummary ...
func InsertAudiogramSummary(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.InsertAudiogramSummary(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdateAudiogramSummary ...
func UpdateAudiogramSummary(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	_, err = service.UpdateAudiogramSummary(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// DeleteAudiogramSummary ...
func DeleteAudiogramSummary(c *gin.Context) {
	sid := c.Param("id")
	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("ID不能为空"))
		return
	}

	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, errors.New("信息类型错误（非数字)"))
		return
	}

	err = service.DeleteAudiogramSummary(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}
