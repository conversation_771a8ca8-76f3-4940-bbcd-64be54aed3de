package v1

import (
	"datacontroller/models"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/app"
	"nodip_service/service"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"
	// "nodip_ego/api/service"
	"utility/logger"

	"github.com/gin-gonic/gin"
)

//UpdateCdcCheckitems ... Verify
func UpdateCdcCheckitems(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	//
	var info = models.ExtCodeDTO{}
	err = json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	if app.Configurations.Application.Uploader == "zj" {
		err = service.UpdateZjCdcCheckitemsService(&info)
	} else if app.Configurations.Application.Uploader == "gj" {
		err = service.UpdateGjcdcCheckitemService(&info)
	} else {
		err = service.UpdateZjCdcCheckitemsService(&info)
	}
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	// response.JSON(c, len(*ret), ret)
	response.JSONModel(c, "OK", "")
}

//InsertHazarddisease ...
func UpdateCdcHazardfactor(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	var info = models.ExtCodeDTO{}
	err = json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	if app.Configurations.Application.Uploader == "zj" {
		err = service.UpdateZjCdcHazardfactorService(&info)
	} else if app.Configurations.Application.Uploader == "gj" {
		err = service.UpdateGjCdcHazardfactorService(&info)
	} else {
		err = service.UpdateZjCdcHazardfactorService(&info)
	}
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}
