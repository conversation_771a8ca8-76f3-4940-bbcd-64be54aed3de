package v1

import (
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/app"
	"nodip_ego/api/cdcupload"
	"nodip_ego/api/response"
	"utility/logger"

	"github.com/gin-gonic/gin"
)

type ReportUploadDto struct {
	Reportid int `json:"rptid"`
}

//CdcUpload ... Verify
func CdcUpload(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var dat ReportUploadDto
	err = json.Unmarshal(body, &dat)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	logger.Log.Infof("要上报的报告:%d", dat.Reportid)
	if dat.Reportid <= 0 {
		response.ERROR(c, http.StatusOK, errors.New("报告编号错误"))
		return
	}

	res, err := cdcupload.DoHzCdcUpload(&app.Configurations.Cdcupload, int64(dat.Reportid))
	if err != nil {
		// logger.Log.Errorf("Error:%+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}
	logger.Log.Infof("Response:%s", res)
	response.JSONModel(c, "OK", "")
}
