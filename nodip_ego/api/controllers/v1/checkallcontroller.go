package v1

import (
	"datacontroller/models"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"nodip_service/service"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"
	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

// QueryCheckall ...
func QueryCheckall(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	// log.Printf("QueryCheckall body data: %s\n", string(body))
	var info = models.CaQueryDTO{}
	err = json.Unmarshal(body, &info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	ret, err := service.QueryCheckall(&info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

// InsertCheckall ...
func InsertCheckall(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	// log.Printf("InsertCheckall body data: %s\n", string(body))
	ret, err := service.InsertCheckall(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdateCheckall ...
func UpdateCheckall(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	// log.Printf("InsertCheckall body data: %s\n", string(body))
	ret, err := service.UpdateCheckall(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}
