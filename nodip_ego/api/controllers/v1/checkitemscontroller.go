package v1

import (
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

// InsertCheckiteminfo ... 添加新的体检信息
// func InsertCheckiteminfo(c *gin.Context) {
// 	body, err := ioutil.ReadAll(c.Request.Body)
// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, err.Error())
// 		return
// 	}
// 	log.Printf("添加新的体检信息(json):%s\n", string(body))
// 	logger.Log.Infof("添加新的体检项目信息(json):%s", string(body))
// 	ret, err := service.InsertCheckitemInfo(body)
// 	if err != nil {
// 		response.ERROR(c, http.StatusOK, err)
// 		return
// 	}

// 	response.JSON(c, 1, ret)
// }

// InsertCheckiteminfos ... 添加新的体检信息
func InsertCheckiteminfos(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	// log.Printf("添加新的体检信息(json):%s\n", string(body))
	// logger.Log.Infof("添加新的体检项目信息(json):%s", string(body))
	ret, err := service.InsertCheckitemInfos(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdateCheckiteminfos ... 更新
func UpdateCheckiteminfos(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	// log.Printf("添加新的体检信息(json):%s\n", string(body))
	// logger.Log.Infof("更新的体检项目信息(json):%s", string(body))
	ret, err := service.UpdateCheckitemInfos(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// GetCheckiteminfos ... query
func GetCheckiteminfos(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	// log.Printf("添加新的体检信息(json):%s\n", string(body))
	// logger.Log.Infof("查找体检项目信息(json):%s", string(body))
	ret, err := service.SearchCheckiteminfos(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

// DeleteCheckiteminfos ... delete
func DeleteCheckiteminfos(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	// log.Printf("添加新的体检信息(json):%s\n", string(body))
	// logger.Log.Infof("删除体检项目信息(json):%s", string(body))
	err = service.RemoveCheckiteminfos(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "ret")
}
