package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ******************* Departinfo BEGIN ******************************

// QueryDepartinfo ...
func QueryDepartinfo(c *gin.Context) {

	sid := c.Param("deptid")

	ret, err := service.QueryDepartinfo(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertDepartinfo ...
func InsertDepartinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.J<PERSON>N(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertDepartinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateDepartinfo ...
func UpdateDepartinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateDepartinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteDepartinfo ...
func DeleteDepartinfo(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}

	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteDepartinfo(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Departinfo END ******************************

// ******************* Deptitem BEGIN ******************************

// QueryDeptitem ...
func QueryDeptitem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.QueryDeptitem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertDeptitem ...
func InsertDeptitem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertDeptitem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateDeptitem ...
func UpdateDeptitem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateDeptitem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteDeptitem ...
func DeleteDeptitem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.DeleteDeptitem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Deptitem END ******************************
