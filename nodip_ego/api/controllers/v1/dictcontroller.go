package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QueryDictionary ...
func QueryDictionary(c *gin.Context) {
	stype := c.Param("type")
	spid := c.Param("pid")

	// log.Println("get type: " + stype)
	if stype == "" {
		response.ERROR(c, http.StatusOK, errors.New("typeid不能为空"))
		return
	}
	itype, err := strconv.Atoi(stype)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	if spid == "" {
		response.ERROR(c, http.StatusOK, errors.New("pid不能为空"))
		return
	}
	ipid, err := strconv.Atoi(spid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	ret, err := service.GetDictionary(itype, ipid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
	// c.JSON(http.StatusOK, "GetInfoConfigs:"+stype)
}

// QueryDictionaries ... Verify
func QueryDictionaries(c *gin.Context) {
	stype := c.Param("type")

	// log.Println("get type: " + stype)
	if stype == "" {
		stype = "0"
	}
	itype, err := strconv.Atoi(stype)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	ret, err := service.GetDictionaries(itype)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
	// c.JSON(http.StatusOK, "GetInfoConfigs:"+stype)
}

// UpdateDictionary ... Verify
func UpdateDictionary(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	err = service.UpdateDictionary(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
	}

	response.JSONModel(c, "OK", "")
	// c.JSON(http.StatusOK, "GetInfoConfigs:"+stype)
}

// QueryIdentity ... Verify
func QueryIdentity(c *gin.Context) {
	stype := c.Param("idname")

	ret, err := service.GetIdentity(stype)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
	}

	response.JSON(c, 1, ret)
	// c.JSON(http.StatusOK, "GetInfoConfigs:"+stype)
}

// UpdateIdentity ... Verify
func UpdateIdentity(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	err = service.UpdateIdentity(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
	}

	response.JSONModel(c, "OK", "")
	// c.JSON(http.StatusOK, "GetInfoConfigs:"+stype)
}

// QuerySampleTypes ... Verify
func QuerySampleTypes(c *gin.Context) {
	scode := c.Param("code")

	ret, err := service.GetSampleTypes(scode)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

// QueryExternalConfigs ... Verify
func QueryExternalConfigs(c *gin.Context) {
	var itype int
	stype := c.Param("itype")
	if stype == "" {
		itype = 0
	} else {
		irt, err := strconv.Atoi(stype)
		if err != nil {
			itype = 0
		} else {
			itype = irt
		}
	}

	ret, err := service.GetExternalConfigs(itype)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}
