package v1

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"io/ioutil"
	"log"
	"net/http"
	"nodip_ego/api/extend"
	"utility/logger"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"

	"github.com/gin-gonic/gin"
)

//GetLisResult ... Verify
func GetLisResult(c *gin.Context) {
	//do nothing
	tjbh := c.Param("tjbh")
	// c.JSON(http.StatusOK, "Successfully get lis result out:"+tjbh)
	// log.Printf("开始获取体检编号为：[%s] 的结果信息", tjbh)
	if tjbh == "" {
		response.ERROR(c, http.StatusOK, errors.New("体检编号不能为空"))
		return
	}
	logger.Log.Infof("开始从默认lis获取体检编号为：[%s] 的结果信息", tjbh)
	ret, err := extend.Requestlisresults(tjbh, "default")
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

func GetLisResultPost(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	var info models.ExtDTO
	// var reterr error
	err = json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("获取数据时出错：%+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// tjbh := c.Param("tjbh")
	// // c.JSON(http.StatusOK, "Successfully get lis result out:"+tjbh)
	// log.Printf("开始获取体检编号为：[%s] 的结果信息", tjbh)
	// if tjbh == "" {
	// 	response.ERROR(c, http.StatusOK, errors.New("体检编号不能为空"))
	// 	return
	// }
	log.Printf("开始从【%s】获取体检编号为：[%s] 的结果信息", info.Extkey, info.Testid)
	ret, err := extend.Requestlisresults(info.Testid, info.Extkey)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//GetPacsResult ... Verify
func GetPacsResult(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	var info models.ExtDTO
	// var reterr error
	err = json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("获取数据时出错：%+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// tjbh := c.Param("tjbh")
	// // c.JSON(http.StatusOK, "Successfully get lis result out:"+tjbh)
	// log.Printf("开始获取体检编号为：[%s] 的结果信息", tjbh)
	// if tjbh == "" {
	// 	response.ERROR(c, http.StatusOK, errors.New("体检编号不能为空"))
	// 	return
	// }
	logger.Log.Infof("开始从【%s】获取体检编号为：[%s] 的结果信息", info.Extkey, info.Testid)
	ret, err := extend.RequestPacsresults(info.Testid, info.Extkey)
	if err != nil {
		// fmt.Printf("获取影像结果：%s\n", err.Error())
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

type extconfigtype struct {
	Extkey string `json:"extkey"`
}

//GetLisXmxxPost ... Verify
func GetLisXmxxPost(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	var info extconfigtype
	// var reterr error
	err = json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("获取数据时出错：%+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// liskey := c.Param("lis")
	// c.JSON(http.StatusOK, "Successfully get lis result out:"+tjbh)
	// if liskey == "" {
	// 	liskey = "default"
	// }
	logger.Log.Infof("开始获取lis key为：[%s] 的项目信息", info.Extkey)
	ret, err := extend.RequestLisXmxx(info.Extkey)
	if err != nil {
		// fmt.Printf("获取项目信息错误：%s\n", err.Error())
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//GetLisXmxx ... Verify
func GetLisXmxx(c *gin.Context) {
	// liskey := c.Param("lis")
	// c.JSON(http.StatusOK, "Successfully get lis result out:"+tjbh)
	// if liskey == "" {
	liskey := "default"
	// }
	logger.Log.Infof("开始获取lis key为：[%s] 的项目信息", liskey)
	ret, err := extend.RequestLisXmxx(liskey)
	if err != nil {
		// fmt.Printf("获取项目信息错误：%s\n", err.Error())
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//GetPacsXmxx ... Verify
func GetPacsXmxx(c *gin.Context) {
	liskey := "default"
	ret, err := extend.RequestPacsXmxx(liskey)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}
