package v1

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/app"
	"nodip_ego/api/external"
	"nodip_service/service"
	"utility/logger"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"
	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

// ******************* External BEGIN ******************************

// GetLisItems ...GetLisItems
func ExternalGetLisItems(c *gin.Context) {
	sid := c.Param("code")
	fmt.Printf("开始获取外部对接数据......")
	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有条码号"))
		logger.Log.Errorf("没有条码号，不能获取检验信息")
		return
	}

	ret, err := service.QueryExternalLisItems(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// ExternalUpload ...GetPacsItems
func ExternalUpload(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	//完成本地数据对接
	var extdto models.ExtTypeDTO
	err = json.Unmarshal(body, &extdto)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	if extdto.Testid == "" {
		// response.JSONModel(c, "OK", "")
		response.JSON(c, 1, "")
		return
	}
	// var localservice = new(external.LocalService)
	// err = localservice.UploadData(&extdto)
	// if err != nil {
	// 	response.ERROR(c, http.StatusOK, err)
	// 	return
	// }

	//再根据配置信息，进行外部对接
	if app.Configurations.External.Exttype == "jhts" {
		logger.Log.Infof("开始进行[jhts]得系统对接:%+v", extdto)
		// logger.Log.Infof("Body:%+v", body)
		var extservice = new(external.JHTSService)

		err = extservice.UploadData(&extdto)
		if err != nil {
			// response.ERROR(c, http.StatusOK, err)
			response.JSONError(c, err, "")
			return
		} else {
			response.JSON(c, 1, "")
			return
		}
	} else if app.Configurations.External.Exttype == "dian" {
		logger.Log.Infof("开始进行【dams】得系统对接:%+v", extdto)
		var extservice = new(external.DIANService)
		err = extservice.UploadData(&extdto)
		if err != nil {
			// response.ERROR(c, http.StatusOK, err)
			response.JSONError(c, err, "")
			return
		} else {
			response.JSON(c, 1, "")
			return
		}
	}
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// response.JSONModel(c, "OK", "")
	response.JSON(c, 1, "")
}

// GetPacsItems ...GetPacsItems
func ExternalGetOrderlist(c *gin.Context) {

	if app.Configurations.External.Exttype == "jhts" {
		logger.Log.Infof("[jhts]不需要获取订单信息")
		response.ERROR(c, http.StatusOK, errors.New("[jhts]不需要获取订单信息"))
		return
	} else if app.Configurations.External.Exttype == "dian" {
		logger.Log.Infof("开始进行【dams】得系统对接")
		var extservice = new(external.DIANService)
		ret, err := extservice.GetOrderList()
		if err != nil {
			response.ERROR(c, http.StatusOK, err)
		}
		response.JSON(c, len(ret), ret)
		return
	}

	response.ERROR(c, http.StatusOK, errors.New("无效得对接配置"))
}

// GetPacsItems ...GetPacsItems
func ExternalGetPacsItems(c *gin.Context) {
	sid := c.Param("code")

	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}

	ret, err := service.QueryExternalpacsItems(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// UploadLisResult ...UploadLisResult
func ExternalUploadLisResult(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	err = service.UploadExternalLisItems(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// UploadPacsResult ...UploadPacsResult
func ExternalUploadPacsResult(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	err = service.UploadExternalPacsItems(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}
