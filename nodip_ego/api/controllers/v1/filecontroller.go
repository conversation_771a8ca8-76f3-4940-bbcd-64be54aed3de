package v1

import (
	"errors"
	"io"
	"net/http"
	"os"
	"path"
	"utility/logger"

	"nodip_ego/api/response"

	"github.com/gin-gonic/gin"
)

// const maxUploadSize = 500 * 1024 * 1024 // 500 mb

// UploadFile ...
func CheckUpdate(c *gin.Context) {
	filePath := c.Param("file")
	downloadpath := "updates/"

	DoDownload(c, downloadpath, filePath)
}

// UploadFile ...
func UploadFile(c *gin.Context) {

	uploadPath := "files/"

	DoUpload(c, uploadPath)

}

// DownloadFile ...
func DownloadFile(c *gin.Context) {
	filePath := c.Param("file")
	downloadpath := "files/"

	DoDownload(c, downloadpath, filePath)
}

func DoUpload(c *gin.Context, uploadpath string) {
	file, err := c.FormFile("file")

	if err != nil {
		response.ERROR(c, http.StatusOK, errors.New("no file need to be upload"))
		return
	}

	if err := c.SaveUploadedFile(file, uploadpath+file.Filename); err != nil {
		response.ERROR(c, http.StatusOK, errors.New("unable to save file"))
		return
	}

	response.JSONModel(c, "OK", "")
}

func DoDownload(c *gin.Context, downloadpath string, filePath string) {
	file, err := os.Open(path.Join(downloadpath, filePath)) //Create a file
	if err != nil {
		logger.Log.Errorf("Error:%v", err)
		response.ERROR(c, http.StatusOK, errors.New("load file error: "+err.Error()))
		return
	}
	defer file.Close()
	c.Writer.Header().Add("Content-type", "application/octet-stream")
	size, err := io.Copy(c.Writer, file)
	logger.Log.Infof("copied buffer size:%d", size)
	if err != nil {
		logger.Log.Errorf("Error:%v", err)
		response.ERROR(c, http.StatusOK, errors.New("文件加载失败:"+err.Error()))
		return
	}
}
