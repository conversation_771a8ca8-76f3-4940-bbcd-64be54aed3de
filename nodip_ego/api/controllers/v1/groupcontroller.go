package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ******************* Groupinfo BEGIN ******************************

// QueryGroupinfo ...
func QueryGroupinfo(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}
	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}
	ret, err := service.QueryGroupinfo(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertGroupinfo ...
func InsertGroupinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertGroupinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateGroupinfo ...
func UpdateGroupinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateGroupinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteGroupinfo ...
func DeleteGroupinfo(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}
	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteGroupinfo(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Groupinfo END ******************************
// ******************* Groupright BEGIN ******************************

// QueryGroupright ...
func QueryGroupright(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}

	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}
	ret, err := service.QueryGroupright(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertGroupright ...
func InsertGroupright(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertGroupright(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateGroupright ...
func UpdateGroupright(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateGroupright(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteGroupright ...
func DeleteGroupright(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
		return
	}
	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteGroupright(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Groupright END ******************************
