package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QueryGuideinfo ...
func QueryGuideinfo(c *gin.Context) {
	sid := c.Param("id")
	var id int
	var err error
	if sid == "" {
		id = 0
	} else {
		id, err = strconv.Atoi(sid)
		if err != nil {
			response.ERROR(c, http.StatusOK, errors.New("id 需要是数字"))
			return
		}
	}
	ret, err := service.QueryGuideinfo(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertGuideinfo ...
func InsertGuideinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertGuideinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateGuideinfo ...
func UpdateGuideinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateGuideinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteGuideinfo ...
func DeleteGuideinfo(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能查找"))
	}

	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteGuideinfo(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ********************* guide item *************************

//QueryGuideitem ...
func QueryGuideitem(c *gin.Context) {
	sid := c.Param("id")
	var id int
	var err error
	if sid == "" || sid == "0" {
		id = 0
	} else {
		id, err = strconv.Atoi(sid)
		if err != nil {
			response.ERROR(c, http.StatusOK, errors.New("id 需要是数字"))
			return
		}
	}
	ret, err := service.QueryGuideitem(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	response.JSON(c, len(*ret), ret)

}

//InsertGuideitem ...
func InsertGuideitem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.InsertGuideitem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateGuideitem ...
func UpdateGuideitem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateGuideitem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteGuideitem ...
func DeleteGuideitem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteGuideitem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}
