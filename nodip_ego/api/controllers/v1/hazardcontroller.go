package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"
	"strconv"
	"utility/logger"

	"github.com/gin-gonic/gin"
)

// ******************* hazardinfo BEGIN ******************************

// QueryHazardinfo ...
func QueryHazardinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.QueryHazardinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertHazardinfo ...
func InsertHazardinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertHazardinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateHazardinfo ...
func UpdateHazardinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateHazardinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteHazardinfo ...
func DeleteHazardinfo(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}

	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteHazardinfo(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* hazardinfo END ******************************

// ******************* hazarditem BEGIN ******************************

// QueryHazarditem ...
func QueryHazarditem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.QueryHazarditem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertHazarditem ...
func InsertHazarditem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertHazarditem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateHazarditem ...
func UpdateHazarditem(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateHazarditem(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteHazarditem ...
func DeleteHazarditem(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}

	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteHazarditem(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* hazarditem END ******************************

// ******************* hazardtype BEGIN ******************************

// QueryHazardtype ...
func QueryHazardtype(c *gin.Context) {

	sid := c.Param("id")

	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID"))
		return
	}

	id, err := strconv.Atoi(sid)
	ret, err := service.QueryHazardtype(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertHazardtype ...
func InsertHazardtype(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertHazardtype(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateHazardtype ...
func UpdateHazardtype(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateHazardtype(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteHazardtype ...
func DeleteHazardtype(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}

	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteHazardtype(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* hazardtype END ******************************

// ******************* hazardtype BEGIN ******************************

// QueryHazarddisease ...
func QueryHazarddisease(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.QueryHazarddisease(body)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertHazarddisease ...
func InsertHazarddisease(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertHazarddisease(body)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateHazarddisease ...
func UpdateHazarddisease(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateHazarddisease(body)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteHazarddisease ...
func DeleteHazarddisease(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}

	id, err := strconv.Atoi(sid)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteHazarddisease(id)
	if err != nil {
		logger.Log.Errorf("错误:+v", err)
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* hazarddisease END ******************************
