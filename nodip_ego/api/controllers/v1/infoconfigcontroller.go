package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/app"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

//GetInfoConfigs ... Verify
func GetInfoConfigs(c *gin.Context) {
	// stype := c.Param("typeid")

	// // fmt.Println("get type: " + stype)
	// if stype == "" {
	// 	stype = "0"
	// }
	// itype, err := strconv.Atoi(stype)
	// if err != nil {
	// 	response.ERROR(c, http.StatusOK, errors.New("信息类型错误（非数字)"))
	// 	return
	// }
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.GetInfoconfigs(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
	// c.<PERSON>(http.StatusOK, "GetInfoConfigs:"+stype)
}

//GetInfoConfig ... Verify
func GetInfoConfig(c *gin.Context) {
	scode := c.Param("code")

	// fmt.Println("get type: " + stype)
	if scode == "" {
		response.ERROR(c, http.StatusOK, errors.New("info code不能为空"))
		return
	}

	ret, err := service.GetInfoconfig(scode)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
	// c.JSON(http.StatusOK, "GetInfoConfigs:"+stype)
}

//GetInfoConfigByName ... Verify
func GetInfoConfigByName(c *gin.Context) {
	scode := c.Param("iname")

	// fmt.Println("get type: " + stype)
	if scode == "" {
		response.ERROR(c, http.StatusOK, errors.New("info code不能为空"))
		return
	}

	ret, err := service.GetInfoConfigByName(scode)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
	// c.JSON(http.StatusOK, "GetInfoConfigs:"+stype)
}

//GetAreaInfo ... GetAreaInfo
func GetAreaInfo(c *gin.Context) {
	scode := c.Param("code")

	// fmt.Printf("开始根据代码获取地区信息，代码: %s\n", scode)
	if scode == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有地区代码)"))
		return
	}

	ret, err := service.GetAreainfo(scode, app.Configurations.Application.Areaprovince)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//GetAreaInfos ... GetAreaInfos
func GetAreaInfos(c *gin.Context) {

	scode := c.Param("code")
	// fmt.Printf("开始根据父代码获取地区信息，代码: %s\n", scode)

	// fmt.Println("get type: " + stype)
	if scode == "" || scode == "0" {
		scode = "00"
	}
	// itype, err := strconv.Atoi(stype)
	// if err != nil {
	// 	response.ERROR(c, http.StatusOK, errors.New("信息类型错误（非数字)"))
	// }

	ret, err := service.GetAreainfos(scode)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}
