package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

// ******************* Instrumentinfo BEGIN ******************************

// QueryInstrumentinfo ...
func QueryInstrumentinfo(c *gin.Context) {
	struid := c.Param("struid")

	// if itemid == "" {
	// 	response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
	// }

	ret, err := service.QueryInstrumentinfo(struid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertInstrumentinfo ...
func InsertInstrumentinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertInstrumentinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateInstrumentinfo ...
func UpdateInstrumentinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateInstrumentinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteInstrumentinfo ...
func DeleteInstrumentinfo(c *gin.Context) {
	sid := c.Param("struid")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
		return
	}

	err := service.DeleteInstrumentinfo(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Instrumentinfo END ******************************

// ******************* Itemrefinfo BEGIN ******************************

// QueryItemrefinfo ...
func QueryItemrefinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.QueryItemrefinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertItemrefinfo ...
func InsertItemrefinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertItemrefinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateItemrefinfo ...
func UpdateItemrefinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateItemrefinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteItemrefinfo ...
func DeleteItemrefinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	err = service.DeleteItemrefinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Itemrefinfo END ******************************
