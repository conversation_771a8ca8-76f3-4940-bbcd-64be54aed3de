package v1

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_service/service"
	"utility/logger"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"
	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

//RegisterMedexaminfo ... 体检信息登记
func Register(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// log.Printf("开始登记体检信息：%s\n", string(body))
	// logger.Log.Debugf("开始登记体检信息：%s", string(body))
	err = service.RegisterMedexaminfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "ret")
}

//QueryMedinfoByTestID ... 支持按照多个testid查找，不同的testid，用“逗号（,）"隔开
func QueryMedinfoByTestID(c *gin.Context) {
	stestid := c.Param("testid")

	if stestid == "" {
		response.ERROR(c, http.StatusOK, errors.New("体检编号不能为空"))
		return
	}
	ret, err := service.QueryMedinfoByTestID(stestid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

//QueryMedinfo ... GetAreaInfo
func QueryMedinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	logger.Log.Debugf("查找条件：%s", string(body))

	var dat = models.MedQueryDTO{}
	err = json.Unmarshal(body, &dat)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	ret, err := service.QueryMedinfo(dat)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

// InsertMedinfo ... 添加新的体检信息
func InsertMedinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	// log.Printf("添加新的体检信息(json):%s\n", string(body))
	// logger.Log.Infof("添加新的体检信息(json):%s", string(body))
	ret, err := service.InsertMedinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// BatchUpdateMedPoision ... 添加新的体检信息
func BatchUpdateMedPoision(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	// log.Printf("更新体检者的毒害因素:%s\n", string(body))

	// err := json.Unmarshal(body, &dat)

	// logger.Log.Infof("更新体检信息:%s", string(body))
	ret, err := service.BatchUpdateMedPoisioninfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdateMedinfo ... 添加新的体检信息
func UpdateMedinfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	// log.Printf("更新体检信息(json):%s\n", string(body))
	// logger.Log.Infof("更新体检信息:%s", string(body))
	ret, err := service.UpdateMedinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdateMedinfoStatus ... 添加新的体检信息
func UpdateMedinfoStatus(c *gin.Context) {
	stestid := c.Param("testid")
	statusid := c.Param("status")
	// log.Printf("更新体检状态信息%s 新状态:%s\n", stestid, statusid)
	err := service.UpdateMedinfoStatus(stestid, statusid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	ret, err := service.QueryMedinfoByTestID(stestid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	response.JSON(c, len(ret), ret)
}

// AppointMedinfo ... 体检信息预约
func AppointMedinfo(c *gin.Context) {
	peid := c.Param("peid")
	testdate := c.Param("testdate")
	status := c.Param("status")

	// log.Printf("预约体检信息%s 新状态:%s\n", peid, testdate)
	err := service.UpdateMedinfoAppointment(peid, status, testdate)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// ret, err := service.GetMedinfoByTestID(stestid)
	// if err != nil {
	// 	response.ERROR(c, http.StatusOK, err)
	// 	return
	// }
	response.JSONModel(c, "", "")
}

// UpdateMedinfoReportStatus ... 体检信息report status
func UpdateMedinfoReportStatus(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateMedinfoReportStatus(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// ret, err := service.GetMedinfoByTestID(stestid)
	// if err != nil {
	// 	response.ERROR(c, http.StatusOK, err)
	// 	return
	// }
	response.JSONModel(c, "", "")
}

// DeleteMedinfo ... 添加新的体检信息
func DeleteMedinfo(c *gin.Context) {

	// testids := c.Param("testid")
	// if testids == "" {
	// 	response.ERROR(c, http.StatusOK, errors.New("体检编号为空"))
	// 	return
	// }
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	// log.Printf("用户 %s 删除体检信息，体检编号:%s", auth.Ctoken.User, testids)
	// logger.Log.Infof("用户 %s 删除体检信息，体检编号:%s", auth.Ctoken.User, testids)
	err = service.RemoveMedinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}
