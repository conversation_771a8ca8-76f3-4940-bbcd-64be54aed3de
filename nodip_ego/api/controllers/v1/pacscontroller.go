package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

// SavePacsresult ...
func SavePacsresult(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.InsertPacsResults(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// QueryPacsresult ...
func QueryPacsresult(c *gin.Context) {
	testid := c.Param("testid")

	if testid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有体检编号，不能查找"))
	}

	ret, err := service.QueryPacsResults(testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// DeletePacsresult ...
func DeletePacsresult(c *gin.Context) {
	testid := c.Param("testid")

	if testid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有体检编号，不能删除"))
	}

	err := service.DeletePacsResults(testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "len(*ret)", "ret")
}
