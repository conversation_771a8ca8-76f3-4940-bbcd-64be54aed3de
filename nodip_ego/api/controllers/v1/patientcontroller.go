package v1

import (
	"datacontroller/models"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"nodip_service/service"
	"utility/logger"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"
	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

// QueryPatients ... 支持按照多个pid查找，不同的pid，用“逗号（,）"隔开
func QueryPatients(c *gin.Context) {
	// pids := c.Param("pids")
	// if pids == "" {
	// 	response.ERROR(c, http.StatusOK, errors.New("pid不能为空"))
	// 	return
	// }

	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.PtQueryDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	// log.Printf("所有的查找条件信息: %s", string(body))
	ret, err := service.QueryPatients(ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

// InsertPatient ...
func InsertPatient(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// log.Printf("体检者信息: %s", string(body))
	ret, err := service.InsertPatientinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdatePatient ...
func UpdatePatient(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// log.Printf("更新体检者信息: %s", string(body))
	ret, err := service.UpdatePatientinfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// QueryPatientHazards ... query patient by id card
func QueryPatientHazards(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	// log.Printf("body data: %s\n", string(body))
	ret, err := service.QueryPatientHazards(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

// DeletePatientHazards ...
func DeletePatientHazards(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// log.Printf("body data: %s\n", string(body))
	err = service.RemovePatientHazards(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "ret")
}

// InsertPatientHazards ...
func InsertPatientHazards(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// log.Printf("InsertPatientHazards: %s\n", string(body))
	ret, err := service.InsertPatientHazards(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// UpdatePatientHazards
func UpdatePatientHazards(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	var info models.TjPatienthazards
	err = json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		response.ERROR(c, http.StatusOK, err)
	}

	err = service.UpdatePatientHazards(&info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, info)
}

// *********************patient diseases**********************

// QueryPatientDiseases ...
func QueryPatientDiseases(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	// log.Printf("QueryPatientDiseases body data: %s\n", string(body))
	ret, err := service.QueryPatientDiseases(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// DeletePatientDiseases ...
func DeletePatientDiseases(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// log.Printf("DeletePatientDiseases body data: %s\n", string(body))
	err = service.RemovePatientDiseases(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "ret")
}

// InsertPatientDiseases ...
func InsertPatientDiseases(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// log.Printf("InsertPatientDiseases: %s\n", string(body))
	ret, err := service.InsertPatientDiseases(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// UpdatePatientDiseases ...
func UpdatePatientDiseases(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// log.Printf("UpdatePatientDiseases: %s\n", string(body))
	ret, err := service.UpdatePatientDiseases(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}
