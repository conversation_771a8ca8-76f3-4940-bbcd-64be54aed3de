package v1

import (
	"datacontroller/models"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"nodip_service/service"
	"utility/logger"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"
	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

type HealthySurvey struct {
	Healthyinfo models.TjHealthyInfo
	Ocuhis      []models.TjOccupationHistory `json:"ocuhis"`
	Dishis      []models.TjDiseaseHistory    `json:"dishis"`
	Marriage    []models.TjMarriageHistory   `json:"marriage"`
}

func QueryQuestionnairInfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdPidDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	healthyinforet, ocuhisret, dishisret, marriageret, err := service.QueryQuestionnair(ids.Testid, ids.Pid)
	logger.Log.Infof("Result:%+v,%+v,%+v,%+v,%+v", healthyinforet, ocuhisret, dishisret, marriageret, err)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	// v1 := interface{healhealthyinforet, ocuhisret, dishisret, marriageret}
	v := HealthySurvey{Healthyinfo: healthyinforet, Ocuhis: ocuhisret, Dishis: dishisret, Marriage: marriageret}
	response.JSON(c, 1, v)
}

func InsertQuestionnairInfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var info = models.QuestionnairModel{}
	err = json.Unmarshal(body, &info)
	logger.Log.Infof("接收到的数据:%+v", info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.InsertQuestionnairInfo(&info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
	// response.JSON(c, 1, )
}

// 问卷基础信息
func QueryHealthyInfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	ret, err := service.FindHealthyinfo(ids.Testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

func InsertHealthyInfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var info = models.TjHealthyInfo{}
	err = json.Unmarshal(body, &info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	err = service.SaveHealthyInfo(&info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, info)
}

func DeleteHealthyInfo(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	err = service.DeleteHealthyInfo(ids.Testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	response.JSONModel(c, "OK", "ret")
}

// ------------- disease history ---------------
func QueryDiseaseHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	ret, err := service.FindDiseaseHistories(ids.Testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

func InsertDiseaseHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var info []models.TjDiseaseHistory
	err = json.Unmarshal(body, &info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	if len(info) <= 0 {
		response.JSONModel(c, "OK", "")
		return
	}

	ret, err := service.SaveDiseaseHistories(info[0].TjTestID, info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), *ret)
}

func DeleteDiseaseHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	err = service.DeleteDiseaseHistories(ids.Testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	response.JSONModel(c, "OK", "ret")
}

// end

// ============= 职业史信息 ================
func QueryOccupationHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	ret, err := service.FindOccupationHistories(ids.Testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

func InsertOccupationHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var info []models.TjOccupationHistory
	err = json.Unmarshal(body, &info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	if len(info) <= 0 {
		response.JSONModel(c, "OK", "")
		return
	}

	ret, err := service.SaveOccupationHistories(info[0].TjTestID, info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), *ret)
}

func DeleteOccupationHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	err = service.DeleteOccupationHistories(ids.Testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	response.JSONModel(c, "OK", "ret")
}

//婚姻史信息
func QueryMarriageHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	ret, err := service.FindMarriageHistories(ids.Testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

func InsertMarriageHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var info []models.TjMarriageHistory
	err = json.Unmarshal(body, &info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	if len(info) <= 0 {
		response.JSONModel(c, "OK", "")
		return
	}

	ret, err := service.SaveMarriageHistories(info[0].TjTestID, info)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), *ret)
}

func DeleteMarriageHistories(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	var ids = models.TestIdDTO{}
	err = json.Unmarshal(body, &ids)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	err = service.DeleteMarriageHistories(ids.Testid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	response.JSONModel(c, "OK", "ret")
}
