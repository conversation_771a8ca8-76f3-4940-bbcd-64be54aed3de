package v1

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"nodip_ego/api/app"
	"nodip_ego/api/docservice"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"
	"strconv"
	"utility/logger"

	"github.com/gin-gonic/gin"
)

func GenerateCorpoccureportDocumentV2(c *gin.Context) {
	// report_id_str := c.<PERSON>m("id")
	// report_id, err := strconv.ParseInt(report_id_str, 10, 64)
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		response.JSONError(c, err, "")
		return
	}
	var info = models.CorpReportDTO{}
	err = json.Unmarshal(body, &info)
	if err != nil {
		response.JSONError(c, err, "")
		return
	}
	logger.Log.Infof("需要处理的报告ID:%v", info)
	// var pagestyle int32 = 2
	// outdir := "./reports"
	filename, err := docservice.GenerateOccuSummaryReport(int64(info.Rptid), int32(info.Pagestyle), info.Outdir)
	if err != nil {
		response.JSONError(c, err, "")
		return
	}
	logger.Log.Infof("生成的报告:%s", filename)

	response.JSON(c, 1, filename)

}

func GenerateCorpoccureportDocument(c *gin.Context) {
	report_id_str := c.Param("id")
	report_id, err := strconv.ParseInt(report_id_str, 10, 64)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
	}
	logger.Log.Infof("需要处理的报告ID:%d", report_id)
	var pagestyle int32 = 1
	outdir := "./reports"
	filename, err := docservice.GenerateOccuSummaryReport(report_id, pagestyle, outdir)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
	}
	logger.Log.Infof("生成的报告:%s", filename)

	response.JSONModel(c, filename, "")

}

// QueryCorpoccureport ...
func DownloadCorpoccureportDocument(c *gin.Context) {
	// body, err := ioutil.ReadAll(c.Request.Body)
	filename := c.Param("filename")
	if filename == "" {
		response.ERROR(c, http.StatusOK, errors.New("no file to download"))
		return
	}

	DoDownload(c, app.DIRECTORY_REPORT, filename)

	// response.JSON(c, len(*ret), ret)
}

// ******************* Corpoccureport BEGIN ******************************

// QueryCorpoccureport ...
func QueryCorpoccureport(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.QueryCorpoccureport(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

// InsertCorpoccureport ...
func InsertCorpoccureport(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertCorpoccureport(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdateCorpoccureport ...
func UpdateCorpoccureport(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateCorpoccureport(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// DeleteCorpoccureport ...
func DeleteCorpoccureport(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}
	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteCorpoccureport(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Corpoccureport END ******************************

// ******************* CorpoccureportInfo BEGIN ******************************

// QueryCorpoccureportInfo ...
func QueryCorpoccureportInfo(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.QueryCorpoccureportInfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(ret), ret)
}

// InsertCorpoccureportInfo ...
func InsertCorpoccureportInfo(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertCorpoccureportInfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdateCorpoccureportInfo ...
func UpdateCorpoccureportInfo(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateCorpoccureportInfo(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// DeleteCorpoccureportInfo ...
func DeleteCorpoccureportInfo(c *gin.Context) {
	sid := c.Param("rptid")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}

	err := service.DeleteCorpoccureportInfo(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* CorpoccureportInfo END ******************************

// ******************* CorpoccureportFc BEGIN ******************************

// QueryCorpoccureportFc ...
func QueryCorpoccureportFc(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	ret, err := service.QueryCorpoccureportFc(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

// InsertCorpoccureportFc ...
func InsertCorpoccureportFc(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertCorpoccureportFc(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// UpdateCorpoccureportFc ...
func UpdateCorpoccureportFc(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateCorpoccureportFc(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// DeleteCorpoccureportFc ...
func DeleteCorpoccureportFc(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.DeleteCorpoccureportFc(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* CorpoccureportFc END ******************************
