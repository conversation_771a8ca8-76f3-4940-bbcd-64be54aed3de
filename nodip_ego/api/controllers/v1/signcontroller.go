package v1

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"io"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/app"
	"nodip_service/service"

	// "nodip_ego/api/models"
	"nodip_ego/api/response"
	// "nodip_ego/api/service"
	"os"
	"utility/logger"

	"github.com/gin-gonic/gin"
)

func UploadSignPhoto(c *gin.Context) {
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	filename := header.Filename
	logger.Log.Infof("File name : " + header.Filename)
	out, err := os.Create(app.DIRECTORY_SIGN + "/" + filename)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	defer out.Close()
	_, err = io.Copy(out, file)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	response.JSONModel(c, "OK", "")
}

// DownloadSignPhoto ...
func DownloadSignPhoto(c *gin.Context) {
	logger.Log.Infof("start to download sign photo")
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		response.ERROR(c, http.StatusOK, errors.New(""))
		return
	}
	var dto = models.TestIdPidDTO{}
	err = json.Unmarshal(body, &dto)
	logger.Log.Infof("接收到的数据:%+v", dto)
	if err != nil {
		response.ERROR(c, http.StatusOK, errors.New(""))
		return
	}
	// var filename string
	filename := ""
	if dto.Pid != "" {
		filename = dto.Pid + ".jpg"
	} else {
		ret, err := service.QueryMedinfoByTestID(dto.Testid)
		if err != nil {
			response.ERROR(c, http.StatusOK, errors.New(""))
			return
		}
		if len(ret) <= 0 {
			response.ERROR(c, http.StatusOK, errors.New("no result"))
			return
		}
		filename = (ret)[0].TjPid + ".jpg"
	}
	if filename == "" {
		response.ERROR(c, http.StatusOK, errors.New("no result"))
		return
	}
	logger.Log.Infof("file name to be download:%s", filename)
	DoDownload(c, app.DIRECTORY_SIGN, filename)
}

// QueryCorpoccureport ...
func DownloadElectronicSign(c *gin.Context) {
	logger.Log.Infof("start to download electronic sign photo")

	filename := c.Param("filename")
	if filename == "" {
		response.ERROR(c, http.StatusOK, errors.New("no file to download"))
		return
	}

	DoDownload(c, app.DIRECTORY_SIGN, filename)
}
