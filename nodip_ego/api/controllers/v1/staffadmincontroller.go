package v1

import (
	"errors"
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ******************* Staffadmin BEGIN ******************************

// QueryStaffadmin ...
func QueryStaffadmin(c *gin.Context) {
	sid := c.Param("staffno")

	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}

	ret, err := service.FindByStaffNo(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// QueryStaffadmins ...
func QueryStaffadmins(c *gin.Context) {
	sid := c.Param("groupid")

	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}
	gid, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}
	ret, err := service.QueryStaffadmins(gid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertStaffadmin ...
func InsertStaffadmin(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertStaffadmin(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

//UpdateStaffadmin ...
func UpdateStaffadmin(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	err = service.UpdateStaffadmin(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

//DeleteStaffadmin ...
func DeleteStaffadmin(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}
	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteStaffadmin(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Staffadmin END ******************************

// ******************* Staffright BEGIN ******************************

// QueryStaffright ...
func QueryStaffright(c *gin.Context) {
	sid := c.Param("staffid")

	if sid == "" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}
	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能query"))
		return
	}

	ret, err := service.QueryStaffright(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertStaffright ...
func InsertStaffright(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertStaffright(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// //UpdateStaffright ...
// func UpdateStaffright(c *gin.Context) {
// 	body, err := ioutil.ReadAll(c.Request.Body)
// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, err.Error())
// 		return
// 	}
// 	err = service.UpdateStaffright(body)
// 	if err != nil {
// 		response.ERROR(c, http.StatusOK, err)
// 		return
// 	}

// 	response.JSONModel(c, "OK", "")
// }

//DeleteStaffright ...
func DeleteStaffright(c *gin.Context) {
	sid := c.Param("staffid")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}
	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteStaffright(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Staffright END ******************************
// ******************* Staffdept BEGIN ******************************

// QueryStaffdept ...
func QueryStaffdept(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.QueryStaffdept(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertStaffdept ...
func InsertStaffdept(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.InsertStaffdept(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, 1, ret)
}

// //UpdateStaffdept ...
// func UpdateStaffdept(c *gin.Context) {
// 	body, err := ioutil.ReadAll(c.Request.Body)
// 	if err != nil {
// 		c.JSON(http.StatusInternalServerError, err.Error())
// 		return
// 	}
// 	err = service.UpdateStaffdept(body)
// 	if err != nil {
// 		response.ERROR(c, http.StatusOK, err)
// 		return
// 	}

// 	response.JSONModel(c, "OK", "")
// }

//DeleteStaffdept ...
func DeleteStaffdept(c *gin.Context) {
	sid := c.Param("id")

	if sid == "" || sid == "0" {
		response.ERROR(c, http.StatusOK, errors.New("没有ID，不能删除"))
	}
	id, err := strconv.Atoi(sid)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}
	err = service.DeleteStaffdept(id)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}

// ******************* Staffdept END ******************************
