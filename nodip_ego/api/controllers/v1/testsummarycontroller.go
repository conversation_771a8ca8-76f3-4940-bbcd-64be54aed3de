package v1

import (
	"io/ioutil"
	"net/http"
	"nodip_ego/api/response"
	"nodip_service/service"

	// "nodip_ego/api/service"

	"github.com/gin-gonic/gin"
)

//QueryTestSummaries ... 支持按照多个testid查找，
func QueryTestSummaries(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.QueryTestSummary(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//InsertTestSummaries ...
func InsertTestSummaries(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.<PERSON>rro<PERSON>())
		return
	}
	ret, err := service.InsertTestSummary(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//UpdateTestSummaries ...
func UpdateTestSummaries(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	ret, err := service.UpdateTestSummaries(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSON(c, len(*ret), ret)
}

//DeleteTestSummaries ...
func DeleteTestSummaries(c *gin.Context) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}

	err = service.DeleteTestSummary(body)
	if err != nil {
		response.ERROR(c, http.StatusOK, err)
		return
	}

	response.JSONModel(c, "OK", "")
}
