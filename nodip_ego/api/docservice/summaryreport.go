package docservice

import (
	"datacontroller/models"
	"fmt"
	"nodip_ego/api/app"
	"nodip_service/service"
	"os"
	"path/filepath"

	// "slices"
	"strings"
	"utility/logger"

	"strconv"
	"time"

	"github.com/unidoc/unioffice"
	"github.com/unidoc/unioffice/color"
	"github.com/unidoc/unioffice/common"
	"github.com/unidoc/unioffice/document"
	"github.com/unidoc/unioffice/measurement"
	"github.com/unidoc/unioffice/schema/soo/ofc/sharedTypes"
	"github.com/unidoc/unioffice/schema/soo/wml"
	"golang.org/x/exp/slices"
)

var reportModel = new(models.ReportModel)
var corpModel = new(models.CorpinfoModel)
var font_family = "SimSun"

// 生成报告
func GenerateOccuSummaryReport(report_id int64, pagestyle int32, outdir string) (string, error) {
	rptfile := ""

	//报告信息
	rptinfo, err := reportModel.GetCorpoccureportinfo(report_id, "")
	if err != nil {
		logger.Log.Errorf("查找报告信息失败，错误：%+v", err)
		return "", err
	}

	//体检单位信息
	corpinfo, err := corpModel.QueryCorpinfobyID(rptinfo.TjCorpid, "")
	if err != nil {
		return "", err
	}

	dto := new(models.ReportinfoDTO)
	dto.Reportids = append(dto.Reportids, strconv.FormatInt(report_id, 10))
	testerinfos, err := reportModel.GetCorpoccureportInfo(dto)
	if err != nil {
		return "", err
	}
	var testids []string
	for _, val := range testerinfos {
		testids = append(testids, val.TjTestID)
	}
	// if len(testerinfos) <= 0 {
	// 	return "", errors.New("没有体检人员信息")
	// }
	var cadto = new(models.CaQueryDTO)
	cadto.RetType = -1
	cadto.CaStatus = -1
	cadto.Testids = testids
	//所有总检信息
	checkallinfos, err := service.QueryCheckall(cadto)
	if err != nil {
		return "", err
	}
	//体检信息
	meddto := models.MedQueryDTO{}
	meddto.Cpme = -1
	meddto.Corpid = -1
	meddto.Testtype = -1
	meddto.Statuslow = -1
	meddto.Statushigh = -1
	meddto.Isrecheck = -1
	meddto.Testid = testids
	medinfos, err := service.QueryMedinfo(meddto)
	if err != nil {
		return "", err
	}
	//体检人员信息
	var ids models.PtQueryDTO
	ids.Testids = testids
	patients, err := service.QueryPatients(ids)
	if err != nil {
		return "", err
	}
	//字典信息
	dicts, err := service.GetDictionaries(0)
	if err != nil {
		return "", err
	}
	customer := ""
	for _, val := range dicts {
		if val.SsTypeid == 19 && val.SsPid == 28 {
			customer = strings.ToUpper(val.SsShort)
			break
		}
	}
	logger.Log.Infof("当前的customer：%s", customer)
	if customer == app.Wzdams {
		//处理温州的报告
		rptfile, err = GenerateOccupationalSummaryReportWz(rptinfo, &medinfos, &checkallinfos, &patients, corpinfo, &dicts, customer, pagestyle, outdir)
		if err != nil {
			return "", err
		}
	} else {
		//处理其他报告
		rptfile, err = GenerateOccupationalSummaryReport(rptinfo, &medinfos, &checkallinfos, &patients, corpinfo, &dicts, customer, pagestyle, outdir)
		if err != nil {
			return "", err
		}
	}

	return rptfile, nil
}

// / 根据报告ID，生成职业健康报告表（docx格式）
func GenerateOccupationalSummaryReport(report *models.TjCorpoccureport, medinfos *[]models.TjMedexaminfo, checkallinfos *[]models.TjCheckallnew, patients *[]models.TjPatient, corpinfo *models.TjCorpinfo, dicts *[]models.SsDictionary, customer string, pagestyle int32, outdir string) (string, error) {

	// filename := fmt.Sprintf("ocu_report_%s.docx", report.TjReportnumint)

	var oculike_data []models.TjCheckallnew
	var forbidden_data []models.TjCheckallnew
	var recheck_data []models.TjCheckallnew
	var other_data []models.TjCheckallnew
	var add_data []models.TjCheckallnew
	for _, ca := range *checkallinfos {
		if ca.TjTypeid == app.Suspected {
			oculike_data = append(oculike_data, ca)
		} else if ca.TjTypeid == app.Forbidden {
			forbidden_data = append(forbidden_data, ca)
		} else if ca.TjTypeid == app.Recheck {
			recheck_data = append(recheck_data, ca)
		} else if ca.TjTypeid == app.Supplementary {
			add_data = append(add_data, ca)
		} else {
			other_data = append(other_data, ca)
		}
	}
	logger.Log.Infof("疑似的人数:%d", len(oculike_data))
	logger.Log.Infof("禁忌的人数:%d", len(forbidden_data))
	logger.Log.Infof("复查的人数:%d", len(recheck_data))
	logger.Log.Infof("其它的人数:%d", len(other_data))

	doc := document.New()
	margin := measurement.Distance(measurement.Millimeter * 10)
	left_margin := measurement.Distance(measurement.Millimeter * 5)

	// doc.BodySection().
	doc_section := doc.BodySection()
	doc_section.SetPageMargins(left_margin, margin, margin, margin, (margin), (margin), (margin))

	generate_report_front(doc, report, corpinfo, dicts, customer)
	//backend page
	generate_report_back(doc, dicts)
	doc.AddParagraph().AddRun().AddPageBreak()

	add_report_header(doc, report, pagestyle)
	//一般结果
	summary := report.TjResult
	generate_report_result(doc, report, corpinfo, dicts, summary)
	// doc.AddParagraph().AddRun().AddPageBreak()

	set_report_page_style(doc, pagestyle)

	//t1
	title := "表1、疑似职业病和职业禁忌证人员名单"
	// for _, val := range forbidden_data {
	// 	oculike_data = append(oculike_data, val)
	// }
	oculike_data = append(oculike_data, forbidden_data...)
	generate_report_tableT1T2(doc, oculike_data, medinfos, patients, title)
	doc.AddParagraph().AddRun().AddBreak()

	//t2
	title = "表2、需复查人员名单"
	generate_report_tableT1T2(doc, recheck_data, medinfos, patients, title)
	doc.AddParagraph().AddRun().AddText("")

	//t3
	title = "表3、其他人员名单（表1-2所列人员以外的受检人员）"
	generate_report_tableT3(doc, other_data, medinfos, patients, title)
	doc.AddParagraph().AddRun().AddBreak()

	//t4
	if len(add_data) > 0 {
		title = "表4、需补检人员名单"
		generate_report_tableT4(doc, add_data, medinfos, patients, title)
		doc.AddParagraph().AddRun().AddBreak()
	}
	// doc.AddParagraph().AddRun().AddPageBreak()
	//sign page
	generate_sign_page(doc, report, dicts, pagestyle, customer)
	//

	filename := fmt.Sprintf("%s-%s.docx", report.TjCorpname, report.TjReportnumint)
	filepath := filepath.Join(app.DIRECTORY_REPORT, filename)
	logger.Log.Infof("report file: %s", filepath)
	doc.SaveToFile(filepath)

	return filename, nil
}

func GenerateOccupationalSummaryReportWz(report *models.TjCorpoccureport, medinfos *[]models.TjMedexaminfo, checkallinfos *[]models.TjCheckallnew, patients *[]models.TjPatient, corpinfo *models.TjCorpinfo, dicts *[]models.SsDictionary, customer string, pagestyle int32, outdir string) (string, error) {

	var oculike_data []models.TjCheckallnew
	var forbidden_data []models.TjCheckallnew
	var recheck_data []models.TjCheckallnew
	var other_data []models.TjCheckallnew
	var add_data []models.TjCheckallnew
	for _, ca := range *checkallinfos {
		if ca.TjTypeid == app.Suspected {
			oculike_data = append(oculike_data, ca)
		} else if ca.TjTypeid == app.Forbidden {
			forbidden_data = append(forbidden_data, ca)
		} else if ca.TjTypeid == app.Recheck {
			recheck_data = append(recheck_data, ca)
		} else if ca.TjTypeid == app.Supplementary {
			add_data = append(add_data, ca)
		} else {
			other_data = append(other_data, ca)
		}
	}
	logger.Log.Infof("疑似的人数:%d", len(oculike_data))
	logger.Log.Infof("禁忌的人数:%d", len(forbidden_data))
	logger.Log.Infof("复查的人数:%d", len(recheck_data))
	logger.Log.Infof("其它的人数:%d", len(other_data))

	doc := document.New()
	margin := measurement.Distance(measurement.Millimeter * 10)
	left_margin := measurement.Distance(measurement.Millimeter * 5)
	gutter := measurement.Distance(measurement.Millimeter * 0)

	var idx int = 1
	doc_section := doc.BodySection()
	doc_section.SetPageMargins(left_margin, margin, margin, margin, (margin), (margin), (gutter))

	generate_report_front(doc, report, corpinfo, dicts, customer)
	//backend page
	generate_report_back(doc, dicts)
	doc.AddParagraph().AddRun().AddPageBreak()

	add_report_header(doc, report, pagestyle)
	//一般结果
	summary := fmt.Sprintf("本次职业健康检查发现：疑似职业病%d人，职业禁忌证%d人，需要复查人员%d人。详见附表：", len(oculike_data), len(forbidden_data), len(recheck_data))
	generate_report_result(doc, report, corpinfo, dicts, summary)
	// doc.AddParagraph().AddRun().AddPageBreak()

	set_report_page_style(doc, pagestyle)

	//t1
	if len(oculike_data) > 0 {
		title := fmt.Sprintf("表%d、疑似职业病人员名单", idx)
		generate_report_tableT1T2(doc, oculike_data, medinfos, patients, title)
		idx += 1
		doc.AddParagraph().AddRun().AddText("")
	}
	if len(forbidden_data) > 0 {
		title := fmt.Sprintf("表%d、职业禁忌证人员名单", idx)
		generate_report_tableT1T2(doc, forbidden_data, medinfos, patients, title)
		idx += 1
		doc.AddParagraph().AddRun().AddText("")
	}
	//t2
	if len(recheck_data) > 0 {
		title := fmt.Sprintf("表%d、需复查人员名单", idx)
		generate_report_tableT1T2(doc, recheck_data, medinfos, patients, title)
		idx += 1
		doc.AddParagraph().AddRun().AddText("")
	}
	//t3
	if len(other_data) > 0 {
		title := ""
		if idx == 1 {
			title = fmt.Sprintf("表%d、受检人员名单", idx)
		} else {
			title = fmt.Sprintf("表%d、其他人员名单", idx)
		}
		generate_report_tableT3(doc, other_data, medinfos, patients, title)
		idx += 1
		doc.AddParagraph().AddRun().AddText("")
	}
	//t4
	if len(add_data) > 0 {
		title := fmt.Sprintf("表%d、需补检人员名单", idx)
		generate_report_tableT4(doc, add_data, medinfos, patients, title)
		doc.AddParagraph().AddRun().AddText("")
	}
	// doc.AddParagraph().AddRun().AddPageBreak()
	//sign page
	generate_sign_page(doc, report, dicts, pagestyle, customer)
	//

	filename := fmt.Sprintf("%s-%s.docx", report.TjCorpname, report.TjReportnumint)
	filepath := filepath.Join(app.DIRECTORY_REPORT, filename)
	logger.Log.Infof("report file: %s", filepath)
	doc.SaveToFile(filepath)

	return filename, nil
}

// // generate_occupational_summary_report_docx
// func GenerateOccupationalSummaryReport(report_id int64) (string, models.TjCorpinfo, models.TjCorpoccureport, []models.TjCheckallnew, []models.TjMedexaminfo, []models.TjPatient, error) {
// 	//报告信息
// 	report, err := reportModel.GetCorpoccureportinfo(report_id, "")
// 	if err != nil {
// 		logger.Log.Errorf("查找报告信息失败，错误：%+v", err)
// 		return "", models.TjCorpinfo{}, models.TjCorpoccureport{}, nil, nil, nil, err
// 	}
// 	// logger.Log.Infof("体检报告信息存在，接下来查找体检单位信息")
// 	//体检单位信息
// 	corpinfo, err := corpModel.QueryCorpinfobyID(report.TjCorpid, "")
// 	if err != nil {
// 		return "", models.TjCorpinfo{}, models.TjCorpoccureport{}, nil, nil, nil, err
// 	}

// 	var testerinfos []models.TjCorpoccureportInfo
// 	var qret []models.TjCheckallnew //= new([]models.TjCheckallnew)
// 	var medret []models.TjMedexaminfo
// 	var pret []models.TjPatient //= new([]models.TjPatient)

// 	//该报告所有体检者信息 query from tj_corpoccureport_info by tj_report_id
// 	dto := new(models.ReportinfoDTO)
// 	dto.Reportids = append(dto.Reportids, strconv.FormatInt(report_id, 10))
// 	testerinfos, err = reportModel.GetCorpoccureportInfo(dto)
// 	if err != nil {
// 		return "", models.TjCorpinfo{}, models.TjCorpoccureport{}, nil, nil, nil, err
// 	}
// 	// logger.Log.Infof("报告人员信息：%+v", testerinfos)
// 	if len(testerinfos) > 0 {
// 		//总检信息
// 		var cadto = new(models.CaQueryDTO)
// 		cadto.RetType = -1
// 		cadto.CaStatus = -1
// 		for _, val := range testerinfos {
// 			cadto.Testids = append(cadto.Testids, val.TjTestID)
// 		}
// 		// logger.Log.Infof("总检查询条件：%+v", cadto)
// 		qret, err = service.QueryCheckall(cadto)
// 		if err != nil {
// 			return "", models.TjCorpinfo{}, models.TjCorpoccureport{}, nil, nil, nil, err
// 		}
// 		//体检信息
// 		meddto := models.MedQueryDTO{}
// 		meddto.Cpme = -1
// 		meddto.Corpid = -1
// 		meddto.Testtype = -1
// 		meddto.Statuslow = -1
// 		meddto.Statushigh = -1
// 		meddto.Isrecheck = -1
// 		for _, val := range testerinfos {
// 			meddto.Testid = append(meddto.Testid, val.TjTestID)
// 		}
// 		medret, err = service.QueryMedinfo(meddto)
// 		if err != nil {
// 			return "", models.TjCorpinfo{}, models.TjCorpoccureport{}, nil, nil, nil, err
// 		}
// 		// logger.Log.Infof("体检信息：%+v", medret)
// 		//体检人员信息
// 		var ids models.PtQueryDTO
// 		for _, val := range testerinfos {
// 			ids.Testids = append(cadto.Testids, val.TjTestID)
// 		}
// 		pret, err = service.QueryPatients(ids)
// 		if err != nil {
// 			return "", models.TjCorpinfo{}, models.TjCorpoccureport{}, nil, nil, nil, err
// 		}
// 	}
// 	//字典信息
// 	dicts, err := service.GetDictionaries(0)
// 	if err != nil {
// 		return "", models.TjCorpinfo{}, models.TjCorpoccureport{}, nil, nil, nil, err
// 	}

// 	doc := document.New()
// 	margin := measurement.Distance(measurement.Millimeter * 10)
// 	left_margin := measurement.Distance(measurement.Millimeter * 5)

// 	// doc.BodySection().
// 	doc_section := doc.BodySection()
// 	doc_section.SetPageMargins(left_margin, margin, margin, margin, (margin), (margin), (margin))

// 	generate_report_front(doc, report, corpinfo, &dicts)
// 	//backend page
// 	generate_report_back(doc, &dicts)
// 	doc.AddParagraph().AddRun().AddPageBreak()

// 	add_report_header(doc, report)
// 	//一般结果
// 	generate_report_result(doc, report, corpinfo, &dicts)
// 	// doc.AddParagraph().AddRun().AddPageBreak()

// 	page_margin := measurement.Distance(measurement.Millimeter * 10)
// 	doc.BodySection().SetPageMargins(left_margin, page_margin, page_margin, page_margin, page_margin, page_margin, page_margin)
// 	//t1
// 	title := "表1、疑似职业病和职业禁忌证人员名单"
// 	// for _, val := range forbidden_data {
// 	// 	oculike_data = append(oculike_data, val)
// 	// }
// 	generate_report_tableT1T2(doc, &qret, &medret, &pret, title)

// 	//t2
// 	title = "表2、需要复查人员名单"
// 	generate_report_tableT1T2(doc, &qret, &medret, &pret, title)

// 	//t3
// 	title = "表3、其他人员名单（表1-2所列人员以外的受检人员）"
// 	generate_report_tableT3(doc, &qret, &medret, &pret, title)

// 	//t4
// 	// if len(add_data) > 0 {
// 	title = "表4、需补检人员名单"
// 	generate_report_tableT4(doc, &qret, &medret, &pret, title)
// 	// }
// 	// doc.AddParagraph().AddRun().AddPageBreak()
// 	//sign page
// 	generate_sign_page(doc, report, &dicts)
// 	//

// 	filename := fmt.Sprintf("ocu_report_%s.docx", report.TjReportnumint)
// 	filepath := filepath.Join(app.DIRECTORY_REPORT, filename)
// 	logger.Log.Infof("report file: %s", filepath)
// 	doc.SaveToFile(filepath)

// 	return filename, *corpinfo, *report, qret, medret, pret, nil
// }

func set_report_page_style(doc *document.Document, pagestyle int32) {
	if pagestyle == 1 {
		sectPr := doc.BodySection()
		sectPr.X().PgSz = wml.NewCT_PageSz()
		sectPr.X().PgSz.CodeAttr = unioffice.Int64(9)
		sectPr.X().PgSz.OrientAttr = wml.ST_PageOrientationLandscape
		sectPr.X().PgSz.WAttr = &sharedTypes.ST_TwipsMeasure{ST_UnsignedDecimalNumber: unioffice.Uint64(16839)}
		sectPr.X().PgSz.HAttr = &sharedTypes.ST_TwipsMeasure{ST_UnsignedDecimalNumber: unioffice.Uint64(11907)}
		sectPr.SetPageMargins(35, 25, 20, 25, 30, 30, 0)
	} else {
		// page_margin := measurement.Distance(measurement.Millimeter * 10)
		doc.BodySection().SetPageMargins(35, 25, 20, 25, 30, 30, 0)
	}

}

// generate_report_front
func generate_report_front(doc *document.Document, rptinfo *models.TjCorpoccureport, corpinfo *models.TjCorpinfo, dicts *[]models.SsDictionary, customer string) {

	para_serial_no := doc.AddParagraph()

	para_serial_no.SetStyle("Heading4")
	// para_serial_no.Properties().SetStartIndent(4 * measurement.Inch)
	para_serial_no.Properties().SetAlignment(wml.ST_JcRight)

	serial_no := para_serial_no.AddRun()
	serial_no.AddBreak()
	serial_no.Properties().SetFontFamily(font_family)
	serial_no.AddText(rptinfo.TjReportnum)
	serial_no.AddBreak()
	serial_no.AddBreak()
	serial_no.AddBreak()

	para_title := doc.AddParagraph()
	para_title.SetStyle("Title")
	// para_title.Properties().SetStartIndent(0.8 * measurement.Inch)
	para_title.Properties().SetAlignment(wml.ST_JcCenter)

	run_title := para_title.AddRun()
	run_title.Properties().SetSize(36)
	run_title.Properties().SetFontFamily(font_family)
	run_title.AddText("职业健康检查报告书")
	run_title.AddBreak()
	run_title.AddBreak()

	hdr := doc.AddHeader()
	para := hdr.AddParagraph()
	para.Properties().AddTabStop(2.5*measurement.Inch, wml.ST_TabJcCenter, wml.ST_TabTlcNone)

	// para.Properties().SetStartIndent(2 * measurement.Inch)
	table := doc.AddTable()
	// table.Properties().SetWidthPercent(90)

	table.Properties().SetWidth(measurement.Millimeter * 160)
	// table.Properties().Borders().SetAll(wml.ST_BorderNone, color.Auto, measurement.Zero)
	rowheight := 30
	// labelwidth := 55
	vertialalign := wml.ST_VerticalJcCenter
	// topalign := wml.ST_TextAlignmentTop
	font_size := measurement.Distance(15)
	var imgsize = measurement.Distance(0.18 * measurement.Inch)
	imgyes, err := common.ImageFromFile("./images/yes.png")
	if err != nil {
		logger.Log.Errorf("Can't load image:./images/yes.png")
	}
	imgyesref, err := doc.AddImage(imgyes)
	if err != nil {
		logger.Log.Errorf("Can't load image:./images/yes.png")
	}
	imgno, err := common.ImageFromFile("./images/no.png")
	if err != nil {
		logger.Log.Errorf("Can't load image:./images/no.png")
	}
	imgnoref, err := doc.AddImage(imgno)
	if err != nil {
		logger.Log.Errorf("Can't load image:./images/no.png")
	}
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().SetWidth(measurement.Millimeter * 30)
		para := cell.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcRight)
		para.Properties().SetStartIndent(0)
		run := para.AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("用人单位: ")

		cell = row.AddCell()
		para.Properties().SetAlignment(wml.ST_JcRight)
		cell.Properties().SetWidth(measurement.Millimeter * 130)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(rptinfo.TjCorpname)
	}
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().SetWidth(measurement.Millimeter * 30)
		// cell.Properties().Margins().SetTop(measurement.Distance(margin))
		para := cell.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcRight)
		run := para.AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("单位地址: ")
		// row.AddCell().AddParagraph().AddRun().AddText("宁夏钢铁（集团）有限责任公司")

		cell = row.AddCell()
		cell.Properties().SetWidth(measurement.Millimeter * 130)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(corpinfo.TjAddress)
	}
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetWidth(measurement.Millimeter * 30)
		cell.Properties().SetVerticalAlignment(vertialalign)
		para := cell.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcRight)
		run := para.AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("联系电话: ")
		// row.AddCell().AddParagraph().AddRun().AddText("宁夏钢铁（集团）有限责任公司")

		cell = row.AddCell()
		cell.Properties().SetWidth(measurement.Millimeter * 130)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(corpinfo.TjPhone)
	}
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().SetColumnSpan(2)
		// cell.Properties().Margins().SetTop(measurement.Distance(margin))
		run := cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.AddText("")
	}

	row := table.AddRow()
	row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

	cell := row.AddCell()
	cell.Properties().SetWidth(measurement.Millimeter * 30)
	cell.Properties().SetVerticalAlignment(wml.ST_VerticalJcTop)
	para = cell.AddParagraph()
	para.Properties().SetAlignment(wml.ST_JcRight)
	run := para.AddRun()
	run.Properties().SetSize(font_size)
	run.Properties().SetFontFamily(font_family)
	run.AddText("体检类型:")

	cell = row.AddCell()
	cell.Properties().SetWidth(measurement.Millimeter * 130)
	cell.Properties().Borders().SetBottom(wml.ST_BorderNone, color.Auto, 1*measurement.Point)
	cell.Properties().SetVerticalAlignment(vertialalign)
	run = cell.AddParagraph().AddRun()
	run.Properties().SetSize(font_size)
	run.Properties().SetFontFamily(font_family)

	shanggan_name := "上岗"
	zaigang_name := "在岗"
	ligang_name := "离岗"
	yingji_name := "应急"

	for _, val := range *dicts {
		if val.SsTypeid == 5 {
			if val.SsPid == 3 {
				shanggan_name = val.SsName
			}
			if val.SsPid == 4 {
				zaigang_name = val.SsName
			}
			if val.SsPid == 5 {
				ligang_name = val.SsName
			}
			if val.SsPid == 6 {
				yingji_name = val.SsName
			}
		}
	}

	if rptinfo.TjTesttype == 3 {
		inl, err := run.AddDrawingInline(imgyesref)
		if err != nil {
			logger.Log.Errorf("unable to add inline image: %s", err)
		}
		inl.SetSize(imgsize, imgsize)
	} else {
		inl, err := run.AddDrawingInline(imgnoref)
		if err != nil {
			logger.Log.Errorf("unable to add inline image: %s", err)
		}
		inl.SetSize(imgsize, imgsize)
	}
	run.AddText(" " + shanggan_name)
	run.AddBreak()

	if rptinfo.TjTesttype == 4 {
		inl, err := run.AddDrawingInline(imgyesref)
		if err != nil {
			logger.Log.Errorf("unable to add inline image: %s", err)
		}
		inl.SetSize(imgsize, imgsize)
		// inl.SetOrigin(wml.WdST_RelFromHPage, wml.WdST_RelFromVTopMargin)
		// inl.SetHAlignment(wml.WdST_AlignHCenter)
		// inl.SetYOffset(3 * measurement.Inch)
		// inl.SetTextWrapSquare(wml.WdST_WrapTextBothSides)
	} else {
		inl, err := run.AddDrawingInline(imgnoref)
		if err != nil {
			logger.Log.Errorf("unable to add inline image: %s", err)
		}
		inl.SetSize(imgsize, imgsize)
	}
	run.AddText(" " + zaigang_name)
	run.AddBreak()

	if rptinfo.TjTesttype == 5 {
		inl, err := run.AddDrawingInline(imgyesref)
		if err != nil {
			logger.Log.Errorf("unable to add inline image: %s", err)
		}
		inl.SetSize(imgsize, imgsize)
	} else {
		inl, err := run.AddDrawingInline(imgnoref)
		if err != nil {
			logger.Log.Errorf("unable to add inline image: %s", err)
		}
		inl.SetSize(imgsize, imgsize)
	}
	run.AddText(" " + ligang_name)
	run.AddBreak()
	if rptinfo.TjTesttype == 6 {
		inl, err := run.AddDrawingInline(imgyesref)
		if err != nil {
			logger.Log.Errorf("unable to add inline image: %s", err)
		}
		inl.SetSize(imgsize, imgsize)
	} else {
		inl, err := run.AddDrawingInline(imgnoref)
		if err != nil {
			logger.Log.Errorf("unable to add inline image: %s", err)
		}
		inl.SetSize(imgsize, imgsize)
	}
	run.AddText(" " + yingji_name)

	if customer != app.Wzdams {
		{
			row := table.AddRow()
			row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

			cell := row.AddCell()
			cell.Properties().SetVerticalAlignment(vertialalign)
			cell.Properties().SetColumnSpan(2)
			// cell.Properties().Margins().SetTop(measurement.Distance(margin))
			run := cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.AddText("")
		}
		{
			row := table.AddRow()
			row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

			cell := row.AddCell()
			cell.Properties().SetWidth(measurement.Millimeter * 30)
			cell.Properties().SetVerticalAlignment(wml.ST_VerticalJcCenter)
			para := cell.AddParagraph()
			para.Properties().SetAlignment(wml.ST_JcRight)
			run := para.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText("复    查:")

			cell = row.AddCell()
			cell.Properties().SetWidth(measurement.Millimeter * 130)
			cell.Properties().Borders().SetBottom(wml.ST_BorderNone, color.Auto, 1*measurement.Point)
			cell.Properties().SetVerticalAlignment(vertialalign)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			if rptinfo.TjIsrecheck == 1 {
				inl, err := run.AddDrawingInline(imgyesref)
				if err != nil {
					logger.Log.Errorf("unable to add inline image: %s", err)
				}
				inl.SetSize(imgsize, imgsize)
			} else {
				inl, err := run.AddDrawingInline(imgnoref)
				if err != nil {
					logger.Log.Errorf("unable to add inline image: %s", err)
				}
				inl.SetSize(imgsize, imgsize)
			}
			run.AddText(" 复查")
		}
	} else {
		run.AddBreak()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		if rptinfo.TjIsrecheck == 1 {
			inl, err := run.AddDrawingInline(imgyesref)
			if err != nil {
				logger.Log.Errorf("unable to add inline image: %s", err)
			}
			inl.SetSize(imgsize, imgsize)
		} else {
			inl, err := run.AddDrawingInline(imgnoref)
			if err != nil {
				logger.Log.Errorf("unable to add inline image: %s", err)
			}
			inl.SetSize(imgsize, imgsize)
		}
		run.AddText(" 复查")
	}
	//************
	if customer == app.Wzdams {
		doc.AddParagraph()
		doc.AddParagraph()
		doc.AddParagraph()
	}
	para = doc.AddParagraph()
	para.Properties().SetAlignment(wml.ST_JcCenter)
	run = para.AddRun()
	run.AddBreak()
	run.AddBreak()
	run.AddBreak()
	run.AddBreak()
	// run.AddBreak()
	run.Properties().SetSize(20)
	run.Properties().SetFontFamily(font_family)
	dict := service.FindDictionary(dicts, 10, 1)
	if dict == nil {
		run.AddText("")
	} else {
		run.AddText(dict.SsName)
	}
	run.AddBreak()
	tm := time.Unix(rptinfo.TjCreatedate, 0)
	run.Properties().SetSize(16)
	run.Properties().SetFontFamily(font_family)
	// logger.Log.Infof("%d create date: %+v", rptinfo.TjCreatedate, tm.Format("2006年01月02日"))
	run.AddText(tm.Format("2006年01月02日"))

	// run.AddPageBreak()
	para = doc.AddParagraph()
	section := para.Properties().AddSection(wml.ST_SectionMarkNextPage)
	section.SetHeader(hdr, wml.ST_HdrFtrDefault)

}

func generate_report_result(doc *document.Document, rptinfo *models.TjCorpoccureport, corpinfo *models.TjCorpinfo, dicts *[]models.SsDictionary, summary string) {

	doc.AddParagraph()

	hdr := doc.AddHeader()
	hdrpara := hdr.AddParagraph()
	hdrpara.Properties().AddTabStop(160*measurement.Millimeter, wml.ST_TabJcCenter, wml.ST_TabTlcNone)

	table := doc.AddTable()
	//总宽度是190mm
	// table.Properties().SetCellSpacing(5)
	// table.Properties().SetWidthPercent(100)
	table.Properties().SetWidth(measurement.Millimeter * 180)
	borders := table.Properties().Borders()
	borders.SetAll(wml.ST_BorderNone, color.Auto, 1*measurement.Point)

	rowheight := 25
	labelwidth := measurement.Distance(measurement.Millimeter * 25)
	leftwidth := measurement.Distance(measurement.Millimeter * 90)
	rightwidth := measurement.Distance(measurement.Millimeter * 40)
	vertialalign := wml.ST_VerticalJcCenter
	// margin := 8
	font_size := measurement.Distance(12)
	// row 1
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)

		para := cell.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)

		run := para.AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)

		run.AddText("用人单位:")

		cell = row.AddCell()
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().SetWidth(leftwidth)
		// cell.Properties().SetWidthPercent(60)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		// cell.Properties().SetWidthAuto()
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(rptinfo.TjCorpname)

		cell = row.AddCell()
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().SetWidth(labelwidth)
		// cell.Properties().SetWidthPercent(10)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("联系电话:")

		cell = row.AddCell()
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().SetWidth(rightwidth)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		// cell.Properties().SetWidthPercent(20)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetFontFamily(font_family)
		run.Properties().SetSize(font_size)
		run.AddText(corpinfo.TjPhone)
	}
	// row 2
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().SetWidth(labelwidth)
		run := cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("地   址:")

		cell = row.AddCell()
		cell.Properties().SetWidth(160 * measurement.Millimeter)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetColumnSpan(3)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetFontFamily(font_family)
		run.Properties().SetSize(font_size)
		run.AddText(corpinfo.TjAddress)
	}
	// row 3
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)
		// row.Properties().SetHeight(measurement.Distance(20), wml.ST_HeightRuleExact)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run := cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("体检日期:")

		cell = row.AddCell()
		cell.Properties().SetWidth(leftwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(rptinfo.TjTestdate)

		cell = row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("体检类别:")

		cell = row.AddCell()
		cell.Properties().SetWidth(rightwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		typename := rptinfo.TjTypename
		idx := slices.IndexFunc(*dicts, func(c models.SsDictionary) bool { return c.SsTypeid == 5 && c.SsPid == rptinfo.TjTesttype })
		if idx > 0 {
			typename = (*dicts)[idx].SsName
		}
		run.AddText(typename)
	}
	// row 4
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run := cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("体检地址:")
		// row.AddCell().AddParagraph().AddRun().AddText("宁夏钢铁（集团）有限责任公司")

		cell = row.AddCell()
		cell.Properties().SetWidth(measurement.Distance(measurement.Millimeter * 150))
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetColumnSpan(3)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(rptinfo.TjTestaddress)
	}
	// row 5
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)
		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run := cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("应检人数:")

		cell = row.AddCell()
		cell.Properties().SetWidth(leftwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(strconv.Itoa(rptinfo.TjApeoplenum))

		cell = row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("受检人数:")

		cell = row.AddCell()
		cell.Properties().SetWidth(rightwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(strconv.Itoa(rptinfo.TjPeoplenum))
	}
	// row 6
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run := cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("危害因素:")

		cell = row.AddCell()
		cell.Properties().SetWidth(measurement.Distance(measurement.Millimeter * 150))
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetColumnSpan(3)
		cell.Properties().SetVerticalAlignment(vertialalign)
		para := cell.AddParagraph()
		para.Properties().Spacing().SetLineSpacing(17*measurement.Point, wml.ST_LineSpacingRuleExact)
		run = para.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.Properties().SetSize(font_size)
		run.AddText(rptinfo.TjPoisions)
	}

	// row 7
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		// cell.Properties().Margins().SetTop(measurement.Distance(margin))
		run := cell.AddParagraph().AddRun()
		run.Properties().SetFontFamily(font_family)
		run.Properties().SetSize(font_size)
		run.AddText("体检项目:")
		// row.AddCell().AddParagraph().AddRun().AddText("宁夏钢铁（集团）有限责任公司")

		cell = row.AddCell()
		cell.Properties().SetWidth(measurement.Distance(measurement.Millimeter * 150))
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetColumnSpan(3)
		cell.Properties().SetVerticalAlignment(vertialalign)
		para := cell.AddParagraph()
		para.Properties().Spacing().SetLineSpacing(17*measurement.Point, wml.ST_LineSpacingRuleExact)
		run = para.AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(rptinfo.TjTestitems)
	}

	//评价依据
	// row 8
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		// cell.Properties().Margins().SetTop(measurement.Distance(margin))
		run := cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("评价依据:")
		// row.AddCell().AddParagraph().AddRun().AddText("宁夏钢铁（集团）有限责任公司")

		cell = row.AddCell()
		cell.Properties().SetWidth(measurement.Distance(measurement.Millimeter * 150))
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetColumnSpan(3)
		cell.Properties().SetVerticalAlignment(vertialalign)
		para := cell.AddParagraph()
		para.Properties().Spacing().SetLineSpacing(17*measurement.Point, wml.ST_LineSpacingRuleExact)
		run = para.AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(rptinfo.TjEvalaw)
	}

	//备注说明
	// row 8
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(rowheight), wml.ST_HeightRuleAtLeast)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run := cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText("备注说明:")

		cell = row.AddCell()
		cell.Properties().SetWidth(measurement.Millimeter * 150)
		cell.Properties().Borders().SetBottom(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)
		cell.Properties().SetColumnSpan(3)
		cell.Properties().SetVerticalAlignment(vertialalign)
		run = cell.AddParagraph().AddRun()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		run.AddText(rptinfo.TjMemo)
	}
	{
		para := doc.AddParagraph()
		para.SetStyle("Title")

		run := para.AddRun()
		run.AddBreak()
		run.AddBreak()
		run.Properties().SetSize(20)
		run.Properties().SetFontFamily(font_family)
		run.AddText("体检结论与处理意见/医学建议:")
	}

	{
		font_size = measurement.Distance(14)
		para := doc.AddParagraph()
		para.Properties().Spacing().SetLineSpacing(19*measurement.Point, wml.ST_LineSpacingRuleExact)
		para.Properties().SetAlignment(wml.ST_JcLeft)
		para.Properties().SetFirstLineIndent(measurement.Distance(4 * measurement.Character))
		// para.Properties().SetStartIndent(measurement.Character * 4)
		run := para.AddRun()
		run.AddBreak()
		run.Properties().SetSize(font_size)
		run.Properties().SetFontFamily(font_family)
		// result := rptinfo.TjResult
		// if customer == app.Wzdams {
		// 	result = fmt.Sprintf("本次职业健康检查发现：疑似职业病%d人，职业禁忌证%d人，需要复查人员%d人。详见附表：")
		// }
		run.AddText(fmt.Sprintf("    %s", summary))
	}

	hdrpara = hdr.AddParagraph()
	hdrpara.Properties().SetAlignment(wml.ST_JcCenter)
	hdrpara.SetStyle("Title")
	run := hdrpara.AddRun()
	run.Properties().SetSize(22)
	run.Properties().SetFontFamily(font_family)
	run.AddText("职 业 健 康 检 查 报 告 书")

	hdrpara = hdr.AddParagraph()

	hdrpara.Properties().SetAlignment(wml.ST_JcLeft)
	hdrpara.Properties().AddTabStop(160*measurement.Millimeter, wml.ST_TabJcCenter, wml.ST_TabTlcNone)
	run = hdrpara.AddRun()
	run.Properties().SetFontFamily(font_family)
	run.AddText(rptinfo.TjReportnum)
	run.AddTab()
	run.Properties().SetFontFamily(font_family)
	run.AddText("第")
	run.Properties().SetFontFamily(font_family)
	run.AddFieldWithFormatting(document.FieldCurrentPage, "", false)
	run.Properties().SetFontFamily(font_family)
	run.AddText("页，共")
	run.Properties().SetFontFamily(font_family)
	run.AddFieldWithFormatting(document.FieldNumberOfPages, "", false)
	run.Properties().SetFontFamily(font_family)
	run.AddText("页")
	para := doc.AddParagraph()
	section := para.Properties().AddSection(wml.ST_SectionMarkNextPage)
	section.SetHeader(hdr, wml.ST_HdrFtrDefault)
	section.SetPageMargins(measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10))

}

func generate_report_tableT1T2(doc *document.Document, tmpret []models.TjCheckallnew, medret *[]models.TjMedexaminfo, pret *[]models.TjPatient, title string) {

	para := doc.AddParagraph()
	para.SetStyle("Title")
	para.Properties().SetKeepWithNext(true)
	run := para.AddRun()
	// run.AddBreak()
	// run.AddBreak()
	run.Properties().SetSize(12)
	run.Properties().SetFontFamily(font_family)
	run.Properties().SetBold(true)
	run.AddText(title)

	vertialalign := wml.ST_VerticalJcCenter
	font_size := measurement.Distance(12)
	para = doc.AddParagraph()
	para.Properties().SetStartIndent(measurement.Distance(measurement.Millimeter * 5))
	table := doc.AddTable()

	w1 := measurement.Distance(measurement.Millimeter * 10)
	w2 := measurement.Distance(measurement.Millimeter * 10)
	w3 := measurement.Distance(measurement.Millimeter * 25)
	w4 := measurement.Distance(measurement.Millimeter * 60)
	w5 := measurement.Distance(measurement.Millimeter * 12)
	// Table 1 & 2, talbe header
	{
		// 4 inches wide
		table.Properties().SetWidthPercent(100)
		borders := table.Properties().Borders()
		// thin borders
		borders.SetAll(wml.ST_BorderSingle, color.Auto, measurement.Zero)

		row := table.AddRow()
		cell := row.AddCell()
		cell.Properties().SetWidth(w2)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run := cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("序号")

		cell = row.AddCell()
		cell.Properties().SetWidth(w2)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		cellPara.SetStyle("Normal")
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("体检编号")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("姓名")
		// cell.AddParagraph().AddRun().AddText("姓名")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("性别")
		// cell.AddParagraph().AddRun().AddText("性别")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		// cell.AddParagraph().AddRun().AddText("年龄")
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("年龄")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("接害工龄")
		// cell.AddParagraph().AddRun().AddText("接害工龄")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("工种")
		// cell.AddParagraph().AddRun().AddText("工种")

		cell = row.AddCell()
		cell.Properties().SetWidth(w5)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("接害因素")
		// cell.AddParagraph().AddRun().AddText("接触职业危害因素名称")

		cell = row.AddCell()
		cell.Properties().SetWidth(w4)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("异常指标")

		cell = row.AddCell()
		cell.Properties().SetWidth(w2)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("结论")

		cell = row.AddCell()
		cell.Properties().SetWidth(w3)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("处理意见")

		cell = row.AddCell()
		cell.Properties().SetWidth(w4)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("医学建议")
	}

	if len(tmpret) <= 0 {
		row := table.AddRow()
		cell := row.AddCell()
		cell.Properties().SetWidth(measurement.Distance(measurement.Millimeter * 230))
		cell.Properties().SetColumnSpan(12)
		row.Properties().SetHeight(measurement.Distance(25), wml.ST_HeightRuleAtLeast)
		// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run := cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("无")
	} else {

		for idx, val := range tmpret {

			medinfo := service.FindMedexaminfo(*medret, val.TjTestid)
			if medinfo == nil {
				continue
			}
			ptinfo := service.FindPatientinfo(*pret, medinfo.TjPid)
			if ptinfo == nil {
				continue
			}
			row := table.AddRow()
			cell := row.AddCell()
			cell.Properties().SetWidth(w2)
			cell.Properties().SetVerticalMerge(wml.ST_MergeRestart)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara := cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run := cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(strconv.Itoa(idx + 1))

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			cell.Properties().SetVerticalMerge(wml.ST_MergeRestart)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjTestid)

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			cell.Properties().SetVerticalMerge(wml.ST_MergeRestart)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(ptinfo.TjPname)

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			cell.Properties().SetVerticalMerge(wml.ST_MergeRestart)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			// run
			if ptinfo.TjPsex == 1 {
				run.AddText("男")
			} else if ptinfo.TjPsex == 2 {
				run.AddText("女")
			} else {
				run.AddText("未知")
			}
			// cell.AddParagraph().AddRun().AddText("性别")

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			cell.Properties().SetVerticalMerge(wml.ST_MergeRestart)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			// cell.AddParagraph().AddRun().AddText("年龄")
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(strconv.Itoa(medinfo.TjAge))

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			cell.Properties().SetVerticalMerge(wml.ST_MergeRestart)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjPoisionage)
			// cell.AddParagraph().AddRun().AddText("接害工龄")

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			cell.Properties().SetVerticalMerge(wml.ST_MergeRestart)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjWorktype)
			// cell.AddParagraph().AddRun().AddText("工种")

			cell = row.AddCell()
			cell.Properties().SetWidth(w5)
			cell.Properties().SetVerticalMerge(wml.ST_MergeRestart)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcLeft)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjPoisionfactor)
			// cell.AddParagraph().AddRun().AddText("接触职业危害因素名称")

			cell = row.AddCell()
			cell.Properties().SetWidth(w4)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcLeft)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			// run.AddText(val.TjOcuabnormal)
			ocuabnormals := strings.Split(val.TjOcuabnormal, "\n")
			for _, ocuabnormal := range ocuabnormals {
				run.AddText(ocuabnormal)
				run.AddBreak()
			}

			cell = row.AddCell()
			cell.Properties().SetWidth(w2)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjOcuconclusion)

			cell = row.AddCell()
			cell.Properties().SetWidth(w3)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcLeft)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjOcuopinion)

			cell = row.AddCell()
			cell.Properties().SetWidth(w4)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcLeft)
			run = cellPara.AddRun() //.AddRun()
			run.Properties().SetFontFamily(font_family)
			run.Properties().SetSize(font_size)
			// run.AddText(val.TjOcusuggestion)
			ocusuggestions := strings.Split(val.TjOcusuggestion, "\n")
			for _, ocusuggestion := range ocusuggestions {
				run.AddText(ocusuggestion)
				run.AddBreak()
			}
			//其它疾病和异常

			//合并的行
			row = table.AddRow()
			cell = row.AddCell()
			cell.Properties().SetVerticalMerge(wml.ST_MergeContinue)
			cell.Properties().SetWidth(w1)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(strconv.Itoa((idx + 1)))

			// row.Properties().SetHeight(measurement.Distance(25), wml.ST_HeightRuleAtLeast)
			cell = row.AddCell()
			cell.Properties().SetVerticalMerge(wml.ST_MergeContinue)
			cell.Properties().SetWidth(w2)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjTestid)

			cell = row.AddCell()
			cell.Properties().SetVerticalMerge(wml.ST_MergeContinue)
			cell.Properties().SetWidth(w1)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(ptinfo.TjPname)

			cell = row.AddCell()
			cell.Properties().SetVerticalMerge(wml.ST_MergeContinue)
			cell.Properties().SetWidth(w1)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			// run
			if ptinfo.TjPsex == 1 {
				run.AddText("男")
			} else if ptinfo.TjPsex == 2 {
				run.AddText("女")
			} else {
				run.AddText("未知")
			}

			cell = row.AddCell()
			cell.Properties().SetVerticalMerge(wml.ST_MergeContinue)
			cell.Properties().SetWidth(w1)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(strconv.Itoa(medinfo.TjAge))

			cell = row.AddCell()
			cell.Properties().SetVerticalMerge(wml.ST_MergeContinue)
			cell.Properties().SetWidth(w1)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjPoisionage)

			cell = row.AddCell()
			cell.Properties().SetVerticalMerge(wml.ST_MergeContinue)
			cell.Properties().SetWidth(w1)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjWorktype)

			cell = row.AddCell()
			cell.Properties().SetVerticalMerge(wml.ST_MergeContinue)
			cell.Properties().SetWidth(w5)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcLeft)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjPoisionfactor)

			//独立的行·
			cell = row.AddCell()
			// cell.Properties().SetWidthPercent(20)
			cell.Properties().SetWidth(w4)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			// run.AddText(val.TjOthabnormal)
			othabnormals := strings.Split(val.TjOthabnormal, "\n")
			for _, othabnormal := range othabnormals {
				run.AddText(othabnormal)
				run.AddBreak()
			}

			cell = row.AddCell()
			cell.Properties().SetWidth(w2)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText("-")
			// run.AddText(val.TjOthconclusion)

			cell = row.AddCell()
			cell.Properties().SetWidth(w3)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjOthopinion)

			cell = row.AddCell()
			cell.Properties().SetWidth(w4)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			// run.AddText(val.TjOthsuggestion)
			othsuggestions := strings.Split(val.TjOthsuggestion, "\n")
			for _, othsuggestion := range othsuggestions {
				run.AddText(othsuggestion)
				run.AddBreak()
			}

		}
	}
}

func generate_report_tableT3(doc *document.Document, tmpret []models.TjCheckallnew, medret *[]models.TjMedexaminfo, pret *[]models.TjPatient, title string) {
	// var tmpret []models.TjCheckallnew

	para := doc.AddParagraph()
	// para.SetStyle("Title")
	// para.Properties().SetKeepWithNext(true)
	run := para.AddRun()
	// run.AddBreak()
	run.Properties().SetSize(12)
	run.Properties().SetBold(true)
	run.Properties().SetFontFamily(font_family)

	run.AddText(title)

	vertialalign := wml.ST_VerticalJcCenter
	font_size := measurement.Distance(12)
	doc.AddParagraph()
	table := doc.AddTable()
	w1 := measurement.Distance(measurement.Millimeter * 10)
	w2 := measurement.Distance(measurement.Millimeter * 20)
	w3 := measurement.Distance(measurement.Millimeter * 60)
	w4 := measurement.Distance(measurement.Millimeter * 10)
	w5 := measurement.Distance(measurement.Millimeter * 15)
	// Table 3
	{
		// 4 inches wide
		table.Properties().SetWidthPercent(100)
		borders := table.Properties().Borders()
		// thin borders
		borders.SetAll(wml.ST_BorderSingle, color.Auto, measurement.Zero)

		row := table.AddRow()

		cell := row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run := cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("序号")

		cell = row.AddCell()
		cell.Properties().SetWidth(w4)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("体检编号")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("姓名")
		// cell.AddParagraph().AddRun().AddText("姓名")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("性别")
		// cell.AddParagraph().AddRun().AddText("性别")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		// cell.AddParagraph().AddRun().AddText("年龄")
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("年龄")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("接害工龄")
		// cell.AddParagraph().AddRun().AddText("接害工龄")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("工种")
		// cell.AddParagraph().AddRun().AddText("工种")

		cell = row.AddCell()
		cell.Properties().SetWidth(w5)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("接害因素")
		// cell.AddParagraph().AddRun().AddText("接触职业危害因素名称")

		cell = row.AddCell()
		cell.Properties().SetWidth(w3)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("异常指标")

		cell = row.AddCell()
		cell.Properties().SetWidth(w2)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("结论")

		cell = row.AddCell()
		cell.Properties().SetWidth(w3)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("医学建议")

	}
	if len(tmpret) <= 0 {
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(25), wml.ST_HeightRuleAtLeast)
		cell := row.AddCell()
		cell.Properties().SetWidth(measurement.Distance(measurement.Millimeter * 230))
		cell.Properties().SetColumnSpan(11)
		// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)

		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("无")
	} else {

		for idx, val := range tmpret {

			medinfo := service.FindMedexaminfo(*medret, val.TjTestid)
			if medinfo == nil {
				continue
			}
			ptinfo := service.FindPatientinfo(*pret, medinfo.TjPid)
			if ptinfo == nil {
				continue
			}
			row := table.AddRow()
			row.Properties().SetHeight(measurement.Distance(25), wml.ST_HeightRuleAtLeast)

			cell := row.AddCell()
			cell.Properties().SetWidth(w1)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara := cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(strconv.Itoa(idx + 1))

			cell = row.AddCell()
			cell.Properties().SetWidth(w4)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjTestid)

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(ptinfo.TjPname)
			// cell.AddParagraph().AddRun().AddText("姓名")

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			// run
			if ptinfo.TjPsex == 1 {
				run.AddText("男")
			} else if ptinfo.TjPsex == 2 {
				run.AddText("女")
			} else {
				run.AddText("未知")
			}
			// cell.AddParagraph().AddRun().AddText("性别")

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			// cell.AddParagraph().AddRun().AddText("年龄")
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(strconv.Itoa(medinfo.TjAge))

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjPoisionage)
			// cell.AddParagraph().AddRun().AddText("接害工龄")

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjWorktype)
			// cell.AddParagraph().AddRun().AddText("工种")

			cell = row.AddCell()
			cell.Properties().SetWidth(w5)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcLeft)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjPoisionfactor)
			// cell.AddParagraph().AddRun().AddText("接触职业危害因素名称")
			{
				cello := row.AddCell()
				cello.Properties().SetWidth(w3)
				cello.Properties().SetVerticalAlignment(vertialalign)
				celloPara := cello.AddParagraph()
				celloPara.Properties().SetAlignment(wml.ST_JcLeft)
				runo := celloPara.AddRun()
				runo.Properties().SetSize(font_size)
				runo.Properties().SetFontFamily(font_family)
				othabnormals := strings.Split(val.TjOthabnormal, "\n")
				for _, othabnormal := range othabnormals {
					runo.AddText(othabnormal)
					runo.AddBreak()
				}
			}
			cell = row.AddCell()
			cell.Properties().SetWidth(w2)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellpara := cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellpara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjOthconclusion)

			cell = row.AddCell()
			cell.Properties().SetWidth(w3)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellpara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellpara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			// run.AddText(val.TjOthsuggestion)
			othsuggestions := strings.Split(val.TjOthsuggestion, "\n")
			for _, othsuggestion := range othsuggestions {
				run.AddText(othsuggestion)
				run.AddBreak()
			}
		}
	}
}

func generate_report_tableT4(doc *document.Document, tmpret []models.TjCheckallnew, medret *[]models.TjMedexaminfo, pret *[]models.TjPatient, title string) {
	// var tmpret []models.TjCheckallnew

	para := doc.AddParagraph()
	para.SetStyle("Title")

	run := para.AddRun()
	// run.AddBreak()
	run.Properties().SetSize(12)
	run.Properties().SetBold(true)
	run.Properties().SetFontFamily(font_family)

	run.AddText(title)

	vertialalign := wml.ST_VerticalJcCenter
	font_size := measurement.Distance(12)
	doc.AddParagraph()
	table := doc.AddTable()
	w1 := measurement.Distance(measurement.Millimeter * 10)
	w2 := measurement.Distance(measurement.Millimeter * 23)
	w3 := measurement.Distance(measurement.Millimeter * 60)
	w4 := measurement.Distance(measurement.Millimeter * 15)
	// Table 3
	{
		// 4 inches wide
		table.Properties().SetWidthPercent(100)
		borders := table.Properties().Borders()
		// thin borders
		borders.SetAll(wml.ST_BorderSingle, color.Auto, measurement.Zero)

		row := table.AddRow()

		cell := row.AddCell()
		cell.Properties().SetWidth(w4)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run := cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("体检编号")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("姓名")
		// cell.AddParagraph().AddRun().AddText("姓名")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("性别")
		// cell.AddParagraph().AddRun().AddText("性别")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		// cell.AddParagraph().AddRun().AddText("年龄")
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("年龄")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("接害工龄")
		// cell.AddParagraph().AddRun().AddText("接害工龄")

		cell = row.AddCell()
		cell.Properties().SetWidth(w1)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("工种")
		// cell.AddParagraph().AddRun().AddText("工种")

		cell = row.AddCell()
		cell.Properties().SetWidth(w2)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("接害因素")
		// cell.AddParagraph().AddRun().AddText("接触职业危害因素名称")

		cell = row.AddCell()
		cell.Properties().SetWidth(w3)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("异常指标")

		cell = row.AddCell()
		cell.Properties().SetWidth(w2)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("结论")

		cell = row.AddCell()
		cell.Properties().SetWidth(w3)
		cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("医学建议")

	}
	if len(tmpret) <= 0 {
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(25), wml.ST_HeightRuleAtLeast)
		cell := row.AddCell()
		cell.Properties().SetWidth(measurement.Distance(measurement.Millimeter * 230))
		cell.Properties().SetColumnSpan(10)
		// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcCenter)

		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("无")
	} else {

		for _, val := range tmpret {

			medinfo := service.FindMedexaminfo(*medret, val.TjTestid)
			if medinfo == nil {
				continue
			}
			ptinfo := service.FindPatientinfo(*pret, medinfo.TjPid)
			if ptinfo == nil {
				continue
			}

			row := table.AddRow()
			// row.Properties().SetHeight(measurement.Distance(25), wml.ST_HeightRuleAtLeast)
			cell := row.AddCell()
			cell.Properties().SetWidth(w4)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara := cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run := cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjTestid)

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(ptinfo.TjPname)
			// cell.AddParagraph().AddRun().AddText("姓名")

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)

			if ptinfo.TjPsex == 1 {
				run.AddText("男")
			} else if ptinfo.TjPsex == 2 {
				run.AddText("女")
			} else {
				run.AddText("未知")
			}
			// cell.AddParagraph().AddRun().AddText("性别")

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			// cell.AddParagraph().AddRun().AddText("年龄")
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(strconv.Itoa(medinfo.TjAge))

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjPoisionage)
			// cell.AddParagraph().AddRun().AddText("接害工龄")

			cell = row.AddCell()
			cell.Properties().SetWidth(w1)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjWorktype)
			// cell.AddParagraph().AddRun().AddText("工种")

			cell = row.AddCell()
			cell.Properties().SetWidth(w2)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			cellPara = cell.AddParagraph()
			cellPara.Properties().SetAlignment(wml.ST_JcCenter)
			run = cellPara.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(medinfo.TjPoisionfactor)
			// cell.AddParagraph().AddRun().AddText("接触职业危害因素名称")

			cell = row.AddCell()
			cell.Properties().SetWidth(w3)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjOthabnormal)

			cell = row.AddCell()
			cell.Properties().SetWidth(w2)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			run = cell.AddParagraph().AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjOthconclusion)

			cell = row.AddCell()
			cell.Properties().SetWidth(w3)
			// cell.Properties().SetShading(wml.ST_ShdSolid, color.LightGray, color.Auto)
			cell.Properties().SetVerticalAlignment(vertialalign)
			run = cell.AddParagraph().AddRun() //.AddRun()
			run.Properties().SetSize(font_size)
			run.Properties().SetFontFamily(font_family)
			run.AddText(val.TjOthsuggestion)
		}
	}
	run.AddPageBreak()
}

func generate_sign_page(doc *document.Document, rptinfo *models.TjCorpoccureport, dicts *[]models.SsDictionary, pagestyle int32, customer string) {
	docpara := doc.AddParagraph()
	// hdr := doc.AddHeader()
	docpara.AddRun().AddBreak()
	// docpara.AddRun().AddBreak()
	if pagestyle == 1 {
		docpara.Properties().SetStartIndent(measurement.Distance(1 * measurement.Inch))
	}
	if customer != app.Wzdams {
		table := doc.AddTable()
		table.Properties().SetWidthPercent(98)
		table.Properties().SetAlignment(wml.ST_JcTableCenter)
		borders := table.Properties().Borders()
		borders.SetAll(wml.ST_BorderSingle, color.Auto, 1*measurement.Point)

		// row1 := table.AddRow()
		// row1.Properties().SetHeight(measurement.Distance(measurement.Millimeter*2), wml.ST_HeightRuleExact)
		// row1.AddCell().AddParagraph().AddRun().AddText("")

		row := table.AddRow()
		row.Properties().SetHeight(0.3*measurement.Point, wml.ST_HeightRuleExact)

		cell := row.AddCell()
		cell.Properties().SetVerticalAlignment(wml.ST_VerticalJcCenter)

		para := cell.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcCenter)
		run := para.AddRun()
		run.AddText("")
	}
	doc.AddParagraph().AddRun().AddText("")

	table := doc.AddTable()
	// table.Properties().SetWidthPercent(100)
	table.Properties().SetWidth(measurement.Millimeter * 200)
	table.Properties().SetAlignment(wml.ST_JcTableCenter)

	labelwidth := measurement.Distance(measurement.Millimeter * 40)
	signwidth := measurement.Distance(measurement.Millimeter * 60)
	labelrightalign := wml.ST_JcRight
	labelleftalign := wml.ST_JcLeft
	vertialalign := wml.ST_VerticalJcCenter
	//row 1
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(30), wml.ST_HeightRuleExact)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(labelrightalign)
		run := cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("主检医师：")

		cell = row.AddCell()
		cell.Properties().SetWidth(signwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcLeft)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)

		{
			dict := service.FindDictionary(dicts, 19, 36) //企业总表的主检医师
			if dict == nil {
				run.AddText("")
			} else {
				if dict.SsShort == "1" { //打印签名
					//先根据总检医生ID，获取总检医生信息，查看总检医生的签名
					var sign_info = dict.SsName
					if rptinfo.TjCreator != "" {
						var staffModel = new(models.StaffadminModel)
						staff, err := staffModel.FindByID(rptinfo.TjCreator)
						if err == nil {
							if staff.TjEsign != "" {
								sign_info = staff.TjEsign
							}
						}
					}
					img1, err := common.ImageFromFile("./sign/" + sign_info)
					if err == nil {
						img1ref, err := doc.AddImage(img1)
						if err != nil {
							logger.Log.Errorf("unable to add image to document: %s", err)
							run.AddText("")
						} else {
							run = cellPara.AddRun()
							inl, err := run.AddDrawingInline(img1ref)
							if err != nil {
								logger.Log.Errorf("unable to add inline image: %s", err)
								run.AddText("")
							} else {
								inl.SetSize(20*measurement.Millimeter, 10*measurement.Millimeter)
								// inl.SetTextWrapNone()
							}
						}
					} else {
						logger.Log.Errorf("unable to create image: %s", err)
						run.AddText("")
					}

				} else if dict.SsShort == "2" {
					var sign_info = dict.SsName
					if rptinfo.TjCreator != "" {
						var staffModel = new(models.StaffadminModel)
						staff, err := staffModel.FindByID(rptinfo.TjCreator)
						if err == nil {
							sign_info = staff.TjStaffname
						}
					}
					run.AddText(sign_info)
				} else {
					run.AddText("")
				}
			}
		}
		cell = row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(labelrightalign)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("审 核 人：")

		cell = row.AddCell()
		cell.Properties().SetWidth(signwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcLeft)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)

		{
			dict := service.FindDictionary(dicts, 19, 40) //企业总表的审核医师
			if dict == nil {
				run.AddText("")
			} else {
				if dict.SsShort == "1" {
					img1, err := common.ImageFromFile("./sign/" + dict.SsName)
					if err == nil {
						img1ref, err := doc.AddImage(img1)
						if err != nil {
							logger.Log.Errorf("unable to add image to document: %s", err)
							run.AddText("")
						} else {
							inl, err := cellPara.AddRun().AddDrawingInline(img1ref)
							if err != nil {
								logger.Log.Errorf("unable to add inline image: %s", err)
								run.AddText("")
							} else {
								inl.SetSize(20*measurement.Millimeter, 10*measurement.Millimeter)
								// inl.SetTextWrapNone()
								// inl.SetOrigin(wml.WdST_RelFromHColumn, wml.WdST_RelFromVParagraph)
								// inl.SetXOffset(20 * measurement.Character)
								// inl.SetTextWrapSquare(wml.WdST_WrapTextBothSides)
							}
						}
					} else {
						logger.Log.Errorf("unable to create image: %s", err)
						run.AddText("")
					}

				} else if dict.SsShort == "2" {
					run.AddText(dict.SsName)
				} else {
					run.AddText("")
				}
			}
		}
	}
	//empty row
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(10), wml.ST_HeightRuleExact)
		cell := row.AddCell()
		cell.Properties().SetColumnSpan(4)
		cell.AddParagraph().AddRun().AddText("")
	}
	//row3
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(30), wml.ST_HeightRuleExact)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(labelrightalign)
		run := cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("批 准 人：")

		cell = row.AddCell()
		cell.Properties().SetWidth(signwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcLeft)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		pizhundate := time.Now().Format("2006年01月02日")
		if rptinfo.TjCreatedate > 0 {
			pizhundate = time.Unix(rptinfo.TjCreatedate, 0).Format("2006年01月02日")
		}
		{
			dict := service.FindDictionary(dicts, 19, 21) //企业总表的批准
			if dict == nil {
				run.AddText("")
				pizhundate = ""
			} else {
				if dict.SsShort == "1" {
					img1, err := common.ImageFromFile("./sign/" + dict.SsName)
					if err == nil {
						img1ref, err := doc.AddImage(img1)
						if err != nil {
							logger.Log.Errorf("unable to add image to document: %s", err)
							run.AddText("")
						} else {
							inl, err := cellPara.AddRun().AddDrawingInline(img1ref)
							if err != nil {
								logger.Log.Errorf("unable to add inline image: %s", err)
								run.AddText("")
							} else {
								inl.SetSize(20*measurement.Millimeter, 10*measurement.Millimeter)
								// inl.SetTextWrapNone()
							}
						}
					} else {
						logger.Log.Errorf("unable to create image: %s", err)
						run.AddText("")
					}

				} else if dict.SsShort == "2" {
					run.AddText(dict.SsName)
				} else {
					run.AddText("")
					pizhundate = ""
				}
			}
		}

		cell = row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(labelrightalign)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("批准日期：")

		if customer == app.Wzdams {
			pizhundate = ""
		}
		cell = row.AddCell()
		cell.Properties().SetWidth(signwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(wml.ST_JcLeft)
		// pizhundate := time.Now().Format("2006年01月02日")
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText(pizhundate)

	}
	//empty row
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(10), wml.ST_HeightRuleExact)
		cell := row.AddCell()
		cell.Properties().SetColumnSpan(4)
		cell.AddParagraph().AddRun().AddText("")
	}
	//row 4
	{
		row := table.AddRow()
		row.Properties().SetHeight(measurement.Distance(30), wml.ST_HeightRuleExact)

		cell := row.AddCell()
		cell.Properties().SetWidth(labelwidth)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cellPara := cell.AddParagraph()
		cellPara.Properties().SetAlignment(labelrightalign)
		run := cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		run.AddText("体检单位(盖章)：")

		cell = row.AddCell()
		cell.Properties().SetColumnSpan(3)
		cell.Properties().SetVerticalAlignment(vertialalign)
		cell.Properties().SetWidth(measurement.Distance(measurement.Millimeter * 130))
		cellPara = cell.AddParagraph()
		cellPara.Properties().SetAlignment(labelleftalign)
		run = cellPara.AddRun()
		run.Properties().SetFontFamily(font_family)
		dict := service.FindDictionary(dicts, 10, 1)
		run.AddText(dict.SsName)
		{
			dict := service.FindDictionary(dicts, 19, 33) //职业健康体检章
			if dict == nil {
				run.AddText("")
			} else {
				if dict.SsShort == "1" {
					img1, err := common.ImageFromFile("./sign/" + dict.SsName)
					if err == nil {
						img1ref, err := doc.AddImage(img1)
						if err != nil {
							logger.Log.Errorf("unable to add image to document: %s", err)
							run.AddText("")
						} else {
							inl, err := doc.AddParagraph().AddRun().AddDrawingAnchored(img1ref)
							if err != nil {
								logger.Log.Errorf("unable to add inline image: %s", err)
								run.AddText("")
							} else {
								inl.SetSize(50*measurement.Millimeter, 40*measurement.Millimeter)
								inl.SetOrigin(wml.WdST_RelFromHColumn, wml.WdST_RelFromVParagraph)
								if pagestyle == 1 {
									inl.SetXOffset(50 * measurement.Character)
								} else {
									inl.SetXOffset(25 * measurement.Character)
								}
								inl.SetTextWrapSquare(wml.WdST_WrapTextBothSides)
							}
						}
					} else {
						logger.Log.Errorf("unable to create image: %s", err)
						run.AddText("")
					}
				} else {
					run.AddText("")
				}
			}
		}
	}
	// {
	// 	dict := service.FindDictionary(dicts, 10, 1)
	// 	para := doc.AddParagraph()
	// 	run := para.AddRun()
	// 	run.AddBreak()
	// 	run.Properties().SetFontFamily(font_family)
	// 	run.AddText("体检单位(盖章)：" + dict.SsName)
	// 	{
	// 		dict := service.FindDictionary(dicts, 19, 33) //职业健康体检章
	// 		if dict == nil {
	// 			run.AddText("")
	// 		} else {
	// 			if dict.SsShort == "1" {
	// 				img1, err := common.ImageFromFile("./sign/" + dict.SsName)
	// 				if err == nil {
	// 					img1ref, err := doc.AddImage(img1)
	// 					if err != nil {
	// 						logger.Log.Errorf("unable to add image to document: %s", err)
	// 						run.AddText("")
	// 					} else {
	// 						inl, err := doc.AddParagraph().AddRun().AddDrawingAnchored(img1ref)
	// 						if err != nil {
	// 							logger.Log.Errorf("unable to add inline image: %s", err)
	// 							run.AddText("")
	// 						} else {
	// 							inl.SetSize(50*measurement.Millimeter, 40*measurement.Millimeter)
	// 							inl.SetOrigin(wml.WdST_RelFromHColumn, wml.WdST_RelFromVParagraph)
	// 							inl.SetXOffset(20 * measurement.Character)
	// 							inl.SetTextWrapSquare(wml.WdST_WrapTextBothSides)
	// 						}
	// 					}
	// 				} else {
	// 					logger.Log.Errorf("unable to create image: %s", err)
	// 					run.AddText("")
	// 				}
	// 			} else {
	// 				run.AddText("")
	// 			}
	// 		}
	// 	}
	// }

}

func generate_report_back(doc *document.Document, dicts *[]models.SsDictionary) {

	// docpara := doc.AddParagraph()
	// hdr := doc.AddHeader()
	// para := doc.AddParagraph()
	// section := para.Properties().AddSection(wml.ST_SectionMarkEvenPage)
	// section.SetHeader(hdr, wml.ST_HdrFtrDefault)

	linespacing := measurement.Distance(measurement.Millimeter * 8)
	{
		para := doc.AddParagraph()
		para.SetStyle("Title")
		para.Properties().SetAlignment(wml.ST_JcCenter)

		run := para.AddRun()
		run.AddBreak()
		// run.AddBreak()
		run.Properties().SetSize(30)
		run.Properties().SetFontFamily(font_family)
		run.AddText("职业健康检查报告书说明")
		run.AddBreak()
		run.AddBreak()
	}
	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		para.Properties().Spacing().SetLineSpacing(linespacing, wml.ST_LineSpacingRuleExact)
		run := para.AddRun()
		run.Properties().SetSize(12)
		run.Properties().SetFontFamily(font_family)
		run.AddText("一、对本报告书有异议的，请于收到之日起十五日内向本单位提出。")
	}
	{
		para := doc.AddParagraph()
		para.Properties().Spacing().SetLineSpacing(linespacing, wml.ST_LineSpacingRuleExact)
		para.Properties().SetAlignment(wml.ST_JcLeft)
		run := para.AddRun()
		run.Properties().SetSize(12)
		run.Properties().SetFontFamily(font_family)
		run.AddText("二、本报告书无主检医师、审核人及批准人签字无效，本报告书无本单位盖章无效。")
	}
	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		para.Properties().Spacing().SetLineSpacing(linespacing, wml.ST_LineSpacingRuleExact)
		run := para.AddRun()
		run.Properties().SetSize(12)
		run.Properties().SetFontFamily(font_family)
		run.AddText("三、本报告书涂改无效。")
	}
	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		para.Properties().Spacing().SetLineSpacing(linespacing, wml.ST_LineSpacingRuleExact)
		run := para.AddRun()
		run.Properties().SetSize(12)
		run.Properties().SetFontFamily(font_family)
		run.AddText("四、本报告书不得部分复制，不得作广告宣传。")
	}
	{
		para := doc.AddParagraph()
		para.Properties().Spacing().SetLineSpacing(linespacing, wml.ST_LineSpacingRuleExact)
		para.Properties().SetAlignment(wml.ST_JcLeft)
		run := para.AddRun()
		run.Properties().SetSize(12)
		run.Properties().SetFontFamily(font_family)
		run.AddText("五、本报告书一式三份（用人单位和用人单位所在地卫生行政部门各一份，职业健康检查机构存档一份）。")
	}
	{
		idx := slices.IndexFunc(*dicts, func(c models.SsDictionary) bool { return c.SsTypeid == 19 && c.SsPid == 13 })
		if idx > 0 {
			add_dict := (*dicts)[idx]
			if add_dict.SsShort == "1" {
				para := doc.AddParagraph()
				para.Properties().SetAlignment(wml.ST_JcLeft)
				run := para.AddRun()
				run.AddBreak()
				run.AddBreak()
				run.Properties().SetSize(10)
				run.Properties().SetFontFamily(font_family)
				run.AddText("      " + add_dict.SsName)
				run.AddBreak()
			} else {
				doc.AddParagraph()
				doc.AddParagraph()
				doc.AddParagraph()
			}
		} else {
			doc.AddParagraph()
			doc.AddParagraph()
			doc.AddParagraph()
		}
	}

	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		run := para.AddRun()
		run.Properties().SetSize(14)
		run.Properties().SetFontFamily(font_family)
		run.AddBreak()
		run.AddText("本单位联系方式：")
	}
	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		run := para.AddRun()
		run.Properties().SetSize(10)
		run.Properties().SetFontFamily(font_family)
		run.AddBreak()
		run.AddText("职业健康检查机构名称：" + service.FindDictionary(dicts, 10, 1).SsName)
	}
	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		run := para.AddRun()
		run.Properties().SetSize(10)
		run.Properties().SetFontFamily(font_family)
		run.AddBreak()
		run.AddText("职业健康检查机构批准证书号：" + service.FindDictionary(dicts, 10, 15).SsName)
	}
	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		run := para.AddRun()
		run.Properties().SetSize(10)
		run.Properties().SetFontFamily(font_family)
		run.AddBreak()
		run.AddText("地址：" + service.FindDictionary(dicts, 10, 5).SsName)
	}
	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		run := para.AddRun()
		run.Properties().SetSize(10)
		run.Properties().SetFontFamily(font_family)
		run.AddBreak()
		run.AddText("邮编：" + service.FindDictionary(dicts, 10, 11).SsName)
	}
	{
		para := doc.AddParagraph()
		para.Properties().SetAlignment(wml.ST_JcLeft)
		run := para.AddRun()
		run.Properties().SetSize(10)
		run.Properties().SetFontFamily(font_family)
		run.AddBreak()
		run.AddText("联系电话：" + service.FindDictionary(dicts, 10, 2).SsName)
	}

	qrcode_img := "./images/qrcode.png"
	if _, err := os.Stat(qrcode_img); err == nil {
		// path/to/whatever exists
		img1, err := common.ImageFromFile(qrcode_img)
		if err != nil {
			logger.Log.Errorf("unable to create image: %s", err)
		}
		img1ref, err := doc.AddImage(img1)
		if err != nil {
			logger.Log.Errorf("unable to add image to document: %s", err)
		}
		para := doc.AddParagraph()
		anchored, err := para.AddRun().AddDrawingAnchored(img1ref)
		if err != nil {
			logger.Log.Errorf("unable to add anchored image: %s", err)
		}
		anchored.SetName("QrCode")
		anchored.SetSize(1.5*measurement.Inch, 1.5*measurement.Inch)
		anchored.SetOrigin(wml.WdST_RelFromHPage, wml.WdST_RelFromVTopMargin)
		anchored.SetHAlignment(wml.WdST_AlignHCenter)
		anchored.SetYOffset(8 * measurement.Inch)
		anchored.SetXOffset(5 * measurement.Inch)
		anchored.SetTextWrapSquare(wml.WdST_WrapTextLeft)
	}

	// para := doc.AddParagraph()
	// section := docpara.Properties().AddSection(wml.ST_SectionMarkContinuous)
	// section.SetHeader(hdr, wml.ST_HdrFtrDefault)
	// section.SetPageMargins(measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10), measurement.Distance(measurement.Millimeter*10))

}

func add_report_header(doc *document.Document, rptinfo *models.TjCorpoccureport, pagestyle int32) {
	hdr := doc.AddHeader()

	para := hdr.AddParagraph()
	// para.Properties().AddTabStop(2.5*measurement.Inch, wml.ST_TabJcCenter, wml.ST_TabTlcNone)
	para.Properties().SetAlignment(wml.ST_JcCenter)
	para.SetStyle("Title")
	run := para.AddRun()
	run.Properties().SetSize(22)
	run.Properties().SetFontFamily(font_family)
	run.AddText("职 业 健 康 检 查 报 告 书")
	// run.AddBreak()

	para = hdr.AddParagraph()

	para.Properties().Spacing().SetLineSpacing(24*measurement.Point, wml.ST_LineSpacingRuleAuto)
	para.Properties().SetAlignment(wml.ST_JcLeft)
	if pagestyle == 1 {
		para.Properties().AddTabStop(250*measurement.Millimeter, wml.ST_TabJcCenter, wml.ST_TabTlcNone)
	} else {
		para.Properties().AddTabStop(170*measurement.Millimeter, wml.ST_TabJcCenter, wml.ST_TabTlcNone)
	}
	run = para.AddRun()
	// run.Properties().SetUnderline(wml.ST_UnderlineThick, color.Black)
	run.AddText(rptinfo.TjReportnum)
	run.Properties().SetFontFamily(font_family)

	run.AddTab()
	// run.AddText("             ")
	// run.Properties()
	// run = para.AddRun()
	// run.Properties().
	run.AddText("第")
	run.Properties().SetFontFamily(font_family)
	run.AddFieldWithFormatting(document.FieldCurrentPage, "", false)

	run.Properties().SetFontFamily(font_family)
	run.AddText("页，共")
	run.Properties().SetFontFamily(font_family)
	run.AddFieldWithFormatting(document.FieldNumberOfPages, "", false)

	run.Properties().SetFontFamily(font_family)
	run.AddText("页")
	// run.AddBreak()

	// para = hdr.AddParagraph()
	// para.Properties().Spacing().SetLineSpacing(measurement.Distance(1*measurement.Millimeter), wml.ST_LineSpacingRuleExact)
	// run = para.AddRun()
	// run.Properties().SetUnderline(wml.ST_UnderlineSingle, color.Black)
	// run.AddText("a ")

	doc.BodySection().SetHeader(hdr, wml.ST_HdrFtrDefault)
}
