package extend

import (
	"database/sql"
	"errors"
	"fmt"
	"nodip_ego/api/extend/exmodels"
	"time"
	"utility/logger"
)

var lisretstr = "select tjbh, brxm, xmxh, xmmc, xmdw, xmjg, gdbj, ckdz, ckgz, ckfw, jyys, bgrq, bgys,sfyc from v_tj_lisresult where tjbh = '%s'"
var sqlxmxx = "select itemid, chinesename, englishab, unit from v_lis_xmxx"
var pacsretstr = "select tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq,sfyc from v_tj_pacsresult where tjbh = '%s'"
var pacsretstr_no_sfyc = "select tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq from v_tj_pacsresult where tjbh = '%s'"

const (
	// DbTypeNone ... 0
	DbTypeNone int = 0
	// DbTypeORACLE ... 1
	DbTypeORACLE int = 1
	// DbTypeMSSQL ... 2
	DbTypeMSSQL int = 2
	// MySQL
	DbTypeMySQL int = 3
)

func requestxmxx(db *sql.DB) (*[]exmodels.Xmxx, error) {

	if db == nil {
		return nil, errors.New("无效数据库连接")
	}

	// sqlstr := fmt.Sprintf(lisretstr, tjbh)
	fmt.Println("执行的sql脚本：" + sqlxmxx)
	logger.Log.Infof("执行的sql脚本：" + sqlxmxx)

	rows, err := db.Query(sqlxmxx)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v", err)
		return nil, err
	}
	defer rows.Close()

	cols, err := rows.Columns()
	if err != nil {
		logger.Log.Errorf("错误信息：%+v", err)
		return nil, err
	}
	if cols == nil {
		return nil, errors.New("no columns")
	}

	vals := make([]interface{}, len(cols))
	for i := 0; i < len(cols); i++ {
		vals[i] = new(interface{})
	}

	rets := make([]exmodels.Xmxx, 0)

	for rows.Next() {
		err = rows.Scan(vals...)
		if err != nil {
			logger.Log.Errorf("错误信息：%+v", err)
			fmt.Println(err)
			continue
		}

		var ret exmodels.Xmxx
		//itemid, chinesename, englishab, unit
		ret.Itemid = getValue(vals[0].(*interface{}))
		ret.Chinesename = getValue(vals[1].(*interface{})) //(*vals[1].(*interface{})).(string)
		ret.Englishab = getValue(vals[2].(*interface{}))
		ret.Unit = getValue(vals[3].(*interface{}))

		rets = append(rets, ret)

	}
	// fmt.Printf("lis结果:%+v\n", lisrets)
	return &rets, nil
}

func getValue(pval *interface{}) string {
	var ret string
	switch v := (*pval).(type) {
	case nil:
		ret = ""
	case bool:
		if v {
			ret = "1"
		} else {
			ret = "0"
		}
	case []byte:
		ret = string(v)
	case time.Time:
		ret = v.Format("2006-01-02 15:04:05.999")
	case int64:
		ret = fmt.Sprintf("%d", v)
	case int:
		ret = fmt.Sprintf("%d", v)
	default:
		ret = v.(string)
	}

	return ret
}

func printValue(pval *interface{}) {
	switch v := (*pval).(type) {
	case nil:
		fmt.Print("NULL")
	case bool:
		if v {
			fmt.Print("1")
		} else {
			fmt.Print("0")
		}
	case []byte:
		fmt.Print(string(v))
	case time.Time:
		fmt.Print(v.Format("2006-01-02 15:04:05.999"))
	default:
		fmt.Print(v)
	}
}
