package extend

import (
	"database/sql"
	"datacontroller/models"
	"errors"
	"fmt"
	"nodip_ego/api/app"
	"nodip_ego/api/extend/exmodels"
	"strconv"
	"utility/logger"

	_ "github.com/denisenkom/go-mssqldb"
	_ "github.com/sijms/go-ora/v2"
)

var pacsmodel = new(models.VTjPacsresultModel)

// RequestPacsXmxx ...
func RequestPacsXmxx(pacskey string) (*[]exmodels.Xmxx, error) {

	// fmt.Println("开始获取pacs得项目信息")
	if pacskey == "" {
		pacskey = "default"
	}
	dbtype := app.Configurations.Extpacs[pacskey].Dbtype
	var connString string
	var db *sql.DB
	var err error
	username := app.Configurations.Extpacs[pacskey].Username
	password := app.Configurations.Extpacs[pacskey].Password
	dbserver := app.Configurations.Extpacs[pacskey].Dbserver
	dbport := app.Configurations.Extpacs[pacskey].Dbport
	servicename := app.Configurations.Extpacs[pacskey].Servicename
	if dbtype == DbTypeORACLE {
		// connString = fmt.Sprintf("user=%s password=%s connectString=%s:%d/%s ", username, password, dbserver, dbport, servicename)
		connString = fmt.Sprintf("oracle://%s:%s@%s:%d/%s", username, password, dbserver, dbport, servicename)

		db, err = sql.Open("oracle", connString)
		if err != nil {
			logger.Log.Errorf("错误信息：%+v", err)
			return nil, err
		}
	} else if dbtype == DbTypeMSSQL {
		connString = fmt.Sprintf("server=%s;database=%s;user id=%s;password=%s;port=%d;encrypt=disable", dbserver, servicename, username, password, dbport)
		db, err = sql.Open("mssql", connString)
		if err != nil {
			logger.Log.Errorf("错误信息：%+v", err)
			return nil, err
		}
	} else if dbtype == DbTypeMySQL {
		connString = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", username, password, dbserver, dbport, servicename)
		db, err = sql.Open("mysql", connString)
		if err != nil {
			return nil, err
		}
		logger.Log.Infof("Lis 连接成功")
	} else {
		return nil, errors.New("Extpacs配置信息不对（无效数据库类型)")
	}
	defer db.Close()
	ret, err := requestxmxx(db)
	return ret, err
}

// RequestPacsresults ...
func RequestPacsresults(tjbh, pacskey string) (*[]models.VTjPacsresult, error) {

	// fmt.Println("开始获取pacs得结果信息信息")
	if pacskey == "" {
		pacskey = "default"
	}
	dbtype := app.Configurations.Extpacs[pacskey].Dbtype
	var connString string
	var db *sql.DB
	var err error
	username := app.Configurations.Extpacs[pacskey].Username
	password := app.Configurations.Extpacs[pacskey].Password
	dbserver := app.Configurations.Extpacs[pacskey].Dbserver
	dbport := app.Configurations.Extpacs[pacskey].Dbport
	servicename := app.Configurations.Extpacs[pacskey].Servicename
	if dbtype == DbTypeORACLE {
		// connString = fmt.Sprintf("user=%s password=%s connectString=%s:%d/%s ", username, password, dbserver, dbport, servicename)
		connString = fmt.Sprintf("oracle://%s:%s@%s:%d/%s", username, password, dbserver, dbport, servicename)
		db, err = sql.Open("oracle", connString)
		if err != nil {
			logger.Log.Errorf("错误信息：%+v", err)
			return nil, err
		}
	} else if dbtype == DbTypeMSSQL {
		connString = fmt.Sprintf("server=%s;database=%s;user id=%s;password=%s;port=%d;encrypt=disable", dbserver, servicename, username, password, dbport)
		db, err = sql.Open("mssql", connString)
		if err != nil {
			logger.Log.Errorf("错误信息：%+v", err)
			return nil, err
		}
	} else if dbtype == DbTypeMySQL {
		connString = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", username, password, dbserver, dbport, servicename)
		db, err = sql.Open("mysql", connString)
		if err != nil {
			return nil, err
		}
	} else {
		return nil, errors.New("Extpacs配置信息不对（无效数据库类型)")
	}
	defer db.Close()
	ret, err := requestpacsresults(tjbh, db)
	if err == nil {
		pacsmodel.InsertPacsResults(ret)
	}
	return ret, err
}

func requestpacsresults(tjbh string, db *sql.DB) (*[]models.VTjPacsresult, error) {

	if db == nil {
		return nil, errors.New("无效数据库连接")
	}

	sqlstr := ""

	if app.Configurations.Application.Pacsversion == "v2" {
		sqlstr = fmt.Sprintf(pacsretstr, tjbh)
	} else {
		sqlstr = fmt.Sprintf(pacsretstr_no_sfyc, tjbh)
	}
	// fmt.Println("执行的sql脚本：" + sqlstr)
	logger.Log.Infof("执行的sql脚本：%s", sqlstr)
	rows, err := db.Query(sqlstr)
	if err != nil {
		// fmt.Printf("执行sql脚本错误，错误信息：%s\n", err.Error())
		logger.Log.Infof("执行sql脚本错误，错误信息：%s", err.Error())
		return nil, err
	}
	defer rows.Close()

	cols, err := rows.Columns()
	if err != nil {
		logger.Log.Errorf("错误信息：%+v", err)
		return nil, err
	}
	if cols == nil {
		return nil, errors.New("no columns")
	}

	vals := make([]interface{}, len(cols))
	for i := 0; i < len(cols); i++ {
		vals[i] = new(interface{})
	}

	rets := make([]models.VTjPacsresult, 0)

	existed := false

	for rows.Next() {
		err = rows.Scan(vals...)
		if err != nil {
			// fmt.Println(err)
			logger.Log.Errorf("scan error:%s", err.Error())
			continue
		}
		existed = false
		var ret models.VTjPacsresult
		// tjbh,brxm,jclx,jcxm,jcmc,imagesight,imagediagnosis,jcys,sxys,bgys,bgrq
		ret.Tjbh = getValue(vals[0].(*interface{}))
		ret.Brxm = getValue(vals[1].(*interface{})) //(*vals[1].(*interface{})).(string)
		ret.Jclx = getValue(vals[2].(*interface{}))
		ret.Jcxm = getValue(vals[3].(*interface{}))
		ret.Jcmc = getValue(vals[4].(*interface{}))
		ret.Imagediagnosis = getValue(vals[6].(*interface{}))

		val := getValue(vals[5].(*interface{})) //检查所见
		if val == "" {
			ret.Imagesight = ret.Imagediagnosis
		} else {
			ret.Imagesight = val
		}
		// ret.Imagesight = getValue(vals[5].(*interface{}))
		ret.Jcys = getValue(vals[7].(*interface{}))
		ret.Sxys = getValue(vals[8].(*interface{}))
		ret.Bgys = getValue(vals[9].(*interface{}))
		ret.Bgrq = getValue(vals[10].(*interface{}))
		ret.Sfyc = 0
		if app.Configurations.Application.Pacsversion == "v2" {
			ret.Sfyc, err = strconv.Atoi(getValue(vals[11].(*interface{})))
			if err != nil {
				logger.Log.Infof("pacs得是否异常结果:%s非整形", getValue(vals[11].(*interface{})))
				ret.Sfyc = 0
			}
		}

		for _, v := range rets {
			if v.Jclx == ret.Jclx && v.Imagesight == ret.Imagesight && v.Imagediagnosis == ret.Imagediagnosis {
				existed = true
			}
		}
		if !existed {
			rets = append(rets, ret)
		}

	}

	// logger.Log.Infof("pacs 结果:%+v", rets)
	return &rets, nil
}
