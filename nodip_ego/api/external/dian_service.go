package external

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"fmt"
	"nodip_ego/api/app"
	"nodip_service/service"
	"utility/logger"

	"github.com/go-resty/resty/v2"
)

type DIANService struct{}

type Medinfo struct {
	RegId       string `json:"checkin_id"`
	OrderId     string `json:"occupy_order_id"`
	PackageId   string `json:"package_name"`
	Name        string `json:"name"`
	Sex         string `json:"sex"`
	Age         int32  `json:"age"`
	Birthday    string `json:"birthday"`
	IdCardType  string `json:"id_card_type"`
	IdCardNo    string `json:"id_card_no"`
	Married     string `json:"marital_status"`
	Address     string `json:"address"`
	Mobile      string `json:"mobile"`
	EmpNo       string `json:"emp_no"`
	WritePerson string `json:"write_person"`
	Department  string `json:"department"`
	OnDutyCode  string `json:"on_duty_code"`
}

type OrderInfo struct {
	OrderNo   string `json:"order_no"`
	OrderName string `json:"order_name"`
}
type OrderBody struct {
	Count int32       `json:"count"`
	List  []OrderInfo `json:"list"`
}

type ExamineBody struct {
	ExaminationNo string `json:"examination_no"`
}

type respData interface {
	OrderBody | ExamineBody | string
}

type DamsResponse[T respData] struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Body T      `json:"body"`
}

// UploadData ... di'an
func (DIANService) UploadData(info *models.ExtTypeDTO) error {

	//迪安只需要增加
	if info.Exttype != app.ExtOpType_ADD {
		logger.Log.Infof("非添加类型(exttype:1)，不需要处理")
		return nil
		// return errors.New("非添加类型(exttype:1)，不需要处理")
	}
	if info.Testid == "" {
		logger.Log.Errorf("请求数据错误，体检号为空")
		return errors.New("体检号不能为空")
	}
	logger.Log.Infof("对接参数:%+v", info)
	medinfos, err := service.QueryMedinfoByTestID(info.Testid)
	if err != nil {
		return err
	}
	if len(medinfos) <= 0 {
		return errors.New("can't find medinfo")
	}
	var patient = new(models.PatientModel)

	ptinfo, err := patient.GetPatientsbyPid(medinfos[0].TjPid)
	if err != nil {
		return err
	}

	var staff = new(models.StaffadminModel)
	staffinfo, err := staff.FindByID(medinfos[0].TjRecorder)
	if err != nil {
		return err
	}
	var dutycode = ""
	if medinfos[0].TjTesttype == 3 {
		dutycode = "SG"
	} else if medinfos[0].TjTesttype == 4 {
		dutycode = "ZG"
	} else if medinfos[0].TjTesttype == 5 {
		dutycode = "LG"
	} else {
		dutycode = ""
	}
	dianinfo := Medinfo{
		RegId:      medinfos[0].TjTestid,
		OrderId:    medinfos[0].TjAdditional,
		PackageId:  medinfos[0].TjPackagename,
		Name:       ptinfo.TjPname,
		Age:        int32(medinfos[0].TjAge),
		Sex:        getSex(ptinfo.TjPsex),
		Birthday:   ptinfo.TjPbirthday,
		IdCardType: "IDENTITY_CARD",
		IdCardNo:   ptinfo.TjPidcard,
		Married:    getMarriage(ptinfo.TjPmarriage),
		Address:    ptinfo.TjPaddress,
		Mobile:     ptinfo.TjPmobile,
		EmpNo:      medinfos[0].TjEmpid,
		// WritePerson: "yhh",
		WritePerson: staffinfo.TjStaffno,
		Department:  medinfos[0].TjEmpid,
		OnDutyCode:  dutycode,
	}
	// logger.Log.Infof("对接数据:%+v", dianinfo)
	json_ret, err := json.Marshal(dianinfo)
	if err != nil {
		return err
	}
	logger.Log.Infof("对接数据:%s", json_ret)
	// No need to set content type, if you have client level setting
	uri := fmt.Sprintf("%s%s", app.Configurations.External.Serverurl, app.URL_DIAN_PEIS)
	logger.Log.Infof("接口:%s", uri)
	client := resty.New()
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(json_ret).
		Post(uri)
	if err != nil {
		logger.Log.Errorf("Response error:%+v", err)
		return err
	}
	logger.Log.Infof("返回结果:%+v", string(resp.Body()))
	// respdata := "{\"code\":\"000000\",\"msg\":\"succeed\",\"body\":{\"examination_no\":\"220000012\"}}"
	var respret = DamsResponse[ExamineBody]{}
	err = json.Unmarshal(resp.Body(), &respret)
	// err = json.Unmarshal([]byte(respdata), &respret)
	if err != nil {
		return err
	}
	logger.Log.Infof("解析后的结果:%+v", respret)
	if respret.Code != "000000" {
		return errors.New(respret.Msg)
	}

	return nil
}

func (DIANService) GetOrderList() ([]OrderInfo, error) {
	uri := fmt.Sprintf("%s%s", app.Configurations.External.Serverurl, app.URL_DIAN_ORDERS)
	client := resty.New()
	resp, err := client.R().SetHeader("Content-Type", "application/json").Get(uri)
	if err != nil {
		// response.ERROR(c, http.StatusOK, err)
		return []OrderInfo{}, err
	}

	logger.Log.Infof("订单返回结果:%+v", resp)
	var respret = DamsResponse[OrderBody]{}
	err = json.Unmarshal(resp.Body(), &respret)
	if err != nil {
		return []OrderInfo{}, err
	}
	if respret.Code != "000000" {
		return []OrderInfo{}, errors.New(respret.Msg)
	}

	logger.Log.Infof("订单返回解析后得结果:%+v", respret)
	return respret.Body.List, nil
}

func getMarriage(marriage int) string {
	if marriage == 1 {
		return "Married"
	} else if marriage == 2 {
		return "Unmarried"
		// } else if marriage == 3 {
		// 	return "离异"
		// } else if marriage == 4 {
		// 	return "丧偶"
	} else {
		return "Unsure"
	}
}

func getSex(sex int) string {
	if sex == 1 {
		return "Male"
	} else if sex == 2 {
		return "Female"
	} else {
		return "Unknown"
	}
}
