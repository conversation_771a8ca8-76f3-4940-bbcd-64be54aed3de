package external

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"fmt"
	"nodip_ego/api/app"
	"utility/logger"

	// "nodip_ego/api/models"

	"time"

	"github.com/go-resty/resty/v2"
)

type JHTSService struct{}

type PacsData struct {
	SystemSn        string `json:"systemSn"` //系统编号，对应接入系统的编号
	SourceSn        string `json:"sourceSn"` //业务流水号
	Code            string `json:"code"`     //申请号，对应单据上的编号
	Classify        int    `json:"classify"` //1: 门诊, 2: 住院, 3: 体检
	Number          string `json:"number"`
	PatientNumber   string `json:"patientNumber"`
	PatientName     string `json:"patientName"`
	Gender          int    `json:"gender"`
	Birthdate       int64  `json:"birthday"`
	Age             string `json:"age"`
	Urgent          int    `json:"urgent"`
	Listype         string `json:"type"`
	ItemsSn         string `json:"itemsSn"`
	Name            string `json:"name"`
	PartMethod      string `json:"partMethod"` //检查部位及方法
	State           int    `json:"state"`      //状态，传代码：{1: 开立, 2: 核对, 3: 支付, 5: 执行, 6: 结果, 9: 取消}。如果应用系统收费完成后申请，则传3；如果开单即申请，则传1，在收费后通过更新状态接口更新到3。
	OrderDeptName   string `json:"orderDeptName"`
	OrderPersonName string `json:"orderPersonName"`
	OrderTime       uint64 `json:"orderTime"` //开单时间，Unix时间戳（毫秒）
}

type LisData struct {
	SystemSn        string `json:"systemSn"` //系统编号，对应接入系统的编号
	SourceSn        string `json:"sourceSn"` //业务流水号
	Code            string `json:"code"`     //申请号，对应单据上的编号
	BarCode         string `json:"barCode"`
	Classify        int    `json:"classify"` //1: 门诊, 2: 住院, 3: 体检
	Number          string `json:"number"`
	PatientNumber   string `json:"patientNumber"`
	PatientName     string `json:"patientName"`
	Idcard          string `json:"idcard"`
	Gender          int    `json:"gender"`
	Birthdate       int64  `json:"birthday"`
	Age             string `json:"age"`
	Phone           string `json:"phone"`
	Urgent          int    `json:"urgent"`
	State           int    `json:"state"` //状态，传代码：{1: 开立, 2: 核对, 3: 支付, 5: 执行, 6: 结果, 9: 取消}。如果应用系统收费完成后申请，则传3；如果开单即申请，则传1，在收费后通过更新状态接口更新到3。
	TypeName        string `json:"typeName"`
	ItemsSn         string `json:"itemsSn"`
	Num             int    `json:"num"`
	Name            string `json:"name"`
	OrderDeptName   string `json:"orderDeptName"`
	OrderDoctorName string `json:"orderDoctorName"`
	// OrderTime     uint64 `json:"orderTime"` //开单时间，Unix时间戳（毫秒）
}

type FeeData struct {
	SystemSn  string `json:"systemSn"` //系统编号，对应接入系统的编号
	Classify  string `json:"classify"`
	OrderType string `json:"orderType"` //订单类型{1：收费，2：退费}，默认1
	// Cost         string    `json:"cost"`      //费别（默认自费）
	DiaposalType   string    `json:"diaposalType"`
	PayType        int       `json:"payType"`        /*支付类型 1：个人支付 2：企业支付*/
	EnterpriseName string    `json:"enterpriseName"` /*公司名字*/
	Gender         int       `json:"gender"`
	Birthday       int64     `json:"birthday"` //时间戳
	Age            string    `json:"age"`
	Idcard         string    `json:"idcard"`
	ItemsList      []FeeItem `json:"itemsList"`
}
type FeeItem struct {
	SourceSn        string `json:"sourceSn"` //业务流水号
	Classify        string `json:"classify"` //1: 门诊, 2: 住院, 3: 体检
	Number          string `json:"number"`   //就诊业务编号（住院号/门诊号/体检
	PatientNumber   string `json:"patientNumber"`
	PatientName     string `json:"patientName"`
	DiaposalType    string `json:"diaposalType"` //处置类型{1：记账，2：销账}，默认1
	TypeSn          string `json:"typeSn"`       //项目类型编号（31：检查，32：检验）用91
	ProjectSn       string `json:"projectSn"`    //项目编号
	ProjectName     string `json:"projectName"`  //项目名称
	Num             int    `json:"num"`
	SaleUnit        string `json:"saleUnit"`
	BookkeepingTime int64  `json:"bookkeepingTime"`
	BillPersonSn    string `json:"billPersonSn"`
	BillPersonName  string `json:"billPersonName"`
	BillDeptSn      string `json:"billDeptSn"`
	BillDeptName    string `json:"billDeptName"`
	CostSource      string `json:"costSource"`
	// Gender          int    `json:"gender"`
	// Urgent          int    `json:"urgent"`
	// Listype         string `json:"type"`
	// Name            string `json:"name"`
	// PartMethod      string `json:"partMethod"` //检查部位及方法
	// State           int    `json:"state"`      //状态，传代码：{1: 开立, 2: 核对, 3: 支付, 5: 执行, 6: 结果, 9: 取消}。如果应用系统收费完成后申请，则传3；如果开单即申请，则传1，在收费后通过更新状态接口更新到3。
	// OrderDeptName   string `json:"orderDeptName"`
	// OrderPersonName string `json:"orderPersonName"`
	// OrderTime       uint64 `json:"orderTime"` //开单时间，Unix时间戳（毫秒）
}
type CancelFee struct {
	Number string `json:"number"`
}

// UploadData ... 金华田氏医院数据对接
func (JHTSService) UploadData(info *models.ExtTypeDTO) error {
	//1. 根据体检号从lis视图里获取数据
	//2. 根据体检号从pacs视图获取数据
	//3. 组合数据并传给接口

	if info.Testid == "" {
		logger.Log.Errorf("请求数据错误，体检号为空")
		return errors.New("体检号不能为空")
	}
	// var extview = new(models.ExternalView)
	var extview = new(models.ExtCheckiteminfoModel)

	logger.Log.Infof("开始对接的数据：%+v", info)
	// 1

	//删除
	if info.Exttype == app.ExtOpType_DEL {
		cancelFee(info.Testid)
	} else {
		ret, err := extview.QueryMany(info.Testid, 0)
		if err != nil {
			logger.Log.Errorf("根据体检号%s获取信息失败,错误信息%+v\n", info.Testid, err)
		}

		//上传lis数据
		uploadLis(ret)

		// 2
		uploadPacs(ret)

		// 3
		uploadFee(ret)
	}
	return nil
}

func cancelFee(testid string) error {
	logger.Log.Infof("取消收费:%s", testid)
	cancel_parm := &CancelFee{
		Number: testid,
	}
	json_ret, err := json.Marshal(cancel_parm)
	if err != nil {
		logger.Log.Infof("Error: %s", err)
		return err
	}
	logger.Log.Infof("cancel fee parameter: %s", json_ret)
	client := resty.New()

	// No need to set content type, if you have client level setting
	uri := fmt.Sprintf("%s%s", app.Configurations.External.Serverurl, app.URL_EXTERNAL_HIS_CANCEL)
	logger.Log.Infof("取消收费接口:%s", uri)
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(json_ret).
		Post(uri)
	if err != nil {
		logger.Log.Errorf("Response error:%+v", err)
		return err
	}

	logger.Log.Infof("取消收费接口:%+v", resp)
	return nil
}

func uploadLis(lisdata []models.ExtCheckiteminfo) error {
	logger.Log.Infof("开始上传lis数据")
	var lisretdata []LisData
	for _, v := range lisdata {
		// logger.Log.Infof("val: -> %v", v)
		if v.Depttype != 2 {
			continue
		}
		lisretdata = append(lisretdata, generatelisUploadData(v))
	}
	json_ret, err := json.Marshal(lisretdata)
	if err != nil {
		logger.Log.Infof("Error: %s", err)
	}
	logger.Log.Infof("lis数据参数：%s", string(json_ret))
	// Create a Resty Client
	client := resty.New()

	// POST JSON string
	// No need to set content type, if you have client level setting
	uri := fmt.Sprintf("%s%s", app.Configurations.External.Serverurl, app.URL_EXTERNAL_LIS)
	logger.Log.Infof("lis数据上传地址:%s", uri)
	if uri != "" {
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetBody(json_ret).
			Post(uri)
		if err != nil {
			logger.Log.Errorf("Response error:%+v", err)

		}

		logger.Log.Infof("lis上传返回结果:%+v", resp)
	}
	return nil
}

func uploadPacs(pacsdata []models.ExtCheckiteminfo) error {
	logger.Log.Infof("开始上传pacs数据")

	var pacsretdata []PacsData
	for _, v := range pacsdata {
		// logger.Log.Infof("val: -> %v", v)
		if v.Depttype != 3 {
			continue
		}
		pacsretdata = append(pacsretdata, generatepacsUploadData(v))
	}
	json_ret, err := json.Marshal(pacsretdata)
	if err != nil {
		logger.Log.Infof("Error: %s", err)
	}
	logger.Log.Infof("pacs数据参数：%s", string(json_ret))
	// Create a Resty Client
	client := resty.New()

	// POST JSON string
	// No need to set content type, if you have client level setting
	uri := fmt.Sprintf("%s%s", app.Configurations.External.Serverurl, app.URL_EXTERNAL_PACS)
	logger.Log.Infof("pacs数据上传地址:%s", uri)
	if uri != "" {
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetBody(json_ret).
			Post(uri)
		if err != nil {
			logger.Log.Errorf("Response error:%+v", err)

		}
		logger.Log.Infof("pacs上传返回结果:%+v", resp)
	}
	return nil
}

func uploadFee(itemssdata []models.ExtCheckiteminfo) error {
	logger.Log.Infof("开始上传收费信息数据:%+v", itemssdata)

	json_ret, err := json.Marshal(generatefeeUploadData(itemssdata))
	if err != nil {
		logger.Log.Infof("Error: %s", err)
	}
	logger.Log.Infof("收费数据参数：%s", string(json_ret))
	// Create a Resty Client
	client := resty.New()

	// POST JSON string
	// No need to set content type, if you have client level setting
	uri := fmt.Sprintf("%s%s", app.Configurations.External.Serverurl, app.URL_EXTERNAL_HIS)
	logger.Log.Infof("his数据上传地址:%s", uri)
	if uri != "" {
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetBody(json_ret).
			Post(uri)
		if err != nil {
			logger.Log.Errorf("Response error:%+v", err)

		}
		logger.Log.Infof("收费信息上传返回结果:%+v", resp)
	}
	return nil
}

func generatelisUploadData(ret models.ExtCheckiteminfo) LisData {

	psex := ret.Psex
	var barcode string
	if ret.Tid == "" {
		barcode = ret.Testid
	} else {
		barcode = ret.Tid
	}
	var birthdate int64 = 0
	var err error
	var tm2 time.Time

	if len(ret.Birthdate) > 10 {
		tm2, err = time.Parse("2006-01-02", ret.Birthdate[0:10])
	} else {
		tm2, err = time.Parse("2006-01-02", ret.Birthdate)
	}
	if err != nil {
		tm2 = time.Now()
	}
	birthdate = tm2.UnixNano() / int64(time.Millisecond)

	var dt = LisData{
		SystemSn:        app.Configurations.External.Appsn, //系统编号，对应接入系统的编号
		SourceSn:        fmt.Sprintf("%d", ret.ID),         //业务流水号
		Code:            barcode,                           //申请号，对应单据上的编号
		Classify:        3,                                 //1: 门诊, 2: 住院, 3: 体检
		Number:          ret.Testid,
		PatientNumber:   ret.Testid,
		PatientName:     ret.Testername,
		Gender:          psex,
		Birthdate:       birthdate,
		Age:             fmt.Sprintf("%d岁", ret.Age),
		Urgent:          2,
		Phone:           ret.Phone,
		ItemsSn:         ret.Itemid,
		Name:            ret.Itemname,
		TypeName:        ret.Sampletype, //检查部位及方法
		OrderDeptName:   ret.Deptname,
		OrderDoctorName: ret.Requestername,
		State:           1, //状态，传代码：{1: 开立, 2: 核对, 3: 支付, 5: 执行, 6: 结果, 9: 取消}。如果应用系统收费完成后申请，则传3；如果开单即申请，则传1，在收费后通过更新状态接口更新到3。
		Num:             1,
	}
	return dt
}

func generatepacsUploadData(ret models.ExtCheckiteminfo) PacsData {
	psex := ret.Psex
	itemid := ""
	dtinfo, err := models.GetCombineinfoByCombid(ret.Itemid)
	if err != nil || dtinfo == nil {
		itemid = ret.Itemid
	} else {
		itemid = dtinfo.TjItemid
	}

	var birthdate int64 = 0
	// var err error
	var tm2 time.Time

	if len(ret.Birthdate) > 10 {
		tm2, err = time.Parse("2006-01-02", ret.Birthdate[0:10])
	} else {
		tm2, err = time.Parse("2006-01-02", ret.Birthdate)
	}
	if err != nil {
		tm2 = time.Now()
	}
	birthdate = tm2.UnixNano() / int64(time.Millisecond)

	var dt = PacsData{
		SystemSn:        app.Configurations.External.Appsn, //系统编号，对应接入系统的编号
		SourceSn:        fmt.Sprintf("%d", ret.ID),         //业务流水号
		Code:            ret.Testid,                        //申请号，对应单据上的编号
		Classify:        3,                                 //1: 门诊, 2: 住院, 3: 体检
		Number:          ret.Testid,
		PatientNumber:   ret.Testid,
		PatientName:     ret.Testername,
		Gender:          psex,
		Birthdate:       birthdate,
		Age:             fmt.Sprintf("%d岁", ret.Age),
		Urgent:          2,
		Listype:         ret.Zdym,
		ItemsSn:         itemid,
		Name:            ret.Itemname,
		PartMethod:      ret.Itemname, //检查部位及方法
		State:           1,            //状态，传代码：{1: 开立, 2: 核对, 3: 支付, 5: 执行, 6: 结果, 9: 取消}。如果应用系统收费完成后申请，则传3；如果开单即申请，则传1，在收费后通过更新状态接口更新到3。
		OrderDeptName:   ret.Deptname,
		OrderPersonName: ret.Requestername,
		OrderTime:       ret.Requestdate * 1000, //uint64(time.Millisecond), //开单时间，Unix时间戳（毫秒）
	}
	return dt
}

func generatefeeUploadData(lis []models.ExtCheckiteminfo) FeeData {
	// var feeitem FeeItem
	var gender int = 9
	var birthdate int64 = 0
	var err error
	var tm2 time.Time
	var idnumber string
	var paytype int = 2
	var enterpriseName = ""
	// var testid = ""
	// var age = "0"
	if len(lis) <= 0 {
		return FeeData{}
	}
	// if len(lis) > 0 {
	gender = lis[0].Psex
	if gender != 1 && gender != 2 {
		gender = 9
	}
	//从字符串转为时间戳，第一个参数是格式，第二个是要转换的时间字符串
	if len(lis[0].Birthdate) > 10 {
		tm2, err = time.Parse("2006-01-02", lis[0].Birthdate[0:10])
	} else {
		tm2, err = time.Parse("2006-01-02", lis[0].Birthdate)
	}
	if err != nil {
		tm2 = time.Now()
	}
	birthdate = tm2.UnixNano() / int64(time.Millisecond)

	idnumber = lis[0].Idcard
	if err != nil {
		gender = 9
	}

	paytype = lis[0].Paytype

	var corpdto = models.CorpQueryDTO{}
	var corpmodels = models.CorpinfoModel{}
	logger.Log.Infof("lis[0].corpnum:%d", lis[0].Corpnum)
	corpdto.Ids = append(corpdto.Ids, lis[0].Corpnum)
	logger.Log.Infof("查找企业信息：%+v", corpdto)
	corpinfo, err := corpmodels.QueryCorpinfo(&corpdto)
	if err != nil || len(*corpinfo) <= 0 {
		enterpriseName = "未知企业"
	} else {
		enterpriseName = (*corpinfo)[0].TjCorpname
	}
	// }

	var itemlist []FeeItem

	for _, v := range lis {
		itemlist = append(itemlist, generatefeeitem(v))
	}

	diaposaltype := "1"

	var feedata = FeeData{
		SystemSn:       app.Configurations.External.Appsn,
		Classify:       "3",
		OrderType:      "1",
		DiaposalType:   diaposaltype,
		Gender:         gender,
		Birthday:       birthdate,
		PayType:        paytype,
		EnterpriseName: enterpriseName,
		Age:            fmt.Sprintf("%d岁", lis[0].Age),
		Idcard:         idnumber,
		ItemsList:      itemlist,
	}
	logger.Log.Infof("fee data:%+v", feedata)
	return feedata
}

func generatefeeitem(item models.ExtCheckiteminfo) FeeItem {
	// logger.Log.Infof("项目信息:%+v", item)
	var fee = FeeItem{
		SourceSn:        fmt.Sprintf("%d", item.ID), //业务流水号
		Classify:        "3",                        //1: 门诊, 2: 住院, 3: 体检
		Number:          item.Testid,                //就诊业务编号（住院号/门诊号/体检
		PatientNumber:   item.Testid,
		PatientName:     item.Testername,
		DiaposalType:    "1",           //处置类型{1：记账，2：销账}，默认1
		TypeSn:          "91",          //项目类型编号（31：检查，32：检验）用91
		ProjectSn:       item.Itemid,   //项目编号
		ProjectName:     item.Itemname, //项目名称
		Num:             1,
		SaleUnit:        "次",
		BookkeepingTime: time.Now().UnixNano() / int64(time.Millisecond),
		BillPersonSn:    item.Requesterid,
		BillPersonName:  item.Requestername,
		BillDeptSn:      item.Deptid,
		BillDeptName:    item.Deptname,
		CostSource:      "体检记账",
	}
	return fee
}
