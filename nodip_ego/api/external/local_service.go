package external

import (
	"datacontroller/models"
	"errors"
	"utility"
	"utility/logger"
	"utility/slice"
)

type LocalService struct{}

func (LocalService) UploadData(info *models.ExtTypeDTO) error {
	if info.Testid == "" {
		logger.Log.Errorf("请求数据错误，体检号为空")
		return errors.New("体检号不能为空")
	}

	ext_local := new(models.ExtCheckiteminfoModel)
	//先全部删除
	err := ext_local.DeleteMany(info.Testid)
	if err != nil {
		logger.Log.Errorf("错误:%+v", err)
		return err
	}
	if info.Exttype != -1 { //获取数据并写入
		//tj_checkiteminfo
		//tj_pateint
		//tj_medexaminfo
		//tj_departinfo
		//tj_staffadmin
		//tj_iteminfo
		var medmodel = new(models.MedexaminfoModel)
		var ptmodel = new(models.PatientModel)
		var itmodel = new(models.ItemsModel)
		var dptmodel = new(models.DeptModel)
		var staffmodel = new(models.StaffadminModel)
		// var cimodel = new(models.checki)
		var testids = []string{info.Testid}
		medinfos, err := medmodel.FindMedinfoByTestID(testids)
		if err != nil {
			logger.Log.Errorf("错误:%+v", err)
			return err
		}
		if len(medinfos) <= 0 {
			logger.Log.Errorf("没有该体检号的体检信息")
			return errors.New("没有该体检号的体检信息:" + info.Testid)
		}
		medinfo := medinfos[0]
		pid := medinfo.TjPid
		ptinfo, err := ptmodel.GetPatientsbyPid(pid)
		if err != nil {
			logger.Log.Errorf("错误:%+v", err)
			return err
		}
		ciinfos, err := models.FindCheckiteminfosByTestid(info.Testid)
		if err != nil {
			logger.Log.Errorf("错误:%+v", err)
			return err
		}
		var deptids, itemids []string
		for _, ci := range ciinfos {
			if !utility.Contains(deptids, ci.TjDeptid) {
				deptids = append(deptids, ci.TjDeptid)
			}
			if !utility.Contains(itemids, ci.TjItemid) && ci.TjCombineflag == 1 {
				itemids = append(itemids, ci.TjItemid)
			}
		}
		// logger.Log.Infof("科室信息:%+v", deptids)
		// logger.Log.Infof("项目信息:%+v", itemids)

		items, err := itmodel.GetIteminfos(itemids)
		if err != nil {
			logger.Log.Errorf("错误:%+v", err)
			return err
		}

		depts, err := dptmodel.GetDepartinfos(deptids)
		if err != nil {
			logger.Log.Errorf("错误:%+v", err)
			return err
		}

		staff, err := staffmodel.FindByID(medinfo.TjRecorder)
		if err != nil {
			logger.Log.Errorf("错误:%+v", err)
			return err
		}

		sampletypes, err := models.FindSampleTypes("")
		if err != nil {
			logger.Log.Errorf("错误:%+v", err)
			return err
		}
		var extitems []models.ExtCheckiteminfo

		for _, val := range ciinfos {
			if val.TjCombineflag == 0 {
				continue //明细项目不处理
			}
			dept, found := slice.Find(depts, func(elem models.TjDepartinfo) bool {
				return elem.TjDeptid == val.TjDeptid
			})
			if !found {
				logger.Log.Errorf("科室信息%s不能找到(确认是否被删除)", val.TjDeptid)
				continue
			}
			// if dept.TjDepttype == 1 {
			// 	//普通检查类型，不处理
			// 	continue
			// }
			item, found := slice.Find(items, func(ele models.TjIteminfo) bool {
				return val.TjItemid == ele.TjItemid
			})
			if !found {
				logger.Log.Errorf("项目信息%s不能找到(确认是否被删除)", val.TjItemid)
				continue
			}

			sample, found := slice.Find(sampletypes, func(elem models.TjSampletype) bool {
				return elem.SampleCode == item.SampleCode
			})
			if !found {
				sample = models.TjSampletype{
					SampleCode: "",
					SampleName: "",
				}
			}

			var itemid = item.TjLisnum
			if itemid == "" {
				itemid = item.TjItemid
			}
			var csex = "未知"
			if ptinfo.TjPsex == 1 {
				csex = "男"
			} else if ptinfo.TjPsex == 2 {
				csex = "女"
			}

			exitem := models.ExtCheckiteminfo{
				Testid:        medinfo.TjTestid,
				Tid:           val.TjBarcode,
				Testername:    ptinfo.TjPname,
				Idcard:        ptinfo.TjPidcard,
				Psex:          ptinfo.TjPsex,
				Csex:          csex,
				Birthdate:     ptinfo.TjPbirthday,
				Phone:         ptinfo.TjPphone,
				Age:           medinfo.TjAge,
				Itemname:      val.TjItemname,
				Itemid:        itemid,
				Itemid2:       item.TjItemid,
				Deptid:        val.TjDeptid,
				Deptname:      dept.TjDeptname,
				Depttype:      dept.TjDepttype,
				Requesterid:   medinfo.TjRecorder,
				Requestername: staff.TjStaffname,
				Requestdate:   uint64(medinfo.TjRecorddate),
				Requestdate2:  utility.TimeStamptoISODate(medinfo.TjRecorddate),
				Paytype:       medinfo.TjPaymethod,
				Packagename:   medinfo.TjPackagename,
				Zdym:          dept.TjZdym,
				Sampletype:    sample.SampleCode,
				Corpnum:       medinfo.TjCorpnum,
				Syncstatus:    0,
			}

			extitems = append(extitems, exitem)
		}
		//insert into tables
		err = ext_local.InsertMany(&extitems)
		if err != nil {
			logger.Log.Errorf("错误:%+v", err)
			return err
		}

	}

	return nil
}
