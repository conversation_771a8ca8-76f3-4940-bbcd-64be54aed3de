package middlewares

import (
	"nodip_ego/api/auth"
	"utility/logger"

	"github.com/gin-gonic/gin"
)

// TokenAuthMiddleware ...
func TokenAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// fmt.Printf("请求信息:%+v\n", c.Request)
		var token *auth.UserToken
		var err error

		token, err = auth.ExtractTokenAuth(c.Request)
		if err != nil {
			// c.JSON(http.StatusUnauthorized, "You need to be authorized to access this route")
			// err := new error("")
			// response.ERROR(c, http.StatusUnauthorized, err)
			// c.Abort()
			// return
			logger.Log.Infof("请求方式：%s, 请求地址:%s", c.Request.Method, c.Request.RequestURI)

		} else {
			// log.Printf("用户[%s]发送请求，请求方式：%s, 请求地址:%s", token.User, c.Request.Method, c.Request.RequestURI)
			logger.Log.Infof("用户[%s]发送请求，请求方式：%s, 请求地址:%s", token.User, c.Request.Method, c.Request.RequestURI)
		}
		c.Next()
	}
}

// TokenMiddleware ...
func TokenMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// fmt.Printf("请求信息:%+v\n", c.Request)
		// var token *auth.UserToken
		// var err error

		token, err := auth.ExtractUserToken(c.Request)
		if err != nil {
			// response.ERROR(c, http.StatusNetworkAuthenticationRequired, err)
			// c.Abort()
			// return
			logger.Log.Infof("请求方式：%s, 请求地址:%s", c.Request.Method, c.Request.RequestURI)
		} else {
			// log.Printf("用户[%s]发送请求，请求方式：%s, 请求地址:%s", token.User, c.Request.Method, c.Request.RequestURI)
			logger.Log.Infof("用户[%s]发送请求，请求方式：%s, 请求地址:%s", token.User, c.Request.Method, c.Request.RequestURI)
		}
		c.Next()
	}
}
