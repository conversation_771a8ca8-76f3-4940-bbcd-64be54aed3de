package response

import (
	"net/http"
	"nodip_ego/api/app"

	"github.com/gin-gonic/gin"
)

// type ResponseModelBase struct {
// 	Code    int    `json:"code"`
// 	Message string `json:"message"`
// }
// 	Code    int    `json:"code"`
// 	Message string `json:"message"`
// }

// ResponseModel ... json page
type ResponseModel struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// ResponsePageData ... json page
type ResponsePageData struct {
	Total int         `json:"total"`
	Data  interface{} `json:"data"`
}

// ResponsePage ... json page
type ResponsePage struct {
	Code    int              `json:"code"`
	Message string           `json:"message"`
	Data    ResponsePageData `json:"data"`
}

// 响应JSON数据

// JSONModel ... json page
func JSONModel(c *gin.Context, msg string, v interface{}) {
	ret := ResponseModel{Code: app.CodeSuccess, Message: msg, Data: v}
	// JSON(c, http.StatusOK, &ret)
	c.J<PERSON>(http.StatusOK, &ret)
}

// JSON ... json page
func JSON(c *gin.Context, total int, v interface{}) {
	ret := ResponsePage{Code: app.CodeSuccess, Message: "ok", Data: ResponsePageData{Total: total, Data: v}}
	// JSON(c, http.StatusOK, &ret)
	c.JSON(http.StatusOK, &ret)
}

// ERROR ... json page
func OK(c *gin.Context, status int) {
	c.JSON(status, ResponseModel{
		Code:    app.CodeSuccess,
		Message: "OK",
		Data:    "",
	})
}

// ERROR ... json page
func ERROR(c *gin.Context, status int, err error) {
	c.JSON(status, ResponseModel{
		Code:    app.CodeError,
		Message: err.Error(),
		Data:    "",
	})
}

// ERROR ... json page
func JSONError(c *gin.Context, err error, v interface{}) {
	ret := ResponsePage{Code: app.CodeError, Message: err.Error(), Data: ResponsePageData{Total: 0, Data: v}}
	// c.JSON(status, ResponseModel{
	// 	Code:    app.CodeError,
	// 	Message: err.Error(),
	// 	Data:    "",
	// })
	c.JSON(http.StatusOK, &ret)
}

// JSONPage ... json page
// func JSONPage(c *gin.Context, total uint64, list interface{}) {
// 	ret := ResponsePage{Code: app.CODE_SUCCESS, Message: "ok", Data: ResponsePageData{Total: total, Data: list}}
// 	JSON(c, http.StatusOK, &ret)
// }
