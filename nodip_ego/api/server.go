package api

import (
	"fmt"
	"net/http"
	"nodip_ego/api/app"
	"nodip_ego/api/controllers"
	"nodip_service/service"
	"utility/logger"

	// "nodip_ego/api/app"

	"github.com/gin-gonic/gin"
	//"tohz.com/john/pneuserver/api/seed"
)

// Run ... run the app
func Run(update string) {

	// log.Printf("[info] start to run the server...")

	// gin.SetMode(gin.DebugMode)
	gin.SetMode(gin.ReleaseMode)

	routersInit := controllers.InitRouter() //.InitRouter()

	// readTimeout := setting.ServerSetting.ReadTimeout
	// writeTimeout := setting.ServerSetting.WriteTimeout
	endPoint := fmt.Sprintf("%s:%d", app.Configurations.Application.Apphost, app.Configurations.Application.Appport)

	maxHeaderBytes := 1 << 20

	server := &http.Server{
		Addr:    endPoint,
		Handler: routersInit,
		// ReadTimeout:    readTimeout,
		// WriteTimeout:   writeTimeout,
		MaxHeaderBytes: maxHeaderBytes,
	}

	if update == "-f" {
		service.Updatedata()
	}
	logger.Log.Infof("start http server listening %s", endPoint)

	server.ListenAndServe()

}
