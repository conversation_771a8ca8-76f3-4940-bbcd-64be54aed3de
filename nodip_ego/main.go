package main

import (
	// "nodip_ego/api"

	"datacontroller/models"
	"nodip_ego/api"
	"nodip_ego/api/app"
	"nodip_ego/api/auth"
	"os"
	"utility/logger"
)

// Config ...
type SystemInfo struct {
	Version      string `json:"version"`
	BuildVersion int    `json:"buildversion"`
	BuildTime    string `json:"buildtime"`
}

func init() {
	app.InitDirectory()
	app.InitConfiguration()
	var isproduction = false
	if app.Configurations.Application.Production == 1 {
		isproduction = true
	}
	logger.InitLogger(app.Configurations.Application.Logfile, isproduction)
	models.Setup(app.Configurations.Database.Username, app.Configurations.Database.Password, app.Configurations.Database.Dbserver, app.Configurations.Database.Dbname, app.Configurations.Database.Port)
	auth.LoadSignedKey()

	sysinfo := SystemInfo{
		Version:      "2.9.00",
		BuildVersion: 20900,
		BuildTime:    "2024-09-04",
	}

	logger.Log.Infof("Starting application. version:%s, buildversion:%d, buildtime:%s", sysinfo.Version, sysinfo.BuildVersion, sysinfo.BuildTime)
}

func main() {

	update := ""
	ilen := len(os.Args)
	if ilen >= 2 {
		update = os.Args[1]
	}
	logger.Log.Infof("==============args 1 is:%s", update)
	api.Run(update)

}
