package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"nodip_ego/api/app"
	"strings"
	utils "utility"
	"utility/logger"

	"github.com/google/uuid"
)

// LoginDTO ... logindto
type LoginDTO struct {
	Userid   string `json:"userid"`
	Password string `json:"password"`
	Terminal string `json:"terminal"`
	Enc      int32  `json:"enc"`
}

var staffmodel = new(models.StaffadminModel)

// Login ... login
func Login(loginstr []byte) (*models.TjStaffadmin, error) {
	// var dat map[string]interface{}
	var dat = LoginDTO{}
	err := json.Unmarshal(loginstr, &dat)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	// fmt.Println(dat)
	logger.Log.Infof("用户登录:%s", dat.Userid)
	if len(strings.Trim(dat.Userid, " ")) <= 0 {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	// var staff = models.TjStaffadmin{}

	// json.Unmarshal(loginstr)
	staff, err := staffmodel.FindByNo(dat.Userid)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	// fmt.Printf("%+v\n", staff) // Print with Variable Name

	//登录用户的有效性验证，密码验证
	if staff.TjStatus <= 0 {
		return nil, errors.New("该账号已冻结，不能登录")
	}

	encpwd, err := utils.Enc(dat.Password, app.ENCKEY, app.IV)
	if err != nil {
		return nil, errors.New("check password error")
	}
	// println("Encoded password: ", encpwd)

	if encpwd != staff.TjPassword {
		return nil, errors.New("账号密码错误，请重试！")
	}

	_, err = staffmodel.SaveLoginHistory(staff)
	if err != nil {
		logger.Log.Errorf("错误: %s", err.Error())
		return nil, errors.New("保存登录信息出错")
	}

	str, err := GenerateLoginSession()
	if err != nil {
		logger.Log.Errorf("生成UUID出错:%s", err.Error())
		return nil, errors.New("生成UUID出错")
	}
	// println("用户得session: ", str)
	staff.LoginSession = str
	err = staffmodel.UpdateLoginSession(staff)
	if err != nil {
		logger.Log.Errorf("错误: %s", err.Error())
		return nil, errors.New("更新登录session出错")
	}

	return staff, nil
}

// VerifyLogin ...
func VerifyLogin(staffno string, uuidstr string) (bool, error) {
	staff, err := staffmodel.FindByNo(staffno)
	// fmt.Printf("用户信息:%+v\n", staff)
	// fmt.Printf("用户登录uuid:%s\n", uuidstr)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return false, err
	}
	if staff.LoginSession != uuidstr {
		return false, errors.New("登录session不一致")
	}

	return true, nil
}

// LogOut ...
func LogOut(staffno string) error {
	logger.Log.Infof("退出登录: %s", staffno)
	staff, err := staffmodel.FindByNo(staffno)
	if err != nil {
		return nil
	}
	staff.LoginSession = ""
	err = staffmodel.UpdateLoginSession(staff)
	logger.Log.Errorf("错误信息：%+v\n", err)
	return err
}

// GenerateLoginSession ...
func GenerateLoginSession() (string, error) {
	val, err := uuid.NewUUID()
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return "", err
	}

	// return strings.ReplaceAll(val.String(), "-", ""), nil
	return val.String(), nil
}
