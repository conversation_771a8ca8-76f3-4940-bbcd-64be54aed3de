package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var audioModel = new(models.AudiogramModel)

// QueryAudiogramDetails ...
func QueryAudiogramDetails(body []byte) (*[]models.TjAudiogramdetail, error) {
	var dat models.AudiogramDTO
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	return audioModel.QueryAudiogramDetails(&dat)
}

//InsertAudiogramDetails ...
func InsertAudiogramDetails(body []byte) (*[]models.TjAudiogramdetail, error) {
	var infos []models.TjAudiogramdetail
	err := json.Unmarshal(body, &infos)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	if len(infos) <= 0 {
		return nil, nil
	}
	// log.Printf("提交更新的电测听结果,总数：%d,值：%+v\n", len(infos), infos)

	dto := models.AudiogramDTO{
		Testid: infos[0].TjTestid,
		Ear:    -1,
		Type:   -1,
		Freq:   -1,
	}

	existInfos, err := audioModel.QueryAudiogramDetails(&dto)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	// log.Printf("系统已经存在的数据,总数：%d,值：%+v\n", len(*existInfos), existInfos)

	var insertedInfos = make([]models.TjAudiogramdetail, 0) //需要增加
	var updatedInfos = make([]models.TjAudiogramdetail, 0)  //需要更新
	// var deletedInfos = make([]models.TjAudiogramdetail, 0)  //需要删除的内容

	for _, v := range infos {
		retv := FindAudigramDetail(*existInfos, &v)
		if retv == nil {
			v.ID = 0
			insertedInfos = append(insertedInfos, v) //insert new
		} else {
			v.ID = (*retv).ID
			updatedInfos = append(updatedInfos, v)
		}

	}
	// log.Printf("需要插入的电测听结果,总数:%d,值：%+v\n", len(insertedInfos), insertedInfos)
	// log.Printf("需要更新的电测听结果,总数:%d,值：%+v\n", len(updatedInfos), updatedInfos)
	if len(insertedInfos) > 0 {
		audioModel.CreateAudiogramDetail(&insertedInfos)
	}
	if len(updatedInfos) > 0 {
		audioModel.UpdateAudiogramDetail(&updatedInfos)
	}
	var retInfos = make([]models.TjAudiogramdetail, 0)
	// retInfos = append(retInfos, insertedInfos...)
	// for _, r := range insertedInfos {
	// 	retInfos = append(retInfos, r)
	// }
	retInfos = append(retInfos, insertedInfos...)
	// for _, r := range updatedInfos {
	// 	retInfos = append(retInfos, r)
	// }
	retInfos = append(retInfos, updatedInfos...)

	log.Printf("返回的结果,总数：%d,值：%+v\n", len(retInfos), retInfos)
	return &retInfos, nil
}

// //UpdateAudiogramDetails ...
// func UpdateAudiogramDetails(body []byte) (*[]models.TjAudiogramdetail, error) {
// 	var dat []models.TjAudiogramdetail
// 	err := json.Unmarshal(body, &dat)
// 	if err != nil {
// 		return nil, err
// 	}

// 	return audioModel.UpdateAudiogramDetail(&dat)
// }

// //DeleteAudiogramDetails ...
// func DeleteAudiogramDetails(testid string, ear int, adtype int) error {
// 	if testid == "" {
// 		return errors.New("体检号不能为空")
// 	}

// 	return audioModel.DeleteAudiogramDetail(testid, ear, adtype)
// }

// FindAudigramDetail ...
func FindAudigramDetail(infos []models.TjAudiogramdetail, ev *models.TjAudiogramdetail) *models.TjAudiogramdetail {
	// finded := false
	for idx := range infos {
		if (*ev).TjTestid == (infos)[idx].TjTestid && (*ev).TjFreq == infos[idx].TjFreq && (*ev).TjEar == infos[idx].TjEar && (*ev).TjAdtype == infos[idx].TjAdtype {
			// find existed
			// finded = true
			return &infos[idx]
		}
	}
	return nil
}

// InsertAudiogramResult ...
func InsertAudiogramResult(body []byte) (*models.TjAudiogramresult, error) {

	var info models.TjAudiogramresult
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return audioModel.CreateAudiogramResult(&info)
}

// QueryAudiogramResult ...
func QueryAudiogramResult(testid string) (*[]models.TjAudiogramresult, error) {

	if testid == "" {
		return nil, errors.New("体检编号不能为空")
	}

	return audioModel.QueryAudiogramResult(testid)
}

// SaveAudiogramResult ...
func SaveAudiogramResult(body []byte) (*models.TjAudiogramresult, error) {
	var info models.TjAudiogramresult
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	ret, err := audioModel.QueryAudiogramResult(info.TjTestid)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	if len(*ret) <= 0 {
		//do insert
		log.Printf("开始插入新的结果值\n")
		return audioModel.CreateAudiogramResult(&info)
	}
	info.ID = (*ret)[0].ID
	log.Printf("开始更新结果值:%+v\n", info)
	return audioModel.UpdateAudiogramResult(&info)

}

// *************************QueryAudiogramRevises ******************************

// QueryAudiogramRevises ...
func QueryAudiogramRevises() (*[]models.TjAudiogramrevise, error) {

	return audioModel.QueryAudiogramRevise()
}

// *************************QueryAudiogramRevises ******************************

// QueryAudiogramSummary ...
func QueryAudiogramSummary() (*[]models.TjAudiogramsummary, error) {

	return audioModel.QueryAudiogramSummary()
}

// InsertAudiogramSummary ...
func InsertAudiogramSummary(body []byte) (*models.TjAudiogramsummary, error) {
	var info models.TjAudiogramsummary
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return audioModel.CreateAudiogramSummary(&info)
}

// UpdateAudiogramSummary ...
func UpdateAudiogramSummary(body []byte) (*models.TjAudiogramsummary, error) {
	var info models.TjAudiogramsummary
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return audioModel.UpdateAudiogramSummary(&info)

}

// DeleteAudiogramSummary ...
func DeleteAudiogramSummary(id int) error {
	if id <= 0 {
		return errors.New("id不能小于0")
	}

	return audioModel.DeleteAudiogramSummary(id)

}
