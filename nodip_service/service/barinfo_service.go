package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var barModel = new(models.BarModel)

// *************************** Barnameinfo BEGIN ******************************

// QueryBarnameinfo ... Query
func QueryBarnameinfo(pid string) (*[]models.TjBarnameinfo, error) {
	// var info = models.ItemrefDTO{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	return nil, err
	// }
	return barModel.GetBarnameinfo(pid)
}

// InsertBarnameinfo ... insert
func InsertBarnameinfo(body []byte) (*models.TjBarnameinfo, error) {
	var info = models.TjBarnameinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return barModel.InsertBarnameinfo(&info)
}

// UpdateBarnameinfo ... Update
func UpdateBarnameinfo(body []byte) error {
	var info = models.TjBarnameinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return barModel.UpdateBarnameinfo(&info)
}

// DeleteBarnameinfo ... Update
func DeleteBarnameinfo(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return barModel.DeleteBarnameinfo(id)
}

// ************************* Barnameinfo END **************************

// *************************** Bardetail BEGIN ******************************

// QueryBardetail ... Query
func QueryBardetail(body []byte) (*[]models.TjBardetail, error) {
	var info = models.BardetailDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	return barModel.GetBardetail(&info)
}

// InsertBardetail ... insert
func InsertBardetail(body []byte) (*models.TjBardetail, error) {
	var info = models.TjBardetail{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return barModel.InsertBardetail(&info)
}

// UpdateBardetail ... Update
func UpdateBardetail(body []byte) error {
	var info = models.TjBardetail{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return barModel.UpdateBardetail(&info)
}

// DeleteBardetail ... Update
func DeleteBardetail(body []byte) error {
	log.Printf("删除bardetail %s\n", string(body))
	var info = models.BardetailDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}
	return barModel.DeleteBardetail(&info)
}

// ************************* Bardetail END **************************

// *************************** Baritems BEGIN ******************************

// QueryBaritems ... Query
func QueryBaritems(pid string) (*[]models.TjBaritems, error) {
	// var info = models.ItemrefDTO{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	return nil, err
	// }
	return barModel.GetBaritems(pid)
}

// InsertBaritems ... insert
func InsertBaritems(body []byte) (*models.TjBaritems, error) {
	var info = models.TjBaritems{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return barModel.InsertBaritems(&info)
}

// UpdateBaritems ... Update
func UpdateBaritems(body []byte) error {
	var info = models.TjBaritems{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return barModel.UpdateBaritems(&info)
}

// DeleteBaritems ... Update
func DeleteBaritems(binum string) error {

	// log.Printf("开始删除条码信息 :%s\n", binum)
	if binum == "" {
		return errors.New("删除条件为空，不能删除")
	}
	err := barModel.DeleteBaritems(binum)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	var dto = new(models.BardetailDTO)
	dto.Binum = append(dto.Binum, binum)
	err = barModel.DeleteBardetail(dto)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	return nil
}

// ************************* Baritems END **************************
