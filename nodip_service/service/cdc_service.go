package service

import "datacontroller/models"

// "nodip_ego/api/models"

// UpdateZjCdcCheckitemsService ... Query
func UpdateZjCdcCheckitemsService(dto *models.ExtCodeDTO) error {

	return models.UpdateScdcCheckitem(dto)
}

func UpdateZjCdcHazardfactorService(dto *models.ExtCodeDTO) error {
	// var info = models.ScdcHazardfactor{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	app.SugarLogger.Errorf("错误信息：%+v\n", err)
	// 	return err
	// }

	return models.UpdateScdcHazardfactor(dto)
}

func UpdateGjcdcCheckitemService(dto *models.ExtCodeDTO) error {
	// var info = models.GjCdcCheckitem{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	app.SugarLogger.Errorf("错误信息：%+v\n", err)
	// 	return err
	// }

	return models.UpdateScdcCheckitem(dto)
}

func UpdateGjCdcHazardfactorService(dto *models.ExtCodeDTO) error {
	// var info = models.GjCdcHazardfactor{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	app.SugarLogger.Errorf("错误信息：%+v\n", err)
	// 	return err
	// }

	return models.UpdateScdcHazardfactor(dto)
}
