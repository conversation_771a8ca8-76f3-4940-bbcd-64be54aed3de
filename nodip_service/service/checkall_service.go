package service

import (
	"datacontroller/models"
	"encoding/json"
	"log"
	"strconv"
	"strings"
	"utility/logger"
	// "nodip_ego/api/models"
)

var patientModel = new(models.PatientModel)

// InsertCheckall ... insert
func InsertCheckall(body []byte) (*models.TjCheckallnew, error) {
	var info = models.TjCheckallnew{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	log.Printf("insert 总检:%+v\n", info)
	return models.CreateCheckAll(&info)
}

// UpdateCheckall ... Update
func UpdateCheckall(body []byte) (*models.TjCheckallnew, error) {
	var info = models.TjCheckallnew{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	log.Printf("update 总检:%+v\n", info)
	return models.UpdateCheckAll(&info)
}

// QueryCheckall ... Query
func QueryCheckall(info *models.CaQueryDTO) ([]models.TjCheckallnew, error) {
	log.Printf("Query 总检:%+v\n", info)
	return models.GetCheckall(info)
}

func CheckCheckallDiseases(ckall *models.TjCheckallnew) {
	if ckall.TjDiscode != "" {
		cadiscode := strings.Split(ckall.TjDiscode, ",")

		for _, disidx := range cadiscode {
			logger.Log.Infof("=====疾病代码======:%s", disidx)
			disid, err := strconv.Atoi(disidx)
			if err != nil {
				continue
			}
			//从tj_diseases中查找疾病信息（根据discode)
			disinfo, err := Diseasemodel.GetDiseasebyId(disid)
			if err != nil {
				continue
			}

			var meddisinfo models.TjDiseaseinfo
			meddisinfo.TjTestid = ckall.TjTestid
			meddisinfo.TjDisid = disinfo.ID
			meddisinfo.TjDiseasenum = disinfo.TjDiscode
			meddisinfo.TjDiseasename = disinfo.TjDisname
			meddisinfo.TjSuggestion = disinfo.TjContent
			if disinfo.TjDeptid == 0 {
				meddisinfo.TjDeptid = "0035"
			}
			meddisinfo.TjIsdisease = 1
			meddisinfo.TjIsoccu = disinfo.TjOccudiseaseflag
			meddisinfo.TjTypeid = disinfo.TjTypeid
			meddisinfo.TjOpinion = disinfo.TjOpinion
			meddisinfo.TjShoworder = disinfo.ID
			logger.Log.Infof("patient diseases info:%+v", meddisinfo)
			patientModel.SavePatientDiseases(&meddisinfo)

		}
	}
}
