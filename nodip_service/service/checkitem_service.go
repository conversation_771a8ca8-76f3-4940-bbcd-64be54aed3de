package service

import (
	"datacontroller/models"
	"encoding/json"
	"fmt"
	"log"
	"utility/logger"

	// "nodip_ego/api/models"
	"strings"
)

// InsertCheckitemInfo ... insert
func InsertCheckitemInfo(body []byte) (*models.TjCheckiteminfo, error) {
	var info models.TjCheckiteminfo
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	log.Printf("开始插入新的检查项目信息......%+v\n", info)
	return models.CreateCheckitemInfo(&info)

	// return nil, nil
}

// InsertCheckitemInfos ... insert
func InsertCheckitemInfos(body []byte) (*[]models.TjCheckiteminfo, error) {
	var info []models.TjCheckiteminfo
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	// first := info[0]
	// models.CreateCheckitemInfo(&first)
	log.Printf("开始插入新的检查项目信息......%+v\n", info)
	return models.CreateCheckitemInfos(&info)

	// return nil, nil
}

// UpdateCheckitemInfos ... insert
func UpdateCheckitemInfos(body []byte) (*[]models.TjCheckiteminfo, error) {
	var info []models.TjCheckiteminfo
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	// log.Printf("check item info(json):%+v\n", info)
	return models.SaveCheckitemInfo(&info)

	// return nil, nil
}

// SearchCheckiteminfos ... get checkiteminfos
func SearchCheckiteminfos(body []byte) ([]models.TjCheckiteminfo, error) {
	var cidto = models.CiQueryDTO{}
	err := json.Unmarshal(body, &cidto)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	log.Printf("query check item info(json):%+v\n", cidto)
	// logger.Log.Infof("开始根据父代码获取地区信息，父代码：: %s", pcode)
	return models.FindCheckiteminfos(&cidto)
	// return nil, nil
}

// RemoveCheckiteminfos ... remove checkiteminfos
func RemoveCheckiteminfos(body []byte) error {
	var cidto = models.CiDeleteDTO{}
	err := json.Unmarshal(body, &cidto)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return err
	}

	return models.DeleteCheckiteminfos(&cidto)
}

func ConvertItemToCheckItem(ti *models.TjIteminfo) (models.TjCheckiteminfo, error) {
	var ret = models.TjCheckiteminfo{}
	ret.TjItemid = ti.TjItemid
	ret.TjItemname = ti.TjItemname
	ret.TjResult = ti.TjDefaultresult
	ret.TjItemunit = ti.TjItemunit
	ret.TjAbnormalflag = 0
	ret.TjBarflag = ti.TjBarflag
	ret.TjAbnormalshow = ""
	ret.TjCombineflag = ti.TjCombineflag
	ret.TjShoworder = ti.TjShoworder
	ret.TjSynid = ti.TjItemid
	ret.TjCombineorder = 0
	if strings.ToUpper(ti.TjValuetype) == "N" {
		if strings.ToUpper(ti.TjReftype) == "X-Y" || strings.ToUpper(ti.TjReftype) == "X_Y" {
			ret.TjItemrange = fmt.Sprintf("%s-%s", ti.TjLowvalue, ti.TjUppervalue)
		} else if strings.ToUpper(ti.TjReftype) == "<Y" {
			ret.TjItemrange = fmt.Sprintf("<%s", ti.TjUppervalue)
		} else if strings.ToUpper(ti.TjReftype) == ">X" {
			ret.TjItemrange = fmt.Sprintf(">%s", ti.TjLowvalue)
		}
	}
	return ret, nil
}
