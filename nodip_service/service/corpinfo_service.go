package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"utility/logger"
	// "nodip_ego/api/models"
)

//
var corpinfoModel = new(models.CorpinfoModel)

// QueryCorpinfo ...
func QueryCorpinfo(body []byte) (*[]models.TjCorpinfo, error) {
	var dat models.CorpQueryDTO
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return corpinfoModel.QueryCorpinfo(&dat)
}

//InsertCorpinfo ...
func InsertCorpinfo(body []byte) (*models.TjCorpinfo, error) {
	var dat models.TjCorpinfo
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return corpinfoModel.CreateCorpinfo(&dat)
}

//UpdateCorpinfo ...
func UpdateCorpinfo(body []byte) error {
	var dat models.TjCorpinfo
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return err
	}

	return corpinfoModel.UpdateCorpinfo(&dat)
}

//DeleteCorpinfo ...
func DeleteCorpinfo(id int) error {
	if id <= 0 {
		return errors.New("id 不能小于等于0")
	}

	return corpinfoModel.DeleteCorpinfo(id)
}

// ********************* corp med info **********************

// QueryCorpMedinfo ...
func QueryCorpMedinfo(body []byte) (*[]models.TjCorpmedexaminfo, error) {
	var dat models.CorpMedQuery
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return corpinfoModel.QueryCorpMedinfo(&dat)
}

//InsertCorpMedinfo ...
func InsertCorpMedinfo(body []byte) (*models.TjCorpmedexaminfo, error) {
	var dat models.TjCorpmedexaminfo
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return corpinfoModel.CreateCorpMedinfo(&dat)
}

//UpdateCorpMedinfo ...
func UpdateCorpMedinfo(body []byte) error {
	var dat models.TjCorpmedexaminfo
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return err
	}

	return corpinfoModel.UpdateCorpMedinfo(&dat)
}

//DeleteCorpMedinfo ...
func DeleteCorpMedinfo(id int) error {
	if id <= 0 {
		return errors.New("id 不能小于等于0")
	}

	return corpinfoModel.DeleteCorpMedinfo(id)
}
