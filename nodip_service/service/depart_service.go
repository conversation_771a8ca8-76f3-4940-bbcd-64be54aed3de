package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

// Deptmodel ...
var Deptmodel = new(models.DeptModel)

// *************************** Departinfo BEGIN ******************************

// QueryDepartinfo ... Query
func QueryDepartinfo(deptid string) (*[]models.TjDepartinfo, error) {

	return Deptmodel.GetDepartinfo(deptid)
}

// InsertDepartinfo ... insert
func InsertDepartinfo(body []byte) (*models.TjDepartinfo, error) {
	var info = models.TjDepartinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Deptmodel.InsertDepartinfo(&info)
}

// UpdateDepartinfo ... Update
func UpdateDepartinfo(body []byte) error {
	var info = models.TjDepartinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Deptmodel.UpdateDepartinfo(&info)
}

// DeleteDepartinfo ... Update
func DeleteDepartinfo(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Deptmodel.DeleteDepartinfo(id)
}

// ************************* Departinfo END **************************

// *************************** Deptitem BEGIN ******************************

// QueryDeptitem ... Query
func QueryDeptitem(body []byte) (*[]models.TjDeptitem, error) {
	var info = models.DeptItemDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	return Deptmodel.GetDeptitem(&info)
}

// InsertDeptitem ... insert
func InsertDeptitem(body []byte) (*models.TjDeptitem, error) {
	var info = models.TjDeptitem{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Deptmodel.InsertDeptitem(&info)
}

// UpdateDeptitem ... Update
func UpdateDeptitem(body []byte) error {
	var info = models.TjDeptitem{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Deptmodel.UpdateDeptitem(&info)
}

// DeleteDeptitem ... Update
func DeleteDeptitem(body []byte) error {
	var info = models.DeptItemDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	if info.Deptid == "" || info.Itemid == "" {
		return errors.New("删除条件为空，不能删除")
	}
	return Deptmodel.DeleteDeptitem(&info)
}

// ************************* Deptitem END **************************
