package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"utility/logger"
	// "nodip_ego/api/models"
)

func FindDictionary(dicts *[]models.SsDictionary, typeid int, pid int) *models.SsDictionary {
	for _, val := range *dicts {
		if val.SsTypeid == typeid && val.SsPid == pid {
			return &val
		}
	}

	return nil
}

func GetExternalConfigs(itype int) (*[]models.TjExternal, error) {
	return models.GetExternalInfo(itype)
}

// GetDictionary ... get infos
func GetDictionary(itype int, pid int) (*[]models.SsDictionary, error) {
	return models.FindDictionary(itype, pid)
}

// GetDictionaries ... get infos
func GetDictionaries(itype int) ([]models.SsDictionary, error) {
	return models.FindDictionaries(itype)
}

// UpdateDictionary ...
func UpdateDictionary(body []byte) error {
	var info models.SsDictionary
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}
	return models.SaveDictionary(&info)
}

// GetIdentity ...
func GetIdentity(code string) (*models.SsIDentity, error) {
	if code == "" {
		return nil, errors.New("code为空")
	}

	return models.FindIdentity(code)
}

// UpdateIdentity ... get area infos
func UpdateIdentity(body []byte) error {
	var idinfo = models.SsIDentity{}
	err := json.Unmarshal(body, &idinfo)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	return models.SaveIdentity(&idinfo)
}

// GetSampleTypes ... get infos
func GetSampleTypes(tcode string) ([]models.TjSampletype, error) {
	return models.FindSampleTypes(tcode)
}
