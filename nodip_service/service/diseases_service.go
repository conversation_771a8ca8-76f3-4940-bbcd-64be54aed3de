package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

// Diseasemodel ...
var Diseasemodel = new(models.DiseaseModel)

// QueryDiseases ... Query
func QueryDiseases(body []byte) (*[]models.TjDiseases, error) {
	var info models.DiseaseDTO

	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("Query :%+v\n", info)
	return Diseasemodel.GetDiseases(&info)
}

// InsertDiseases ... insert
func InsertDiseases(body []byte) (*models.TjDiseases, error) {
	var info = models.TjDiseases{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Diseasemodel.InsertDisease(&info)
}

// UpdateDiseases ... Update
func UpdateDiseases(body []byte) error {
	var info = models.TjDiseases{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Diseasemodel.UpdateDisease(&info)
}

// DeleteDiseases ... Update
func DeleteDiseases(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Diseasemodel.DeleteDisease(id)
}

// **************************** //

// QueryOccucondition ... Query
func QueryOccucondition(body []byte) (*[]models.TjOccucondition, error) {
	var info models.OccuconditionDTO

	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("Query :%+v\n", info)
	return Diseasemodel.GetOccucondition(&info)
}

// InsertOccucondition ... insert
func InsertOccucondition(body []byte) (*models.TjOccucondition, error) {
	var info = models.TjOccucondition{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	ocudto := models.OccuconditionDTO{
		Itemid:    info.TjItemid,
		Poisionid: info.TjPoision,
		Testtype:  info.TjTesttype,
	}

	ret, err := Diseasemodel.GetOccucondition(&ocudto) //根据条件进行查找，
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	if len(*ret) <= 0 {
		log.Printf("insert :%+v\n", info)
		return Diseasemodel.InsertOccucondition(&info)
	}
	info.ID = (*ret)[0].ID
	err = Diseasemodel.UpdateOccucondition(&info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	return &info, nil

}

// UpdateOccucondition ... Update
func UpdateOccucondition(body []byte) error {
	var info = models.TjOccucondition{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Diseasemodel.UpdateOccucondition(&info)
}

// DeleteOccucondition ... Update
func DeleteOccucondition(id int) error {
	// var info = models.TjOccucondition{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Diseasemodel.DeleteOccucondition(id)
}

// *************************** Autodiagcondition BEGIN ******************************

// QueryAutodiagcondition ... Query
func QueryAutodiagcondition(body []byte) (*[]models.TjAutodiagcondition, error) {
	var info = models.AutodiagDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	return Diseasemodel.GetAutodiagcondition(&info)
}

// InsertAutodiagcondition ... insert
func InsertAutodiagcondition(body []byte) (*models.TjAutodiagcondition, error) {
	var info = models.TjAutodiagcondition{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Diseasemodel.InsertAutodiagcondition(&info)
}

// UpdateAutodiagcondition ... Update
func UpdateAutodiagcondition(body []byte) error {
	var info = models.TjAutodiagcondition{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Diseasemodel.UpdateAutodiagcondition(&info)
}

// DeleteAutodiagcondition ... Update
func DeleteAutodiagcondition(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return Diseasemodel.DeleteAutodiagcondition(id)
}

// ************************* Autodiagcondition END **************************
