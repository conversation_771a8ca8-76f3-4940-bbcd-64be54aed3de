package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"utility/logger"

	// "nodip_ego/api/models"
	"time"
)

// Diseasemodel ...
var externalmodel = new(models.ExternalView)

// func ExternalUpload(testid string, isdel bool){
// 	if app.Configurations.External.Exttype == "jhts" {
// 		logger.Log.Infof("开始进行[jhts]得系统对接")
// 		// logger.Log.Infof("Body:%+v", body)
// 		var extservice = new(external.JHTSService)
// 		extservice.UploadData(testid, isdel)
// 	}
// }

// QueryDiseases ... Query
func QueryExternalLisItems(barcode string) (*[]models.VexternalLis, error) {
	if barcode == "" {
		return nil, errors.New("条码号不能为空")
	}

	log.Printf("Query :%+s\n", barcode)
	return externalmodel.GetLisCheckItems(barcode)
}

// QueryExternalpacsItems ... Query
func QueryExternalpacsItems(barcode string) (*[]models.VexternalPacs, error) {
	if barcode == "" {
		return nil, errors.New("条码号不能为空")
	}

	log.Printf("Query :%+s\n", barcode)
	return externalmodel.GetPacsCheckItems(barcode)
}

// UploadExternalpacsItems ... Upload
func UploadExternalLisItems(body []byte) error {
	var info []models.LisDTO
	var reterr error
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("处理数据时发生错误，错误信息：%+v\n", err)
		return err
	}

	labrets := make([]models.TjLabresult, 0)
	testids := make([]string, 0)
	origids := make([]string, 0)

	for _, v := range info {
		testids = append(testids, v.Testid)
		origids = append(origids, v.Itemcode)
	}

	err = labModel.DeleteLabresultbyIDs(testids, origids)
	if err != nil {
		logger.Log.Errorf("处理数据时发生错误，错误信息：%+v\n", err)
		return err
	}

	items, err := Itemsmodel.GetIteminfosbyLisnum(origids)
	if err != nil {
		logger.Log.Errorf("处理数据时发生错误，错误信息：%+v\n", err)
		return err
	}

	for _, v := range info {
		ret, err := ConvertLabDTO2LabResult(&v, items)
		if err != nil {
			logger.Log.Errorf("转换数据时发生错误：%+v", err)
			// haserror = true
			reterr = err
			continue
		}
		labrets = append(labrets, ret...)
	}
	// fmt.Printf("开始插入labresult：%+v\n", labrets)
	_, err = labModel.InsertLabresult(&labrets)
	if err != nil {
		logger.Log.Errorf("处理数据时发生错误，错误信息：%+v\n", err)
		return err
	}
	return reterr
	// if haserror {
	// 	return errors.New("处理上报数据中有部分数据错误，请重试")
	// } else {
	// 	return nil
	// }
}

// UploadExternalpacsItems ... Upload
func UploadExternalPacsItems(body []byte) error {
	var info []models.PacsDTO
	var reterr error
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("处理数据时发生错误，错误信息：%+v\n", err)
		return err
	}
	fmt.Printf("shuju:%+v\n", &info)
	labrets := make([]models.TjPacsresult, 0)
	testids := make([]string, 0)
	origids := make([]string, 0)

	for _, v := range info {
		testids = append(testids, v.Testid)
		origids = append(origids, v.Jclx)
	}

	err = pacsModel.DeletePacsResultbyIDS(testids, origids)
	if err != nil {
		logger.Log.Errorf("处理数据时发生错误，错误信息：%+v\n", err)
		return err
	}

	items, err := Itemsmodel.GetIteminfosbyLisnum(origids)
	fmt.Printf("项目列表：%+v\n", items)
	if err != nil {
		logger.Log.Errorf("处理数据时发生错误，错误信息：%+v\n", err)
		return err
	}

	for _, v := range info {
		ret, err := ConvertDTO2PacsResult(&v, items)
		if err != nil {
			logger.Log.Errorf("转换数据时发生错误：%+v", err)
			// haserror = true
			reterr = err
			continue
		}
		labrets = append(labrets, ret...)
	}
	// fmt.Printf("开始插入labresult：%+v\n", labrets)
	_, err = pacsModel.CreatePacsResults(&labrets)
	if err != nil {
		logger.Log.Errorf("处理数据时发生错误，错误信息：%+v\n", err)
		return err
	}
	return reterr
}

func ConvertLabDTO2LabResult(lis *models.LisDTO, items *[]models.TjIteminfo) ([]models.TjLabresult, error) {
	rets := make([]models.TjLabresult, 0)
	// loc, _ := time.LoadLocation("Asia/Shanghai")
	// tdate, err := time.Parse("2006-01-02 15:04:05", lis.Checkdate)
	tdate, err := time.ParseInLocation("2006-01-02 15:04:05", lis.Checkdate, time.Local)
	if err != nil {
		return nil, err
	}

	for _, v := range *items {
		if v.TjLisnum == lis.Itemcode && v.TjCombineflag == 0 {
			labret := models.TjLabresult{
				ID:              0,
				TjClinicid:      lis.Testid,
				TjTestid:        lis.Testid,
				TjPatientname:   "",
				TjSex:           "",
				TjOrigrec:       lis.Itemcode,
				TjItemid:        v.TjItemid, //需要从数据中获取项目代码
				TjAnalyte:       lis.Itemname,
				TjShortname:     "",
				TjUnits:         "",
				TjFinal:         lis.Result,
				TjRn10:          "",
				TjCkfwL:         lis.Range_l,
				TjCkfwH:         lis.Range_h,
				TjCkfw:          lis.Refrange,
				TjAbnormalflag:  lis.Abnormal,
				TjDisplowhigh:   lis.Displayflag,
				TjSenddate:      tdate.Local().Unix(), //用Checkdate来判断
				TjOrdno:         "",
				TjTestgroup:     "",
				TjCheckdoctor:   lis.Checkdoctor,   // 检查者(直接用名字显示)
				TjRecheckdoctor: lis.Recheckdoctor, // 复查医生(直接用名字显示)
				TjImporter:      lis.Recheckdoctor, // 导入者(直接用名字显示)目前导入者就是复查医生
				TjImportdate:    time.Now().Unix(), // 导入日期
				TjIsreceived:    0,                 // 是否已经接收
				TjReceivdate:    0,                 // 接收日期
			}
			rets = append(rets, labret)
		}
	}

	return rets, nil
}

func ConvertDTO2PacsResult(lis *models.PacsDTO, items *[]models.TjIteminfo) ([]models.TjPacsresult, error) {
	// tdate, err := time.Parse("2006-01-02 15:04:05", lis.Bgrq)
	tdate, err := time.ParseInLocation("2006-01-02 15:04:05", lis.Bgrq, time.Local)
	// loc, _ := time.LoadLocation("Asia/Shanghai")
	// tdate, err := time.ParseInLocation("2006-01-02 15:04:05", lis.Bgrq, loc)
	if err != nil {
		return nil, err
	}
	rets := make([]models.TjPacsresult, 0)
	for _, v := range *items {
		if v.TjLisnum == lis.Jclx && v.TjCombineflag == 0 {
			labret := models.TjPacsresult{
				ID:               0,
				TjTestid:         lis.Testid,
				TjPatientname:    "",
				TjItemid:         v.TjItemid, //需要从数据中获取项目代码
				TjItemname:       lis.Jcxm,
				TjJclx:           lis.Jclx,
				TjJcxm:           lis.Jcxm,
				TjJcmc:           lis.Jcxm,
				TjJcys:           lis.Jcys,
				TjSxys:           "",
				TjBgys:           lis.Bgys,
				TjImporter:       lis.Bgys,
				TjImagesight:     lis.Imagesight,
				TjImagediagnosis: lis.Imagediagnosis,
				TjBgrq:           tdate.Local().Unix(),
				TjImportdate:     time.Now().Unix(), // 导入日期

			}
			rets = append(rets, labret)
		}
	}

	return rets, nil
}
