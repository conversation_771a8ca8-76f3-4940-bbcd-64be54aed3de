package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var groupModel = new(models.GroupModel)

// *************************** Groupinfo BEGIN ******************************

// QueryGroupinfo ... Query
func QueryGroupinfo(id int) (*[]models.TjGroupinfo, error) {
	// var info = models.ItemrefDTO{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	return nil, err
	// }
	return groupModel.GetGroupinfo(id)
}

// InsertGroupinfo ... insert
func InsertGroupinfo(body []byte) (*models.TjGroupinfo, error) {
	var info = models.TjGroupinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return groupModel.InsertGroupinfo(&info)
}

// UpdateGroupinfo ... Update
func UpdateGroupinfo(body []byte) error {
	var info = models.TjGroupinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return groupModel.UpdateGroupinfo(&info)
}

// DeleteGroupinfo ... Update
func DeleteGroupinfo(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return groupModel.DeleteGroupinfo(id)
}

// ************************* Groupinfo END **************************

// *************************** Groupright BEGIN ******************************

// QueryGroupright ... Query
func QueryGroupright(id int) (*[]models.TjGroupright, error) {
	// var info = models.ItemrefDTO{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	return nil, err
	// }
	return groupModel.GetGrouprights(id)
}

// InsertGroupright ... insert
func InsertGroupright(body []byte) (*[]models.TjGroupright, error) {
	var info []models.TjGroupright
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return groupModel.InsertGroupright(&info)
}

// UpdateGroupright ... Update
func UpdateGroupright(body []byte) error {
	var info = models.TjGroupright{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return groupModel.UpdateGroupright(&info)
}

// DeleteGroupright ... Update
func DeleteGroupright(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return groupModel.DeleteGroupright(id)
}

// ************************* Groupright END **************************
