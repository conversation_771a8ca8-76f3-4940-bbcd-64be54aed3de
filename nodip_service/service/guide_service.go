package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

// Guidemodel ...
var Guidemodel = new(models.GuideModel)

// QueryGuideinfo ... Query
func QueryGuideinfo(gid int) (*[]models.TjGuideinfo, error) {

	if gid < 0 {
		return nil, errors.New("ID不能小于等于0")
	}

	return Guidemodel.Query(gid)
}

// InsertGuideinfo ... insert
func InsertGuideinfo(body []byte) (*models.TjGuideinfo, error) {
	var info = models.TjGuideinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.<PERSON>rf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Guidemodel.Insert(&info)
}

// UpdateGuideinfo ... Update
func UpdateGuideinfo(body []byte) error {
	var info = models.TjGuideinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Guidemodel.Update(&info)
}

// DeleteGuideinfo ... Update
func DeleteGuideinfo(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Guidemodel.Delete(id)
}

// ****************** guide item *****************************

// Guideitemmodel ...
var Guideitemmodel = new(models.GuideItemModel)

// QueryGuideitem ... Query
func QueryGuideitem(gid int) (*[]models.TjGuideitem, error) {

	if gid < 0 {
		return nil, errors.New("ID不能小于0")
	}

	return Guideitemmodel.Query(gid)
}

// InsertGuideitem ... insert
func InsertGuideitem(body []byte) (*[]models.TjGuideitem, error) {
	var info []models.TjGuideitem
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Guideitemmodel.Insert(&info)
}

// UpdateGuideitem ... Update
func UpdateGuideitem(body []byte) error {
	var info = models.TjGuideitem{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Guideitemmodel.Update(&info)
}

// DeleteGuideitem ... Update
func DeleteGuideitem(body []byte) error {

	var dto models.GuideitemDTO
	err := json.Unmarshal(body, &dto)

	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	if dto.Guideid <= 0 && dto.Itemid == "" {
		return errors.New("不能全部为空")
	}

	return Guideitemmodel.Delete(&dto)
}
