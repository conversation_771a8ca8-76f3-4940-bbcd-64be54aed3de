package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

// Hazardmodel ...
var Hazardmodel = new(models.HazardModel)

// *************************** hazardinfo BEGIN ******************************

// QueryHazardinfo ... Query
func QueryHazardinfo(body []byte) (*[]models.TjHazardinfo, error) {
	var info = models.HazardDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	return Hazardmodel.GetHazardinfo(&info)
}

// InsertHazardinfo ... insert
func InsertHazardinfo(body []byte) (*models.TjHazardinfo, error) {
	var info = models.TjHazardinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Hazardmodel.InsertHazardinfo(&info)
}

// UpdateHazardinfo ... Update
func UpdateHazardinfo(body []byte) error {
	var info = models.TjHazardinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Hazardmodel.UpdateHazardinfo(&info)
}

// DeleteHazardinfo ... Update
func DeleteHazardinfo(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Hazardmodel.DeleteHazardinfo(id)
}

// ************************* END **************************

// *************************** hazarditem BEGIN ******************************

// QueryHazarditem ... Query
func QueryHazarditem(body []byte) (*[]models.TjHazarditem, error) {
	var info = models.HazardDIDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	return Hazardmodel.GetHazarditem(&info)
}

// InsertHazarditem ... insert
func InsertHazarditem(body []byte) (*models.TjHazarditem, error) {
	var info = models.TjHazarditem{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	hddidto := models.HazardDIDTO{
		Itemid: info.TjItemid,
		Hids:   []int{info.TjHid},
		Type:   info.TjTesttype,
	}

	ret, err := Hazardmodel.GetHazarditem(&hddidto)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	if len(*ret) <= 0 {
		log.Printf("insert :%+v\n", info)
		return Hazardmodel.InsertHazarditem(&info)
	}
	//already existed
	return &info, nil
}

// UpdateHazarditem ... Update
func UpdateHazarditem(body []byte) error {
	var info = models.TjHazarditem{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Hazardmodel.UpdateHazarditem(&info)
}

// DeleteHazarditem ... Update
func DeleteHazarditem(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Hazardmodel.DeleteHazarditem(id)
}

// ************************* END **************************

// *************************** hazardtype BEGIN ******************************

// QueryHazardtype ... Query
func QueryHazardtype(id int) (*[]models.TjHazardtype, error) {

	return Hazardmodel.GetHazardtype(id)
}

// InsertHazardtype ... insert
func InsertHazardtype(body []byte) (*models.TjHazardtype, error) {
	var info = models.TjHazardtype{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Hazardmodel.InsertHazardtype(&info)
}

// UpdateHazardtype ... Update
func UpdateHazardtype(body []byte) error {
	var info = models.TjHazardtype{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Hazardmodel.UpdateHazardtype(&info)
}

// DeleteHazardtype ... Update
func DeleteHazardtype(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Hazardmodel.DeleteHazardtype(id)
}

// ************************* hazardtype END **************************

// *************************** hazarddisease BEGIN ******************************

// QueryHazarddisease ... Query
func QueryHazarddisease(body []byte) (*[]models.TjHazarddisease, error) {
	var info = models.HazardDIDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	return Hazardmodel.GetHazarddisease(&info)
}

// InsertHazarddisease ... insert
func InsertHazarddisease(body []byte) (*models.TjHazarddisease, error) {
	var info = models.TjHazarddisease{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Hazardmodel.InsertHazarddisease(&info)
}

// UpdateHazarddisease ... Update
func UpdateHazarddisease(body []byte) error {
	var info = models.TjHazarddisease{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Hazardmodel.UpdateHazarddisease(&info)
}

// DeleteHazarddisease ... Update
func DeleteHazarddisease(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Hazardmodel.DeleteHazarddisease(id)
}

// ************************* hazarddisease END **************************
