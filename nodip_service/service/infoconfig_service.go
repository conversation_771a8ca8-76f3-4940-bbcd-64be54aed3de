package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"utility/logger"
	// "nodip_ego/api/models"
)

// InfoModel ...
var InfoModel = new(models.InfoModel)

// GetInfoconfigs ... get infos
func GetInfoconfigs(body []byte) (*[]models.SsInfoconfig, error) {
	var info = models.KeysIntDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	return InfoModel.GetInfoConfigs(info.KeyIDs)
}

// GetInfoconfig ... get info
func GetInfoconfig(scode string) (*models.SsInfoconfig, error) {
	return InfoModel.GetInfoConfig(scode)
}

// GetInfoConfigByName ... get info
func GetInfoConfigByName(scode string) (*models.SsInfoconfig, error) {
	return InfoModel.GetInfoConfigByName(scode)
}

// GetAreainfo ... get area info by code
func GetAreainfo(code, localarea string) (*[]models.SsArea, error) {
	if code == "" {
		return nil, errors.New("code为空")
	}
	// fmt.Printf("开始获取地区信息，代码：%s\n", code)
	return InfoModel.GetAreaInfo(code, localarea)
}

// GetAreainfos ... get area infos
func GetAreainfos(pcode string) (*[]models.SsArea, error) {
	// fmt.Printf("开始根据父代码获取地区信息，父代码：%s\n", pcode)
	// logger.Log.Infof("开始根据父代码获取地区信息，父代码：: %s", pcode)
	return InfoModel.GetAreaInfosbyParentcode(pcode)
}
