package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var instrumentModel = new(models.InstrumentModel)

// *************************** Instrumentinfo BEGIN ******************************

// QueryInstrumentinfo ... Query
func QueryInstrumentinfo(struid string) (*[]models.TjInstrumentinfo, error) {

	return instrumentModel.GetInstrumentinfo(struid)
}

// InsertInstrumentinfo ... insert
func InsertInstrumentinfo(body []byte) (*models.TjInstrumentinfo, error) {
	var info = models.TjInstrumentinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	ret, err := instrumentModel.GetInstrumentinfo(info.TjStruid)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	if len(*ret) > 0 {
		return nil, errors.New("该仪器编号已经存在")
	}

	log.Printf("insert :%+v\n", info)
	return instrumentModel.InsertInstrumentinfo(&info)
}

// UpdateInstrumentinfo ... Update
func UpdateInstrumentinfo(body []byte) error {
	var info = models.TjInstrumentinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return instrumentModel.UpdateInstrumentinfo(&info)
}

// DeleteInstrumentinfo ... Update
func DeleteInstrumentinfo(struid string) error {
	// var info = models.TjDiseasesnew{}
	if struid == "" || struid == "0" {
		return errors.New("id不能为kong")
	}

	return instrumentModel.DeleteInstrumentinfo(struid)
}

// ************************* Instrumentinfo END **************************

// *************************** Itemrefinfo BEGIN ******************************

// QueryItemrefinfo ... Query
func QueryItemrefinfo(body []byte) (*[]models.TjItemrefinfo, error) {
	var info = models.ItemrefDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	return Itemsmodel.GetItemrefinfos(&info)
}

// InsertItemrefinfo ... insert
func InsertItemrefinfo(body []byte) (*models.TjItemrefinfo, error) {
	var info = models.TjItemrefinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Itemsmodel.InsertItemrefinfo(&info)
}

// UpdateItemrefinfo ... Update
func UpdateItemrefinfo(body []byte) error {
	var info = models.TjItemrefinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Itemsmodel.UpdateItemrefinfo(&info)
}

// DeleteItemrefinfo ... Update
func DeleteItemrefinfo(body []byte) error {
	var info = models.ItemrefDelDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	if info.ID <= 0 && info.Struid == "" {
		return errors.New("删除条件为空，不能删除")
	}
	return Itemsmodel.DeleteItemrefinfo(&info)
}

// ************************* Itemrefinfo END **************************
