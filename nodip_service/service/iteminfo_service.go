package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

// Itemsmodel ...
var Itemsmodel = new(models.ItemsModel)

// *************************** Itemtype BEGIN ******************************

// QueryItemtype ... Query
func QueryItemtype(id int) (*[]models.TjItemtype, error) {

	return Itemsmodel.GetItemtypes(id)
}

// InsertItemtype ... insert
func InsertItemtype(body []byte) (*models.TjItemtype, error) {
	var info = models.TjItemtype{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Itemsmodel.InsertItemtype(&info)
}

// UpdateItemtype ... Update
func UpdateItemtype(body []byte) error {
	var info = models.TjItemtype{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return err
	}

	logger.Log.Infof("update :%+v\n", info)
	return Itemsmodel.UpdateItemtype(&info)
}

// DeleteItemtype ... Update
func DeleteItemtype(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Itemsmodel.DeleteItemtype(id)
}

// ************************* Itemtype END **************************

// ************************* Iteminfo BEGIN ******************************

// QueryIteminfos ...
func QueryAllIteminfos(sflag string) (*[]models.TjIteminfo, error) {
	// var keys = models.KeysDTO{}
	// err := json.Unmarshal(body, &keys)
	// if err != nil {
	// 	logger.Log.Infof("错误: %+v", err)
	// 	return nil, err
	// }
	// var keyids []string
	return Itemsmodel.GetIteminfobyFlags(sflag)
}

// QueryIteminfos ...
func QueryIteminfos(body []byte) ([]models.TjIteminfo, error) {
	var keys = models.KeysDTO{}
	err := json.Unmarshal(body, &keys)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return Itemsmodel.GetIteminfos(keys.KeyIDs)
}

// CreateIteminfos ...
func CreateIteminfos(body []byte) (*models.TjIteminfo, error) {
	var info = models.TjIteminfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	return Itemsmodel.InsertIteminfo(&info)
}

// UpdateIteminfo ... Update
func UpdateIteminfo(body []byte) error {
	var info = models.TjIteminfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return err
	}
	return Itemsmodel.UpdateIteminfo(&info)
}

// DeleteIteminfo ... Update
func DeleteIteminfo(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Itemsmodel.DeleteIteminfo(id)
}

// ************************* Iteminfo END ******************************

// *************************** TjCombineinfo BEGIN ******************************

// QueryCombineinfos ... Query
func QueryCombineinfos(body []byte) (*[]models.TjCombineinfo, error) {
	var info = models.KeysDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}
	return Itemsmodel.GetCombineinfo(info.KeyIDs)
}

// InsertCombineinfo ... insert
func InsertCombineinfo(body []byte) (*[]models.TjCombineinfo, error) {
	var info []models.TjCombineinfo
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Itemsmodel.InsertCombineinfo(&info)
}

// UpdateCombineinfo ... Update
func UpdateCombineinfo(body []byte) error {
	var info = models.TjCombineinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Itemsmodel.UpdateCombineinfo(&info)
}

// DeleteCombineinfo ... Delete
func DeleteCombineinfo(body []byte) error {
	var dto = models.ItemdelDTO{}
	err := json.Unmarshal(body, &dto)
	if err != nil {
		logger.Log.Infof("错误: %+v", err)
		return err
	}

	if dto.Combid == "" && dto.Itemid == "" {
		return errors.New("两个删除条件都为空，不能删除")
	}

	return Itemsmodel.DeleteCombineinfo(&dto)
}

// ************************* TjCombineinfo END **************************

// *************************** Itemresultinfo BEGIN ******************************

// QueryItemresultinfo ... Query
func QueryItemresultinfo(itemid string) (*[]models.TjItemresultinfo, error) {

	return Itemsmodel.GetItemresultinfo(itemid)
}

// InsertItemresultinfo ... insert
func InsertItemresultinfo(body []byte) (*models.TjItemresultinfo, error) {
	var info = models.TjItemresultinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return Itemsmodel.InsertItemresultinfo(&info)
}

// UpdateItemresultinfo ... Update
func UpdateItemresultinfo(body []byte) error {
	var info = models.TjItemresultinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return Itemsmodel.UpdateItemresultinfo(&info)
}

// DeleteItemresultinfo ... Update
func DeleteItemresultinfo(id int) error {
	// var info = models.TjDiseasesnew{}
	if id <= 0 {
		return errors.New("id不能为0")
	}

	return Itemsmodel.DeleteItemresultinfo(id)
}

// ************************* Itemresultinfo END **************************
