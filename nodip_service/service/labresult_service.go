package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var labModel = new(models.LabModel)

// *************************** Labresult BEGIN ******************************

// QueryLabresult ... Query
func QueryLabresult(body []byte) (*[]models.TjLabresult, error) {
	var info = models.LabresultDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}
	return labModel.GetLabresult(&info)
}

// InsertLabresult ... insert
func InsertLabresult(body []byte) (*[]models.TjLabresult, error) {
	var info []models.TjLabresult
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return labModel.InsertLabresult(&info)
}

// // UpdateLabresult ... Update
// func UpdateLabresult(body []byte) error {
// 	var info = models.TjLabresult{}
// 	err := json.Unmarshal(body, &info)
// 	if err != nil {
// 		return err
// 	}

// 	log.Printf("update :%+v\n", info)
// 	return labModel.UpdateLabresult(&info)
// }

// DeleteLabresult ... Update
func DeleteLabresult(body []byte) error {
	var info = models.KeysDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息：%+v\n", err)
		return err
	}
	if len(info.KeyIDs) <= 0 {
		return errors.New("testids are empty,not allowed to delete")
	}
	return labModel.DeleteLabresult(info.KeyIDs)
}

// ************************* Labresult END **************************
