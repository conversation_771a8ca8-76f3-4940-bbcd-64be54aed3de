package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"utility/logger"

	// "nodip_ego/api/models"
	"strconv"
	"strings"
)

var medexamin = new(models.MedexaminfoModel)

func RegisterMedexaminfo(info []byte) error {
	return nil
}

func FindMedexaminfo(medinfos []models.TjMedexaminfo, testid string) *models.TjMedexaminfo {

	for _, val := range medinfos {
		if val.TjTestid == testid {
			return &val
		}
	}

	return nil
}

// InsertMedinfo ... insert new medexaminfo
func InsertMedinfo(info []byte) (*models.TjMedexaminfo, error) {
	var medinfo = models.TjMedexaminfo{}
	err := json.Unmarshal(info, &medinfo)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	// logger.Log.Infof("Medinfo(json):%+v\n", medinfo)
	// ExternalUpload(medinfo.TjTestid,false)
	logger.Log.Infof("需要写入的体检信息:%+v\n", (medinfo))
	return medexamin.CreateMedinfo(&medinfo)
}

// UpdateMedinfo ... UpdateMedinfo new medexaminfo
func UpdateMedinfo(info []byte) (*models.TjMedexaminfo, error) {
	var medinfo = models.TjMedexaminfo{}
	err := json.Unmarshal(info, &medinfo)
	if err != nil {
		return nil, err
	}
	// logger.Log.Infof("Medinfo(json):%+v\n", medinfo)
	// ExternalUpload(medinfo.TjTestid,false)
	logger.Log.Infof("需要更新的体检信息:%+v\n", (medinfo))
	return medexamin.UpdateMedinfo(&medinfo)
}

// BatchUpdateMedPoisioninfo ... UpdateMedinfo new medexaminfo
func BatchUpdateMedPoisioninfo(info []byte) (*models.TjMedexaminfo, error) {

	logger.Log.Infof("接收到的信息%+v\n", (string(info)))
	var ninfo = models.MedinfoPoisionDTO{}
	err := json.Unmarshal(info, &ninfo)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	logger.Log.Infof("需要修改的体检者信息%+v\n", (ninfo))
	m, err := QueryMedinfoByTestID(ninfo.Medinfo.TjTestid)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	if len(m) <= 0 {
		return nil, errors.New("没有该体检编号的体检者信息")
	}
	logger.Log.Infof("体检者信息%+v\n", (m))
	// ninfo.Medinfo = (*m)[0]
	var pfactor string
	tids := make([]string, 0)
	tids = append(tids, ninfo.Medinfo.TjTestid)
	patient.DeletePatientHazards(tids) //先删除该体检者所有的毒害因素信息
	pfactor = ""
	phinfos := make([]models.TjPatienthazards, len(ninfo.Hazardinfo))
	// var phinfos []models.TjPatienthazards
	for idx, hdinfo := range ninfo.Hazardinfo {
		var ptinfo models.TjPatienthazards
		ptinfo.ID = 0
		ptinfo.TjTestid = ninfo.Medinfo.TjTestid
		ptinfo.TjHid = hdinfo.ID
		ptinfo.TjPoisionage = ninfo.Medinfo.TjPoisionage
		// logger.Log.Infof("毒害因素信息：%+v\n", ptinfo)
		phinfos[idx] = ptinfo

		pfactor += hdinfo.TjHname
		if idx != len(ninfo.Hazardinfo)-1 {
			pfactor += "、"
		}
	}
	patient.CreatePatientHazards(&phinfos) // 插入毒害因素列表
	// ninfo.Medinfo.TjPoisionfactor = pfactor
	m[0].TjPoisionfactor = pfactor

	logger.Log.Infof("采用新的毒害因素更新体检这信息:%+v\n", m[0])
	medexamin.UpdateMedinfo(&m[0])
	return &ninfo.Medinfo, nil
}

// UpdateMedinfoStatus ... UpdateMedinfo new medexaminfo
func UpdateMedinfoStatus(testid string, status string) error {

	if testid == "" {
		return errors.New("体检编号不能为空")
	}
	if status == "" {
		return errors.New("新状态不能为空")
	}

	istatus, err := strconv.Atoi(status)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	return medexamin.UpdateMedinfoStatus(testid, istatus)
}

// UpdateMedinfoAppointment ... UpdateMedinfo new medexaminfo
func UpdateMedinfoAppointment(peid string, status string, testdate string) error {

	if peid == "" {
		return errors.New("peid不能为空")
	}
	ipeid, err := strconv.Atoi(peid)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	if status == "" {
		return errors.New("status不能为空")
	}
	istatus, err := strconv.Atoi(status)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	if testdate == "" {
		return errors.New("体检日期不能为空")
	}

	itestdate, err := strconv.Atoi(testdate)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	return medexamin.UpdateMedinfoAppointStatus(ipeid, istatus, itestdate)
}

// QueryMedinfoByTestID ... get infos
func QueryMedinfoByTestID(stestid string) ([]models.TjMedexaminfo, error) {

	strarr := strings.Split(stestid, ",")
	return medexamin.FindMedinfoByTestID(strarr)
}

// QueryMedinfo ...
func QueryMedinfo(dat models.MedQueryDTO) ([]models.TjMedexaminfo, error) {

	logger.Log.Infof("query condition: %+v", dat)
	return medexamin.FindMedinfo(&dat)
	// return models.FindMedinfo(dat.Dtstart, dat.Dtend, testids, dat.Cpme, dat.Corpid, dat.Testtype, dat.Statuslow, dat.Statushigh, pids, dat.Pname, dat.Isrecheck, dat.Rptnum)
}

// RemoveMedinfo ... UpdateMedinfo new medexaminfo
func RemoveMedinfo(body []byte) error {

	var keys = models.KeysDTO{}
	err := json.Unmarshal(body, &keys)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}
	logger.Log.Infof("删除体检信息，体检编号：%+v", keys.KeyIDs)

	err = medexamin.DeleteMedinfo(keys.KeyIDs)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	//减少复查次数
	//从复查登记表里删除体检信息
	err = reportModel.DeleteCorpoccureportFc(keys.KeyIDs)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}
	return nil
}

// UpdateMedinfoReportStatus ... UpdateMedinfoReportStatus
func UpdateMedinfoReportStatus(body []byte) error {

	var info = models.MedReportDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}
	logger.Log.Infof("update 体检信息： %+v", info)
	// testid := strings.Split(testids, ",")
	// if err != nil {
	// 	return err
	// }
	return medexamin.UpdateMedinfoReportStatus(&info)
}
