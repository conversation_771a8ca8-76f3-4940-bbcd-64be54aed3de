package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var packageModel = new(models.PackageModel)

// *************************** Packageinfo BEGIN ******************************

// QueryPackageinfo ... Query
func QueryPackageinfo(pid string) (*[]models.TjPackageinfo, error) {
	// var info = models.ItemrefDTO{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	return nil, err
	// }
	return packageModel.GetPackageinfo(pid)
}

// InsertPackageinfo ... insert
func InsertPackageinfo(body []byte) (*models.TjPackageinfo, error) {
	var info = models.TjPackageinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	info.ID = 0 //force set id to 0 for insert
	log.Printf("insert :%+v\n", info)
	return packageModel.InsertPackageinfo(&info)
}

// UpdatePackageinfo ... Update
func UpdatePackageinfo(body []byte) error {
	var info = models.TjPackageinfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return packageModel.UpdatePackageinfo(&info)
}

// DeletePackageinfo ... Update
func DeletePackageinfo(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return packageModel.DeletePackageinfo(id)
}

// ************************* Packageinfo END **************************

// *************************** Packagedetail BEGIN ******************************

// QueryPackagedetail ... Query
func QueryPackagedetail(id int) (*[]models.TjPackagedetail, error) {
	if id <= 0 {
		return nil, errors.New("id为0，不能查找")
	}
	return packageModel.GetPackagedetails(id)
}

// InsertPackagedetail ... insert
func InsertPackagedetail(body []byte) (*models.TjPackagedetail, error) {
	var info = models.TjPackagedetail{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	info.ID = 0
	log.Printf("insert :%+v\n", info)
	return packageModel.InsertPackagedetail(&info)
}

// UpdatePackagedetail ... Update
func UpdatePackagedetail(body []byte) error {
	var info = models.TjPackagedetail{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}
	log.Printf("update :%+v\n", info)
	return packageModel.UpdatePackagedetail(&info)
}

// DeletePackagedetail ... Update
func DeletePackagedetail(body []byte) error {
	var info = models.PackagedelDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	if info.ID <= 0 && info.Pnum <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return packageModel.DeletePackagedetail(&info)
}

// ************************* Packagedetail END **************************
