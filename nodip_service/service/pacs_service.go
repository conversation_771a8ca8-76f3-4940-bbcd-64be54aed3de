package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"utility/logger"
	// "nodip_ego/api/models"
)

var pacsModel = new(models.PacsModel)

// QueryPacsResults ...
func QueryPacsResults(testid string) (*[]models.TjPacsresult, error) {

	if testid == "" {
		return nil, errors.New("体检编号不能为空")
	}

	return pacsModel.FindPacsResults(testid)
}

// InsertPacsResults ...
func InsertPacsResults(body []byte) (*[]models.TjPacsresult, error) {

	var infos []models.TjPacsresult
	err := json.Unmarshal(body, &infos)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	return pacsModel.CreatePacsResults(&infos)
}

// DeletePacsResults ...
func DeletePacsResults(testid string) error {

	if testid == "" {
		return errors.New("体检编号不能为空")
	}

	return pacsModel.DeletePacsResult(testid)
}
