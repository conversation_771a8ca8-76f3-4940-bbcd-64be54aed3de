package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"

	// "nodip_ego/api/models"
	"strings"
)

var patient = new(models.PatientModel)

func FindPatientinfo(ptinfos []models.TjPatient, pid string) *models.TjPatient {

	for _, val := range ptinfos {
		if val.TjPid == pid {
			return &val
		}
	}

	return nil
}

// InsertPatientinfo ... insert
func InsertPatientinfo(body []byte) (*models.TjPatient, error) {
	var info = models.TjPatient{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	if info.TjPidcard == "" {
		log.Printf("身份证为空，直接插入新的体检者信息")
		return patient.CreatePatientinfo(&info)
	}
	idcard := []string{info.TjPidcard}
	ret, err := patient.GetPatientsbyIdcard(&idcard)
	if err != nil || len(*ret) <= 0 {
		log.Printf("不能根据身份证号获取体检者信息，插入新的体检者信息")
		return patient.CreatePatientinfo(&info)
	}

	info.ID = (*ret)[0].ID
	log.Printf("身份证号已经存在，更新体检者信息")
	return patient.UpdatePatientinfo(&info)

}

// UpdatePatientinfo ... UpdatePatientinfo new medexaminfo
func UpdatePatientinfo(info []byte) (*models.TjPatient, error) {
	var medinfo = models.TjPatient{}
	err := json.Unmarshal(info, &medinfo)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	// log.Printf("Medinfo(json):%+v\n", medinfo)
	return patient.UpdatePatientinfo(&medinfo)
}

// QueryPatients ... get infos
func QueryPatients(ids models.PtQueryDTO) ([]models.TjPatient, error) {

	// log.Printf("ids:%+v\n", ids.Key)
	// strarr := strings.Split(pids, ",")
	return patient.GetPatients(&ids)
}

// QueryPatientbyIdcard ... GetPatientbyIdcard
func QueryPatientbyIdcard(pval string) (*[]models.TjPatient, error) {
	strarr := strings.Split(pval, ",")
	return patient.GetPatientsbyIdcard(&strarr)
}

// InsertPatientHazards ... insert
func InsertPatientHazards(body []byte) (*[]models.TjPatienthazards, error) {
	var infos []models.TjPatienthazards
	err := json.Unmarshal(body, &infos)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	// log.Printf("Medinfo(json):%+v\n", medinfo)
	var insert_infos []models.TjPatienthazards
	for idx := range infos {
		if infos[idx].ID == 0 {
			insert_infos = append(insert_infos, infos[idx])
		} else {
			patient.UpdatePatientHazards(&infos[idx])
		}
	}
	if len(insert_infos) > 0 {
		return patient.CreatePatientHazards(&insert_infos)
	} else {
		return &infos, nil
	}
}

// InsertPatientHazards ... insert
func UpdatePatientHazards(info *models.TjPatienthazards) error {
	return patient.UpdatePatientHazards(info)
}

// QueryPatientHazards ... chazhao
func QueryPatientHazards(body []byte) ([]models.TjPatienthazards, error) {
	var keys models.KeysDTO
	err := json.Unmarshal(body, &keys)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	hds, err := patient.GetPatientHazards(keys.KeyIDs)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	// logger.Log.Infof("结果数据:%+v", hds)
	// uncheck := true
	// for _, val := range hds {
	// 	if val.TjTypeid != comdef.Normal {
	// 		uncheck = false
	// 		break
	// 	}
	// }
	logger.Log.Infof("返回数据......%+v", hds)
	return hds, err
}

// RemovePatientHazards ...
func RemovePatientHazards(body []byte) error {
	var keys models.KeysDTO
	err := json.Unmarshal(body, &keys)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil
	}
	if len(keys.KeyIDs) <= 0 {
		return errors.New("体检号不能为空")
	}
	return patient.DeletePatientHazards(keys.KeyIDs)
}

//******************** patient diseases ********************

// InsertPatientDiseases ... insert
func InsertPatientDiseases(body []byte) (*[]models.TjDiseaseinfo, error) {
	var info []models.TjDiseaseinfo
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	if len(info) <= 0 {
		return nil, errors.New("疾病列表为空")
	}
	//先删除这些疾病信息
	disids := make([]int, 0)
	testids := make([]string, 0)
	for _, v := range info {
		disids = append(disids, v.TjDisid)
		testids = append(testids, v.TjTestid)
	}

	log.Printf("需要删除的疾病信息, testids:%+v, disids:%+v\n", testids, disids)
	//先删除所有的疾病信息
	err = patient.DeletePatientDisease(testids, disids)
	if err != nil {
		return nil, err
	}
	//再插入疾病信息
	// log.Printf("需要插入的疾病信息, %+v\n", &info)
	ret, err := patient.CreatePatientDiseases(&info)

	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	//再插入疾病信息
	log.Printf("插入成功后的疾病信息, %+v\n", &info)
	return ret, nil
}

// UpdatePatientDiseases ... UpdatePatientDiseases
func UpdatePatientDiseases(body []byte) (*models.TjDiseaseinfo, error) {
	var info models.TjDiseaseinfo
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	// log.Printf("Medinfo(json):%+v\n", medinfo)
	return patient.SavePatientDiseases(&info)
}

// QueryPatientDiseases ... chazhao
func QueryPatientDiseases(body []byte) (*[]models.TjDiseaseinfo, error) {
	var keys models.PatientDiseaseDTO
	err := json.Unmarshal(body, &keys)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	return patient.GetPatientDiseases(&keys)
	// return nil, nil
}

// RemovePatientDiseases ...
func RemovePatientDiseases(body []byte) error {
	var keys models.PatientDiseaseDTO
	err := json.Unmarshal(body, &keys)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil
	}
	// if len(keys.KeyIDs) <= 0 {
	// 	return errors.New("体检号不能为空")
	// }
	return patient.DeletePatientDiseases(&keys)
}
