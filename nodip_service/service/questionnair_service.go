package service

import (
	"datacontroller/models"
	"nodip_ego/api/app"
	"utility/logger"
	// "nodip_ego/api/models"
)

var questionnair = new(models.QuestionnairModel)

//根据体检编号，档案编号查找问卷信息
//先根据体检编号查找问卷信息，如果没有，则根据档案号查找历史问卷信息
func QueryQuestionnair(testid, pid string) (models.TjHealthyInfo, []models.TjOccupationHistory, []models.TjDiseaseHistory, []models.TjMarriageHistory, error) {
	logger.Log.Infof("开始根据体检号:%s，pid:%s查找问卷信息", testid, pid)
	//query
	var healthyinfo models.TjHealthyInfo
	var ocuhistory []models.TjOccupationHistory
	var dishistory []models.TjDiseaseHistory
	var marriage []models.TjMarriageHistory

	healthyinfo, err := FindHealthyinfo(testid)
	if err != nil && err.Error() != app.ERROR_RECORDNOTFOUND {
		return healthyinfo, ocuhistory, dishistory, marriage, err
	}

	if err != nil && err.Error() == app.ERROR_RECORDNOTFOUND {
		if pid == "" {
			logger.Log.Infof("不能找到体检编号为%s的问卷信息,pid为空，返回结果", testid)
			return healthyinfo, ocuhistory, dishistory, marriage, err
		}
		logger.Log.Infof("不能找到体检编号为%s的问卷信息,需要根据pid:%s去查找", testid, pid)

		healthyinfo, err = questionnair.QueryQuestionnairInfobyPid(pid)
		logger.Log.Infof("根据档案号%s的查找结果:%+v, %+v", pid, healthyinfo, err)
		if err != nil {
			if err.Error() != app.ERROR_RECORDNOTFOUND {
				logger.Log.Errorf("查找时发生错误，错误信息:%+v", err)
				return healthyinfo, ocuhistory, dishistory, marriage, err
			} else {
				logger.Log.Infof("找不到任何健康调查信息,返回空")
				return healthyinfo, ocuhistory, dishistory, marriage, nil
			}
		}
	}

	//找不到任何问卷信息
	// if healthyinfo == nil {
	// 	logger.Log.Infof("找不到体检号为:%s的任何健康问卷信息...")
	// 	return healthyinfo, ocuhistory, dishistory, marriage, nil
	// }

	//职业史
	ocuhistory, err = FindOccupationHistories(healthyinfo.TjTestID)
	logger.Log.Infof("职业史:%+v", ocuhistory)
	if err != nil && err.Error() != app.ERROR_RECORDNOTFOUND {
		logger.Log.Errorf("FindOccupationHistories error:%+v", err)
	}
	//疾病史
	dishistory, err = FindDiseaseHistories(healthyinfo.TjTestID)
	logger.Log.Infof("疾病史:%+v", dishistory)
	if err != nil && err.Error() != app.ERROR_RECORDNOTFOUND {
		logger.Log.Errorf("FindDiseaseHistories error:%+v", err)
	}
	//婚姻史
	marriage, err = FindMarriageHistories(healthyinfo.TjTestID)
	logger.Log.Infof("婚姻史:%+v", marriage)
	if err != nil && err.Error() != app.ERROR_RECORDNOTFOUND {
		logger.Log.Errorf("FindMarriageHistories error:%+v", err)
	}

	return healthyinfo, ocuhistory, dishistory, marriage, nil
}

func InsertQuestionnairInfo(nairinfo *models.QuestionnairModel) error {
	if nairinfo == nil {
		return nil
	}
	//先根据体检编号清除数据，然后做批量写入
	DeleteHealthyInfo(nairinfo.Healthyinfo.TjTestID)
	DeleteOccupationHistories(nairinfo.Healthyinfo.TjTestID)
	DeleteDiseaseHistories(nairinfo.Healthyinfo.TjTestID)
	DeleteMarriageHistories(nairinfo.Healthyinfo.TjTestID)
	//do insert

	SaveHealthyInfo(&nairinfo.Healthyinfo)
	SaveOccupationHistories(nairinfo.Healthyinfo.TjTestID, nairinfo.Ocuhis)
	SaveDiseaseHistories(nairinfo.Healthyinfo.TjTestID, nairinfo.Dishis)
	SaveMarriageHistories(nairinfo.Healthyinfo.TjTestID, nairinfo.Marriage)
	return nil
}

//问卷信息
func FindHealthyinfo(testid string) (models.TjHealthyInfo, error) {

	// questionnair.
	return questionnair.QueryQuestionnairinfo(testid)

}

func SaveHealthyInfo(info *models.TjHealthyInfo) error {
	// return questionnair.
	err := questionnair.DeleteQuestionnairinfo(info.TjTestID)
	if err != nil {
		return err
	}

	_, err = questionnair.CreateQuestionnairinfo(info)
	return err

}

func DeleteHealthyInfo(testid string) error {
	return questionnair.DeleteQuestionnairinfo(testid)
}

//职业史信息
func FindOccupationHistories(testid string) ([]models.TjOccupationHistory, error) {
	return questionnair.QueryOccupationHistories(testid)
}

func SaveOccupationHistories(testid string, infos []models.TjOccupationHistory) (*[]models.TjOccupationHistory, error) {
	err := questionnair.DeleteOccupationHistories(testid)
	if err != nil {
		return nil, err
	}
	ret, err := questionnair.CreateOccupationHistories(infos)
	if err != nil {
		return ret, err
	}

	return ret, nil
}

func DeleteOccupationHistories(testid string) error {
	return questionnair.DeleteOccupationHistories(testid)
}

//既往史（包括职业病史）
func FindDiseaseHistories(testid string) ([]models.TjDiseaseHistory, error) {
	return questionnair.QueryDiseaseHistories(testid)
}

func SaveDiseaseHistories(testid string, infos []models.TjDiseaseHistory) (*[]models.TjDiseaseHistory, error) {
	err := questionnair.DeleteMarriageHistories(testid)
	if err != nil {
		return nil, err
	}
	ret, err := questionnair.CreateDiseaseHistories(infos)
	if err != nil {
		return ret, err
	}

	return ret, nil
}

func DeleteDiseaseHistories(testid string) error {
	return questionnair.DeleteDiseaseHistories(testid)
}

//婚姻史
func FindMarriageHistories(testid string) ([]models.TjMarriageHistory, error) {
	return questionnair.QueryMarriageHistories(testid)
}

func SaveMarriageHistories(testid string, infos []models.TjMarriageHistory) (*[]models.TjMarriageHistory, error) {
	err := questionnair.DeleteMarriageHistories(testid)
	if err != nil {
		return nil, err
	}

	ret, err := questionnair.CreateMarriageHistories(infos)
	if err != nil {
		return ret, err
	}

	return ret, nil
}

func DeleteMarriageHistories(testid string) error {
	return questionnair.DeleteMarriageHistories(testid)
}
