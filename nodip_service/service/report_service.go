package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var reportModel = new(models.ReportModel)

// *************************** Corpoccureport BEGIN ******************************

// QueryCorpoccureport ... Query
func QueryCorpoccureport(body []byte) ([]models.TjCorpoccureport, error) {
	var info = models.ReportDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	return reportModel.GetCorpoccureport(&info)
}

// InsertCorpoccureport ... insert
func InsertCorpoccureport(body []byte) (*models.TjCorpoccureport, error) {
	var info = models.TjCorpoccureport{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return reportModel.InsertCorpoccureport(&info)
}

// UpdateCorpoccureport ... Update
func UpdateCorpoccureport(body []byte) error {
	var info = models.TjCorpoccureport{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return reportModel.UpdateCorpoccureport(&info)
}

// DeleteCorpoccureport ... Update
func DeleteCorpoccureport(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return reportModel.DeleteCorpoccureport(id)
}

// ************************* Corpoccureport END **************************

// *************************** CorpoccureportFc BEGIN ******************************

// QueryCorpoccureportFc ... Query
func QueryCorpoccureportFc(body []byte) (*[]models.TjCorpoccureportFc, error) {
	var info = models.ReportinfoDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	return reportModel.GetCorpoccureportFc(&info)
}

// InsertCorpoccureportFc ... insert
func InsertCorpoccureportFc(body []byte) (*models.TjCorpoccureportFc, error) {
	var info = models.TjCorpoccureportFc{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return reportModel.InsertCorpoccureportFc(&info)
}

// UpdateCorpoccureportFc ... Update
func UpdateCorpoccureportFc(body []byte) error {
	var info = models.TjCorpoccureportFc{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return reportModel.UpdateCorpoccureportFc(&info)
}

// DeleteCorpoccureportFc ... delete
func DeleteCorpoccureportFc(body []byte) error {
	var info = models.KeysDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	if len(info.KeyIDs) <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return reportModel.DeleteCorpoccureportFc(info.KeyIDs)
}

// ************************* CorpoccureportFc END **************************

// *************************** CorpoccureportInfo BEGIN ******************************

// QueryCorpoccureportInfo ... Query
func QueryCorpoccureportInfo(body []byte) ([]models.TjCorpoccureportInfo, error) {
	var info = models.ReportinfoDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	return reportModel.GetCorpoccureportInfo(&info)
}

// InsertCorpoccureportInfo ... insert
func InsertCorpoccureportInfo(body []byte) (*[]models.TjCorpoccureportInfo, error) {
	var info []models.TjCorpoccureportInfo
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	// log.Printf("insert :%+v\n", info)
	var testids []string
	for _, val := range info {
		testids = append(testids, val.TjTestID)
	}
	err = reportModel.DeleteCorpoccureportInfos(testids)
	if err != nil {
		return nil, err
	}
	return reportModel.InsertCorpoccureportInfo(&info)
}

// UpdateCorpoccureportInfo ... Update
func UpdateCorpoccureportInfo(body []byte) error {
	var info = models.TjCorpoccureportInfo{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return reportModel.UpdateCorpoccureportInfo(&info)
}

// DeleteCorpoccureportInfo ... Update
func DeleteCorpoccureportInfo(rptid string) error {

	if rptid == "" {
		return errors.New("删除条件为空，不能删除")
	}
	return reportModel.DeleteCorpoccureportInfo(rptid)
}

// ************************* CorpoccureportInfo END **************************
