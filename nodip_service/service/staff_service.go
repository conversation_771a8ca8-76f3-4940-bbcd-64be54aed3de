package service

// "github.com/jinzhu/gorm"
import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var staffModel = new(models.StaffadminModel)

// *************************** Staffadmin BEGIN ******************************

// FindByStaffNo ... FindByStaffNo
func FindByStaffNo(uno string) (*models.TjStaffadmin, error) {
	// var staff = models.TjStaffadmin{}
	return staffmodel.FindByNo(uno)
}

// QueryStaffadmins ... Query
func QueryStaffadmins(gid int) (*[]models.TjStaffadmin, error) {
	// var info = models.ItemrefDTO{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	return nil, err
	// }
	return staffModel.GetStaffadmins(gid)
}

// InsertStaffadmin ... insert
func InsertStaffadmin(body []byte) (*models.TjStaffadmin, error) {
	var info = models.TjStaffadmin{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return staffModel.InsertStaffadmin(&info)
}

// UpdateStaffadmin ... Update
func UpdateStaffadmin(body []byte) error {
	var info = models.TjStaffadmin{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return err
	}

	log.Printf("update :%+v\n", info)
	return staffModel.UpdateStaffadmin(&info)
}

// DeleteStaffadmin ... Update
func DeleteStaffadmin(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return staffModel.DeleteStaffadmin(id)
}

// ************************* Staffadmin END **************************

// *************************** Staffright BEGIN ******************************

// QueryStaffright ... Query
func QueryStaffright(pid int) (*[]models.TjStaffright, error) {
	// var info = models.ItemrefDTO{}
	// err := json.Unmarshal(body, &info)
	// if err != nil {
	// 	return nil, err
	// }
	return staffModel.GetStaffrights(pid)
}

// InsertStaffright ... insert
func InsertStaffright(body []byte) (*[]models.TjStaffright, error) {
	var info []models.TjStaffright
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return staffModel.InsertStaffrights(&info)
}

// // UpdateStaffright ... Update
// func UpdateStaffright(body []byte) error {
// 	var info = models.TjStaffright{}
// 	err := json.Unmarshal(body, &info)
// 	if err != nil {
// 		return err
// 	}

// 	log.Printf("update :%+v\n", info)
// 	return staffModel.UpdateStaffright(&info)
// }

// DeleteStaffright ... Update
func DeleteStaffright(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return staffModel.DeleteStaffright(id)
}

// ************************* Staffright END **************************

// *************************** Staffdept BEGIN ******************************

// QueryStaffdept ... Query
func QueryStaffdept(body []byte) (*[]models.TjStaffdept, error) {
	var info = models.StaffDeptDTO{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}
	return staffModel.GetStaffdept(&info)
}

// InsertStaffdept ... insert
func InsertStaffdept(body []byte) (*models.TjStaffdept, error) {
	var info = models.TjStaffdept{}
	err := json.Unmarshal(body, &info)
	if err != nil {
		logger.Log.Errorf("错误信息： %+v", err)
		return nil, err
	}

	log.Printf("insert :%+v\n", info)
	return staffModel.InsertStaffdept(&info)
}

// // UpdateStaffdept ... Update
// func UpdateStaffdept(body []byte) error {
// 	var info = models.TjStaffdept{}
// 	err := json.Unmarshal(body, &info)
// 	if err != nil {
// 		return err
// 	}

// 	log.Printf("update :%+v\n", info)
// 	return staffModel.UpdateStaffdept(&info)
// }

// DeleteStaffdept ... Update
func DeleteStaffdept(id int) error {

	if id <= 0 {
		return errors.New("删除条件为空，不能删除")
	}
	return staffModel.DeleteStaffdept(id)
}

// ************************* Staffdept END **************************
