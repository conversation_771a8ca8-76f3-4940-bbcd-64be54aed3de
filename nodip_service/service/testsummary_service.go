package service

import (
	"datacontroller/models"
	"encoding/json"
	"errors"
	"log"
	"utility/logger"
	// "nodip_ego/api/models"
)

var tsmodel = new(models.TestSummaryModel)

// QueryTestSummary ...
func QueryTestSummary(body []byte) (*[]models.TjTestsummary, error) {
	var dat = models.TsQueryDTO{}
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Errorf("错误信息: %+v", err)
		return nil, err
	}

	// log.Printf("%+v\n", testids)
	// log.Printf("%+v\n", pids)

	logger.Log.Infof("QueryTestSummary condition: %+v", dat)
	return tsmodel.GetTestsummaries(&dat)
}

// InsertTestSummary ...
func InsertTestSummary(body []byte) (*[]models.TjTestsummary, error) {
	var dat []models.TjTestsummary
	err := json.Unmarshal(body, &dat)
	if err != nil {
		logger.Log.Errorf("错误信息: %+v", err)
		return nil, err
	}

	log.Printf("create test summary: %+v\n", &dat)
	// log.Printf("%+v\n", pids)

	logger.Log.Infof("InsertTestSummary: %+v", dat)

	return tsmodel.CreateTestSummary(&dat)
}

// UpdateTestSummaries ...
func UpdateTestSummaries(body []byte) (*[]models.TjTestsummary, error) {
	var infos []models.TjTestsummary
	err := json.Unmarshal(body, &infos)
	if err != nil {
		logger.Log.Errorf("错误信息:%+v", err)
		return nil, err
	}
	if len(infos) <= 0 {
		return nil, errors.New("没有数据，不能保存testsummary")
	}

	logger.Log.Infof("UpdateTestSummaries condition: %+v", infos)

	insertSumms := make([]models.TjTestsummary, 0)
	UpdateSumms := make([]models.TjTestsummary, 0)
	testid := infos[0].TjTestid
	existedSums, err := tsmodel.GetTestsummary(testid, "") //查找该体检者所有已经存在的科室小结信息
	if err != nil {
		logger.Log.Errorf("更新总检数据错误:%+v", err)
	}

	if len(*existedSums) <= 0 { //如果不存在，则全部插入
		return tsmodel.CreateTestSummary(&infos)
	}
	for _, v := range infos {
		retv := FindTestSummary(*existedSums, &v)
		if retv == nil {
			v.ID = 0
			insertSumms = append(insertSumms, v) //insert new
		} else {
			v.ID = (*retv).ID
			UpdateSumms = append(UpdateSumms, v)
		}
	}
	log.Printf("需要插入的科室小结信息:%+v\n", insertSumms)
	log.Printf("需要更新的科室小结信息:%+v\n", UpdateSumms)
	if len(insertSumms) > 0 {
		tsmodel.CreateTestSummary(&insertSumms)
	}
	if len(UpdateSumms) > 0 {
		tsmodel.UpdateTestSummary(&UpdateSumms)
	}
	var retInfos = make([]models.TjTestsummary, 0)
	retInfos = append(retInfos, insertSumms...)
	// for _, r := range insertSumms {
	// 	retInfos = append(retInfos, r)
	// }
	retInfos = append(retInfos, UpdateSumms...)
	// for _, r := range UpdateSumms {
	// 	retInfos = append(retInfos, r)
	// }

	log.Printf("返回的结果,总数:%d,值:%+v\n", len(retInfos), retInfos)
	return &retInfos, nil
}

// DeleteTestSummary ...
func DeleteTestSummary(body []byte) error {
	var dto = models.TsDeleteDTO{}
	err := json.Unmarshal(body, &dto)
	if err != nil {
		logger.Log.Errorf("错误信息: %+v", err)
		return err
	}
	if dto.Testid == "" {
		return errors.New("体检编号为空，不能删除")
	}
	return tsmodel.DeleteTestSummary(dto.Testid, dto.Deptid)
}

//FindTestSummary ...
func FindTestSummary(infos []models.TjTestsummary, ev *models.TjTestsummary) *models.TjTestsummary {
	// finded := false
	for idx := range infos {
		if (*ev).TjTestid == (infos)[idx].TjTestid && (*ev).TjDeptid == infos[idx].TjDeptid {
			// find existed
			// finded = true
			return &infos[idx]
		}
	}
	return nil
}
