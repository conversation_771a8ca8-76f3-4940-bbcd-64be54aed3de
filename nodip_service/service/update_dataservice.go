package service

import (
	"datacontroller/models"
	"nodip_service/comdef"
	"strconv"
	"strings"
	"utility/logger"
)

func Updatedata() error {

	logger.Log.Infof("重新处理数据......")

	var patientModel = new(models.PatientModel)
	var testids []string
	// testids := []string{"50003204"}
	cadto := models.CaQueryDTO{
		Testids:  testids,
		Stdate:   "**********",
		Enddate:  "",
		CaStatus: -1,
		RetType:  -1,
	}
	ckall, err := models.GetCheckall(&cadto)
	if err != nil {
		return err
	}
	// ckall := *ckret

	for caidx := range ckall {
		//处理疾病信息
		if ckall[caidx].TjDiscode != "" {
			cadiscode := strings.Split(ckall[caidx].TjDiscode, ",")
			newdiscode := ""
			cadiscodelen := len(cadiscode)
			for lidx, disidx := range cadiscode {
				logger.Log.Infof("=====疾病代码======:%s", disidx)
				disid, err := strconv.Atoi(disidx)
				if err != nil {
					continue
				}
				if disid > 5000 {
					//从tj_diseases中查找疾病信息（根据discode)
					disinfo, err := Diseasemodel.GetDisease(disidx)
					if err != nil {
						continue
					}
					newdiscode += strconv.Itoa(disinfo.ID)
					if lidx != cadiscodelen-1 {
						newdiscode += ","
					}
					var meddisinfo models.TjDiseaseinfo
					meddisinfo.TjTestid = ckall[caidx].TjTestid
					meddisinfo.TjDisid = disinfo.ID
					meddisinfo.TjDiseasenum = disinfo.TjDiscode
					meddisinfo.TjDiseasename = disinfo.TjDisname
					meddisinfo.TjSuggestion = disinfo.TjContent
					meddisinfo.TjDeptid = "0035"
					meddisinfo.TjIsdisease = 1
					meddisinfo.TjIsoccu = disinfo.TjOccudiseaseflag
					meddisinfo.TjTypeid = disinfo.TjTypeid
					meddisinfo.TjOpinion = disinfo.TjOpinion
					meddisinfo.TjShoworder = disinfo.ID
					logger.Log.Infof("patient diseases info:%+v", meddisinfo)
					patientModel.SavePatientDiseases(&meddisinfo)
				}
			}
			if newdiscode != "" {
				ckall[caidx].TjDiscode = newdiscode
				models.UpdateCheckAll(&ckall[caidx])
			}
		}
		var keys models.KeysDTO
		keys.KeyIDs = append(keys.KeyIDs, ckall[caidx].TjTestid)
		hds, err := patient.GetPatientHazards(keys.KeyIDs)
		if err != nil {
			logger.Log.Infof("error:%+v", err)
			continue
		}
		if ckall[caidx].TjHazardcode != "" && (ckall[caidx].TjTypeid == comdef.Forbidden || ckall[caidx].TjTypeid == comdef.Suspected) { //毒害因素非空
			hazards := strings.Split(ckall[caidx].TjHazardcode, ",")
			for _, hdid := range hazards {
				hdid_int, err := strconv.Atoi(hdid)
				if err != nil {
					continue
				}
				for idx := range hds {
					if hds[idx].TjTestid == ckall[caidx].TjTestid {
						if hds[idx].TjHid == hdid_int {
							hds[idx].TjTypeid = ckall[caidx].TjTypeid
							hds[idx].TjDiseases = ckall[caidx].TjDiscode
							patient.UpdatePatientHazards(&hds[idx])
						}
					}
				}
			}
		}

		if ckall[caidx].TjTypeid == comdef.OtherDisease {
			//其它疾病或异常
			if ckall[caidx].TjHazardcode != "" { //毒害因素代码非空
				//
				discode := ckall[caidx].TjDiscode
				if discode == "" {
					//query from patient disease
					dto := models.PatientDiseaseDTO{
						Testid: ckall[caidx].TjTestid,
						Deptid: "",
						Disid:  0,
						ID:     0,
					}
					diseases, err := patient.GetPatientDiseases(&dto)
					logger.Log.Infof("病人疾病信息：%v", diseases)
					if err == nil {
						for _, val := range *diseases {
							discode += strconv.Itoa(val.TjDisid) + ","
						}
					}
				}
				hazards := strings.Split(ckall[caidx].TjHazardcode, ",")
				for _, hdid := range hazards {
					hdid_int, err := strconv.Atoi(hdid)
					if err != nil {
						continue
					}
					for idx := range hds {
						if hds[idx].TjTestid == ckall[caidx].TjTestid {
							if hds[idx].TjHid == hdid_int {
								hds[idx].TjTypeid = ckall[caidx].TjTypeid
								hds[idx].TjDiseases = discode
								patient.UpdatePatientHazards(&hds[idx])
							}
						}
					}
				}
			} else {
				discode := ckall[caidx].TjDiscode
				if discode == "" {
					//query from patient disease
					dto := models.PatientDiseaseDTO{
						Testid: ckall[caidx].TjTestid,
						Deptid: "",
						Disid:  0,
						ID:     0,
					}
					diseases, err := patient.GetPatientDiseases(&dto)
					logger.Log.Infof("病人疾病信息：%v", diseases)
					if err == nil {
						for _, val := range *diseases {
							discode += strconv.Itoa(val.TjDisid) + ","
						}
					}
				}
				for idx := range hds {
					if hds[idx].TjTestid == ckall[caidx].TjTestid {
						hds[idx].TjTypeid = ckall[caidx].TjTypeid
						hds[idx].TjDiseases = discode
						patient.UpdatePatientHazards(&hds[idx])
					}
				}
			}
		}

		if ckall[caidx].TjTypeid == comdef.Recheck {
			if ckall[caidx].TjHazardcode != "" { //毒害因素代码非空
				hazards := strings.Split(ckall[caidx].TjHazardcode, ",")
				for _, hdid := range hazards {
					hdid_int, err := strconv.Atoi(hdid)
					if err != nil {
						continue
					}
					for idx := range hds {
						if hds[idx].TjTestid == ckall[caidx].TjTestid {
							if hds[idx].TjHid == hdid_int {
								hds[idx].TjTypeid = ckall[caidx].TjTypeid
								hds[idx].TjDiseases = ckall[caidx].TjHazardcode
								patient.UpdatePatientHazards(&hds[idx])
							}
						}
					}
				}
			}
		}
	}

	return nil
}
