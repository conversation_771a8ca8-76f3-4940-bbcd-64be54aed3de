// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// DO NOT EDIT: generated by unioffice ECMA-376 generator
//
// Use of this source code is governed by the terms of the Affero GNU General
// Public License version 3.0 as published by the Free Software Foundation and
// appearing in the file LICENSE included in the packaging of this file. A
// commercial license can be purchased via https://unidoc.io website.

package chart

import (
	"encoding/xml"

	"github.com/unidoc/unioffice"
)

type CT_UpDownBars struct {
	GapWidth *CT_GapAmount
	UpBars   *CT_UpDownBar
	DownBars *CT_UpDownBar
	ExtLst   *CT_ExtensionList
}

func NewCT_UpDownBars() *CT_UpDownBars {
	ret := &CT_UpDownBars{}
	return ret
}

func (m *CT_UpDownBars) MarshalXML(e *xml.Encoder, start xml.StartElement) error {
	e.EncodeToken(start)
	if m.GapWidth != nil {
		segapWidth := xml.StartElement{Name: xml.Name{Local: "c:gapWidth"}}
		e.EncodeElement(m.GapWidth, segapWidth)
	}
	if m.UpBars != nil {
		seupBars := xml.StartElement{Name: xml.Name{Local: "c:upBars"}}
		e.EncodeElement(m.UpBars, seupBars)
	}
	if m.DownBars != nil {
		sedownBars := xml.StartElement{Name: xml.Name{Local: "c:downBars"}}
		e.EncodeElement(m.DownBars, sedownBars)
	}
	if m.ExtLst != nil {
		seextLst := xml.StartElement{Name: xml.Name{Local: "c:extLst"}}
		e.EncodeElement(m.ExtLst, seextLst)
	}
	e.EncodeToken(xml.EndElement{Name: start.Name})
	return nil
}

func (m *CT_UpDownBars) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	// initialize to default
lCT_UpDownBars:
	for {
		tok, err := d.Token()
		if err != nil {
			return err
		}
		switch el := tok.(type) {
		case xml.StartElement:
			switch el.Name {
			case xml.Name{Space: "http://schemas.openxmlformats.org/drawingml/2006/chart", Local: "gapWidth"},
				xml.Name{Space: "http://purl.oclc.org/ooxml/drawingml/chart", Local: "gapWidth"}:
				m.GapWidth = NewCT_GapAmount()
				if err := d.DecodeElement(m.GapWidth, &el); err != nil {
					return err
				}
			case xml.Name{Space: "http://schemas.openxmlformats.org/drawingml/2006/chart", Local: "upBars"},
				xml.Name{Space: "http://purl.oclc.org/ooxml/drawingml/chart", Local: "upBars"}:
				m.UpBars = NewCT_UpDownBar()
				if err := d.DecodeElement(m.UpBars, &el); err != nil {
					return err
				}
			case xml.Name{Space: "http://schemas.openxmlformats.org/drawingml/2006/chart", Local: "downBars"},
				xml.Name{Space: "http://purl.oclc.org/ooxml/drawingml/chart", Local: "downBars"}:
				m.DownBars = NewCT_UpDownBar()
				if err := d.DecodeElement(m.DownBars, &el); err != nil {
					return err
				}
			case xml.Name{Space: "http://schemas.openxmlformats.org/drawingml/2006/chart", Local: "extLst"},
				xml.Name{Space: "http://purl.oclc.org/ooxml/drawingml/chart", Local: "extLst"}:
				m.ExtLst = NewCT_ExtensionList()
				if err := d.DecodeElement(m.ExtLst, &el); err != nil {
					return err
				}
			default:
				unioffice.Log("skipping unsupported element on CT_UpDownBars %v", el.Name)
				if err := d.Skip(); err != nil {
					return err
				}
			}
		case xml.EndElement:
			break lCT_UpDownBars
		case xml.CharData:
		}
	}
	return nil
}

// Validate validates the CT_UpDownBars and its children
func (m *CT_UpDownBars) Validate() error {
	return m.ValidateWithPath("CT_UpDownBars")
}

// ValidateWithPath validates the CT_UpDownBars and its children, prefixing error messages with path
func (m *CT_UpDownBars) ValidateWithPath(path string) error {
	if m.GapWidth != nil {
		if err := m.GapWidth.ValidateWithPath(path + "/GapWidth"); err != nil {
			return err
		}
	}
	if m.UpBars != nil {
		if err := m.UpBars.ValidateWithPath(path + "/UpBars"); err != nil {
			return err
		}
	}
	if m.DownBars != nil {
		if err := m.DownBars.ValidateWithPath(path + "/DownBars"); err != nil {
			return err
		}
	}
	if m.ExtLst != nil {
		if err := m.ExtLst.ValidateWithPath(path + "/ExtLst"); err != nil {
			return err
		}
	}
	return nil
}
