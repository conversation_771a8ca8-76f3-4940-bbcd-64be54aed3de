// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// DO NOT EDIT: generated by unioffice ECMA-376 generator
//
// Use of this source code is governed by the terms of the Affero GNU General
// Public License version 3.0 as published by the Free Software Foundation and
// appearing in the file LICENSE included in the packaging of this file. A
// commercial license can be purchased via https://unidoc.io website.

package chartDrawing_test

import (
	"encoding/xml"
	"testing"

	"github.com/unidoc/unioffice/schema/soo/dml/chartDrawing"
)

func TestCT_GraphicFrameNonVisualConstructor(t *testing.T) {
	v := chartDrawing.NewCT_GraphicFrameNonVisual()
	if v == nil {
		t.Errorf("chartDrawing.NewCT_GraphicFrameNonVisual must return a non-nil value")
	}
	if err := v.Validate(); err != nil {
		t.Errorf("newly constructed chartDrawing.CT_GraphicFrameNonVisual should validate: %s", err)
	}
}

func TestCT_GraphicFrameNonVisualMarshalUnmarshal(t *testing.T) {
	v := chartDrawing.NewCT_GraphicFrameNonVisual()
	buf, _ := xml.Marshal(v)
	v2 := chartDrawing.NewCT_GraphicFrameNonVisual()
	xml.Unmarshal(buf, v2)
}
